{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'წინა' eeee p'-ზე'\",\n  yesterday: \"'გუშინ' p'-ზე'\",\n  today: \"'დღეს' p'-ზე'\",\n  tomorrow: \"'ხვალ' p'-ზე'\",\n  nextWeek: \"'შემდეგი' eeee p'-ზე'\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ka/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'წინა' eeee p'-ზე'\",\n  yesterday: \"'გუშინ' p'-ზე'\",\n  today: \"'დღეს' p'-ზე'\",\n  tomorrow: \"'ხვალ' p'-ზე'\",\n  nextWeek: \"'შემდეგი' eeee p'-ზე'\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,uBAAuB;EACjCC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}