{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M8.71 7.71a.9959.9959 0 0 1 0-1.41l2.59-2.59c.39-.39 1.02-.39 1.41 0L15.3 6.3c.39.39.39 1.02 0 1.41-.39.39-1.02.39-1.41 0L13 6.83v5.1c0 1.06.42 2.08 1.17 2.83l4.12 4.12c.39.39.39 1.02 0 1.41s-1.02.39-1.41 0L12 15.41l-4.88 4.88c-.39.39-1.02.39-1.41 0a.9959.9959 0 0 1 0-1.41l4.12-4.12c.75-.75 1.17-1.77 1.17-2.83v-5.1l-.88.88c-.39.39-1.02.39-1.41 0\"\n}), 'MergeRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/MergeRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M8.71 7.71a.9959.9959 0 0 1 0-1.41l2.59-2.59c.39-.39 1.02-.39 1.41 0L15.3 6.3c.39.39.39 1.02 0 1.41-.39.39-1.02.39-1.41 0L13 6.83v5.1c0 1.06.42 2.08 1.17 2.83l4.12 4.12c.39.39.39 1.02 0 1.41s-1.02.39-1.41 0L12 15.41l-4.88 4.88c-.39.39-1.02.39-1.41 0a.9959.9959 0 0 1 0-1.41l4.12-4.12c.75-.75 1.17-1.77 1.17-2.83v-5.1l-.88.88c-.39.39-1.02.39-1.41 0\"\n}), 'MergeRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}