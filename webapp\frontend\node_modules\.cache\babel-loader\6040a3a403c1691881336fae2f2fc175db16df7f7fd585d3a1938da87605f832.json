{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\GestioneComande.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Save as SaveIcon, Search as SearchIcon, Print as PrintIcon, ViewList as ViewListIcon, Assignment as AssignmentIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GestioneComande = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [comande, setComande] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_comanda: '',\n    data_comanda: '',\n    tipo_comanda: 'POSA',\n    id_cavo: '',\n    operatore: '',\n    note: ''\n  });\n\n  // Carica le comande\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle comande');\n      console.error('Errore nel caricamento delle comande:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi disponibili\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente\n  useEffect(() => {\n    loadComande();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaComande') {\n      loadComande();\n    } else if (option === 'creaComanda') {\n      loadCavi();\n      setDialogType('creaComanda');\n      // Imposta la data di oggi come default\n      const today = new Date().toISOString().split('T')[0];\n      setFormData({\n        ...formData,\n        data_comanda: today\n      });\n      setOpenDialog(true);\n    } else if (option === 'modificaComanda') {\n      loadComande();\n      setDialogType('selezionaComanda');\n      setOpenDialog(true);\n    } else if (option === 'eliminaComanda') {\n      loadComande();\n      setDialogType('eliminaComanda');\n      setOpenDialog(true);\n    } else if (option === 'stampaComanda') {\n      loadComande();\n      setDialogType('stampaComanda');\n      setOpenDialog(true);\n    } else if (option === 'assegnaComanda') {\n      loadComande();\n      loadCavi();\n      setDialogType('selezionaCavoComanda');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setSelectedCavo(null);\n    setFormData({\n      numero_comanda: '',\n      data_comanda: '',\n      tipo_comanda: 'POSA',\n      id_cavo: '',\n      operatore: '',\n      note: ''\n    });\n  };\n\n  // Gestisce la selezione di una comanda\n  const handleComandaSelect = comanda => {\n    setSelectedComanda(comanda);\n    if (dialogType === 'selezionaComanda') {\n      setDialogType('modificaComanda');\n      setFormData({\n        numero_comanda: comanda.numero_comanda,\n        data_comanda: comanda.data_comanda.split('T')[0],\n        tipo_comanda: comanda.tipo_comanda,\n        id_cavo: comanda.id_cavo || '',\n        operatore: comanda.operatore || '',\n        note: comanda.note || ''\n      });\n    } else if (dialogType === 'stampaComanda') {\n      handleStampaComanda(comanda.id_comanda);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    setFormData({\n      ...formData,\n      id_cavo: cavo.id_cavo\n    });\n    if (dialogType === 'selezionaCavoComanda') {\n      setDialogType('selezionaComandaPerCavo');\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di una comanda\n  const handleCreaComanda = async () => {\n    try {\n      if (!formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n      setLoading(true);\n      await comandeService.createComanda(cantiereId, formData);\n      onSuccess('Comanda creata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nella creazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la modifica di una comanda\n  const handleModificaComanda = async () => {\n    try {\n      if (!formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n      setLoading(true);\n      await comandeService.updateComanda(cantiereId, selectedComanda.id_comanda, formData);\n      onSuccess('Comanda modificata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nella modifica della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella modifica della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione di una comanda\n  const handleEliminaComanda = async () => {\n    try {\n      if (!selectedComanda) {\n        onError('Seleziona una comanda da eliminare');\n        return;\n      }\n      setLoading(true);\n      await comandeService.deleteComanda(cantiereId, selectedComanda.id_comanda);\n      onSuccess('Comanda eliminata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nell\\'eliminazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'eliminazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'assegnazione di una comanda a un cavo\n  const handleAssegnaComanda = async () => {\n    try {\n      if (!selectedCavo || !selectedComanda) {\n        onError('Seleziona un cavo e una comanda');\n        return;\n      }\n      setLoading(true);\n      await comandeService.assignComandaToCavo(cantiereId, selectedComanda.id_comanda, selectedCavo.id_cavo);\n      onSuccess('Comanda assegnata al cavo con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nell\\'assegnazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'assegnazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la stampa di una comanda\n  const handleStampaComanda = async idComanda => {\n    try {\n      setLoading(true);\n      const response = await comandeService.printComanda(cantiereId, idComanda);\n\n      // Apri il PDF in una nuova finestra\n      window.open(response.file_url, '_blank');\n      onSuccess('PDF della comanda generato con successo');\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore nella generazione del PDF della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del PDF della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le comande in formato tabella\n  const renderComandeTable = () => {\n    if (comande.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessuna comanda trovata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Numero\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Operatore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: comande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.id_comanda\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.numero_comanda\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: new Date(comanda.data_comanda).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.tipo_comanda\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.id_cavo || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.operatore || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => {\n                  setSelectedComanda(comanda);\n                  setDialogType('modificaComanda');\n                  setFormData({\n                    numero_comanda: comanda.numero_comanda,\n                    data_comanda: comanda.data_comanda.split('T')[0],\n                    tipo_comanda: comanda.tipo_comanda,\n                    id_cavo: comanda.id_cavo || '',\n                    operatore: comanda.operatore || '',\n                    note: comanda.note || ''\n                  });\n                  setOpenDialog(true);\n                },\n                children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleStampaComanda(comanda.id_comanda),\n                children: /*#__PURE__*/_jsxDEV(PrintIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => {\n                  setSelectedComanda(comanda);\n                  setDialogType('eliminaComanda');\n                  setOpenDialog(true);\n                },\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this)]\n          }, comanda.id_comanda, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaComanda' || dialogType === 'modificaComanda') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaComanda' ? 'Crea Nuova Comanda' : 'Modifica Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"numero_comanda\",\n                label: \"Numero Comanda\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.numero_comanda,\n                onChange: handleFormChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"data_comanda\",\n                label: \"Data Comanda\",\n                type: \"date\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.data_comanda,\n                onChange: handleFormChange,\n                InputLabelProps: {\n                  shrink: true\n                },\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                variant: \"outlined\",\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Tipo Comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"tipo_comanda\",\n                  value: formData.tipo_comanda,\n                  onChange: handleFormChange,\n                  label: \"Tipo Comanda\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"POSA\",\n                    children: \"POSA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"PARTENZA\",\n                    children: \"PARTENZA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ARRIVO\",\n                    children: \"ARRIVO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_cavo\",\n                label: \"ID Cavo (opzionale)\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_cavo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"operatore\",\n                label: \"Operatore\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.operatore,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"note\",\n                label: \"Note\",\n                fullWidth: true,\n                multiline: true,\n                rows: 3,\n                variant: \"outlined\",\n                value: formData.note,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: dialogType === 'creaComanda' ? handleCreaComanda : handleModificaComanda,\n            disabled: loading || !formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaComanda') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Comanda da Modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this) : comande.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: comande.map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleComandaSelect(comanda),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `${comanda.numero_comanda} - ${comanda.tipo_comanda}`,\n                secondary: `Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo || 'Non assegnato'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 21\n              }, this)\n            }, comanda.id_comanda, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaComanda') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedComanda ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 17\n          }, this) : comande.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: comande.map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedComanda(comanda),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `${comanda.numero_comanda} - ${comanda.tipo_comanda}`,\n                secondary: `Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 23\n              }, this)\n            }, comanda.id_comanda, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la comanda \", selectedComanda.numero_comanda, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), selectedComanda && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleEliminaComanda,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'stampaComanda') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Comanda da Stampare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this) : comande.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: comande.map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleComandaSelect(comanda),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `${comanda.numero_comanda} - ${comanda.tipo_comanda}`,\n                secondary: `Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo || 'Non assegnato'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 21\n              }, this)\n            }, comanda.id_comanda, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavoComanda') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Cavo per Assegnare Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: cavo.id_cavo,\n                secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 21\n              }, this)\n            }, cavo.id_cavo, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaComandaPerCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: [\"Seleziona Comanda da Assegnare al Cavo \", selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this) : comande.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: comande.map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedComanda(comanda),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `${comanda.numero_comanda} - ${comanda.tipo_comanda}`,\n                secondary: `Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 21\n              }, this)\n            }, comanda.id_comanda, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), selectedComanda && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleAssegnaComanda,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 71\n            }, this),\n            children: \"Assegna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaComande' && !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Comande\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 13\n      }, this) : renderComandeTable()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 660,\n      columnNumber: 9\n    }, this) : !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: !selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [selectedOption === 'creaComanda' && 'Crea nuova comanda', selectedOption === 'modificaComanda' && 'Modifica comanda', selectedOption === 'eliminaComanda' && 'Elimina comanda', selectedOption === 'stampaComanda' && 'Stampa comanda', selectedOption === 'assegnaComanda' && 'Assegna comanda a cavo']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 674,\n      columnNumber: 9\n    }, this) : null, renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 658,\n    columnNumber: 5\n  }, this);\n};\n_s(GestioneComande, \"5KxD2WdNuzr2gBYYKNGRLZj+ucc=\");\n_c = GestioneComande;\nexport default GestioneComande;\nvar _c;\n$RefreshReg$(_c, \"GestioneComande\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Search", "SearchIcon", "Print", "PrintIcon", "ViewList", "ViewListIcon", "Assignment", "AssignmentIcon", "comandeService", "caviService", "jsxDEV", "_jsxDEV", "GestioneComande", "cantiereId", "onSuccess", "onError", "_s", "loading", "setLoading", "comande", "setComande", "cavi", "<PERSON><PERSON><PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedComanda", "setSelectedComanda", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "numero_comanda", "data_comanda", "tipo_comanda", "id_cavo", "operatore", "note", "loadComande", "data", "getComande", "error", "console", "loadCavi", "get<PERSON><PERSON>", "handleOptionSelect", "option", "today", "Date", "toISOString", "split", "handleCloseDialog", "handleComandaSelect", "comanda", "handleStampaComanda", "id_comanda", "handleCavoSelect", "cavo", "handleFormChange", "e", "name", "value", "target", "handleCreaComanda", "createComanda", "message", "handleModificaComanda", "updateComanda", "handleEliminaComanda", "deleteComanda", "handleAssegnaComanda", "assignComandaToCavo", "idComanda", "response", "printComanda", "window", "open", "file_url", "renderComandeTable", "length", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "size", "map", "toLocaleDateString", "onClick", "fontSize", "color", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "container", "spacing", "sx", "mt", "item", "xs", "sm", "label", "variant", "onChange", "required", "type", "InputLabelProps", "shrink", "multiline", "rows", "disabled", "startIcon", "button", "primary", "secondary", "mb", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "p", "gutterBottom", "display", "justifyContent", "my", "minHeight", "alignItems", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/GestioneComande.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Search as SearchIcon,\n  Print as PrintIcon,\n  ViewList as ViewListIcon,\n  Assignment as AssignmentIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\n\nconst GestioneComande = ({ cantiereId, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [comande, setComande] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_comanda: '',\n    data_comanda: '',\n    tipo_comanda: 'POSA',\n    id_cavo: '',\n    operatore: '',\n    note: ''\n  });\n\n  // Carica le comande\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle comande');\n      console.error('Errore nel caricamento delle comande:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi disponibili\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente\n  useEffect(() => {\n    loadComande();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaComande') {\n      loadComande();\n    } else if (option === 'creaComanda') {\n      loadCavi();\n      setDialogType('creaComanda');\n      // Imposta la data di oggi come default\n      const today = new Date().toISOString().split('T')[0];\n      setFormData({\n        ...formData,\n        data_comanda: today\n      });\n      setOpenDialog(true);\n    } else if (option === 'modificaComanda') {\n      loadComande();\n      setDialogType('selezionaComanda');\n      setOpenDialog(true);\n    } else if (option === 'eliminaComanda') {\n      loadComande();\n      setDialogType('eliminaComanda');\n      setOpenDialog(true);\n    } else if (option === 'stampaComanda') {\n      loadComande();\n      setDialogType('stampaComanda');\n      setOpenDialog(true);\n    } else if (option === 'assegnaComanda') {\n      loadComande();\n      loadCavi();\n      setDialogType('selezionaCavoComanda');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setSelectedCavo(null);\n    setFormData({\n      numero_comanda: '',\n      data_comanda: '',\n      tipo_comanda: 'POSA',\n      id_cavo: '',\n      operatore: '',\n      note: ''\n    });\n  };\n\n  // Gestisce la selezione di una comanda\n  const handleComandaSelect = (comanda) => {\n    setSelectedComanda(comanda);\n\n    if (dialogType === 'selezionaComanda') {\n      setDialogType('modificaComanda');\n      setFormData({\n        numero_comanda: comanda.numero_comanda,\n        data_comanda: comanda.data_comanda.split('T')[0],\n        tipo_comanda: comanda.tipo_comanda,\n        id_cavo: comanda.id_cavo || '',\n        operatore: comanda.operatore || '',\n        note: comanda.note || ''\n      });\n    } else if (dialogType === 'stampaComanda') {\n      handleStampaComanda(comanda.id_comanda);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    setFormData({\n      ...formData,\n      id_cavo: cavo.id_cavo\n    });\n\n    if (dialogType === 'selezionaCavoComanda') {\n      setDialogType('selezionaComandaPerCavo');\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di una comanda\n  const handleCreaComanda = async () => {\n    try {\n      if (!formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n\n      setLoading(true);\n      await comandeService.createComanda(cantiereId, formData);\n      onSuccess('Comanda creata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nella creazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la modifica di una comanda\n  const handleModificaComanda = async () => {\n    try {\n      if (!formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n\n      setLoading(true);\n      await comandeService.updateComanda(cantiereId, selectedComanda.id_comanda, formData);\n      onSuccess('Comanda modificata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nella modifica della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella modifica della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione di una comanda\n  const handleEliminaComanda = async () => {\n    try {\n      if (!selectedComanda) {\n        onError('Seleziona una comanda da eliminare');\n        return;\n      }\n\n      setLoading(true);\n      await comandeService.deleteComanda(cantiereId, selectedComanda.id_comanda);\n      onSuccess('Comanda eliminata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nell\\'eliminazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'eliminazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'assegnazione di una comanda a un cavo\n  const handleAssegnaComanda = async () => {\n    try {\n      if (!selectedCavo || !selectedComanda) {\n        onError('Seleziona un cavo e una comanda');\n        return;\n      }\n\n      setLoading(true);\n      await comandeService.assignComandaToCavo(cantiereId, selectedComanda.id_comanda, selectedCavo.id_cavo);\n      onSuccess('Comanda assegnata al cavo con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nell\\'assegnazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'assegnazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la stampa di una comanda\n  const handleStampaComanda = async (idComanda) => {\n    try {\n      setLoading(true);\n      const response = await comandeService.printComanda(cantiereId, idComanda);\n\n      // Apri il PDF in una nuova finestra\n      window.open(response.file_url, '_blank');\n\n      onSuccess('PDF della comanda generato con successo');\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore nella generazione del PDF della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del PDF della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le comande in formato tabella\n  const renderComandeTable = () => {\n    if (comande.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessuna comanda trovata</Alert>\n      );\n    }\n\n    return (\n      <TableContainer component={Paper}>\n        <Table size=\"small\">\n          <TableHead>\n            <TableRow>\n              <TableCell>ID</TableCell>\n              <TableCell>Numero</TableCell>\n              <TableCell>Data</TableCell>\n              <TableCell>Tipo</TableCell>\n              <TableCell>Cavo</TableCell>\n              <TableCell>Operatore</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {comande.map((comanda) => (\n              <TableRow key={comanda.id_comanda}>\n                <TableCell>{comanda.id_comanda}</TableCell>\n                <TableCell>{comanda.numero_comanda}</TableCell>\n                <TableCell>{new Date(comanda.data_comanda).toLocaleDateString()}</TableCell>\n                <TableCell>{comanda.tipo_comanda}</TableCell>\n                <TableCell>{comanda.id_cavo || '-'}</TableCell>\n                <TableCell>{comanda.operatore || '-'}</TableCell>\n                <TableCell>\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => {\n                      setSelectedComanda(comanda);\n                      setDialogType('modificaComanda');\n                      setFormData({\n                        numero_comanda: comanda.numero_comanda,\n                        data_comanda: comanda.data_comanda.split('T')[0],\n                        tipo_comanda: comanda.tipo_comanda,\n                        id_cavo: comanda.id_cavo || '',\n                        operatore: comanda.operatore || '',\n                        note: comanda.note || ''\n                      });\n                      setOpenDialog(true);\n                    }}\n                  >\n                    <EditIcon fontSize=\"small\" />\n                  </IconButton>\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => handleStampaComanda(comanda.id_comanda)}\n                  >\n                    <PrintIcon fontSize=\"small\" />\n                  </IconButton>\n                  <IconButton\n                    size=\"small\"\n                    color=\"error\"\n                    onClick={() => {\n                      setSelectedComanda(comanda);\n                      setDialogType('eliminaComanda');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    <DeleteIcon fontSize=\"small\" />\n                  </IconButton>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaComanda' || dialogType === 'modificaComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaComanda' ? 'Crea Nuova Comanda' : 'Modifica Comanda'}\n          </DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_comanda\"\n                  label=\"Numero Comanda\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_comanda}\n                  onChange={handleFormChange}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_comanda\"\n                  label=\"Data Comanda\"\n                  type=\"date\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_comanda}\n                  onChange={handleFormChange}\n                  InputLabelProps={{ shrink: true }}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth variant=\"outlined\">\n                  <InputLabel>Tipo Comanda</InputLabel>\n                  <Select\n                    name=\"tipo_comanda\"\n                    value={formData.tipo_comanda}\n                    onChange={handleFormChange}\n                    label=\"Tipo Comanda\"\n                    required\n                  >\n                    <MenuItem value=\"POSA\">POSA</MenuItem>\n                    <MenuItem value=\"PARTENZA\">PARTENZA</MenuItem>\n                    <MenuItem value=\"ARRIVO\">ARRIVO</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo (opzionale)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"operatore\"\n                  label=\"Operatore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.operatore}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12}>\n                <TextField\n                  name=\"note\"\n                  label=\"Note\"\n                  fullWidth\n                  multiline\n                  rows={3}\n                  variant=\"outlined\"\n                  value={formData.note}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={dialogType === 'creaComanda' ? handleCreaComanda : handleModificaComanda}\n              disabled={loading || !formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Comanda da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : comande.length === 0 ? (\n              <Alert severity=\"info\">Nessuna comanda disponibile</Alert>\n            ) : (\n              <List>\n                {comande.map((comanda) => (\n                  <ListItem\n                    button\n                    key={comanda.id_comanda}\n                    onClick={() => handleComandaSelect(comanda)}\n                  >\n                    <ListItemText\n                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}\n                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo || 'Non assegnato'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Comanda</DialogTitle>\n          <DialogContent>\n            {!selectedComanda ? (\n              loading ? (\n                <CircularProgress />\n              ) : comande.length === 0 ? (\n                <Alert severity=\"info\">Nessuna comanda disponibile</Alert>\n              ) : (\n                <List>\n                  {comande.map((comanda) => (\n                    <ListItem\n                      button\n                      key={comanda.id_comanda}\n                      onClick={() => setSelectedComanda(comanda)}\n                    >\n                      <ListItemText\n                        primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}\n                        secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la comanda {selectedComanda.numero_comanda}?\n                </Alert>\n                <Typography variant=\"body1\">\n                  Questa operazione non può essere annullata.\n                </Typography>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedComanda && (\n              <Button\n                onClick={handleEliminaComanda}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'stampaComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Comanda da Stampare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : comande.length === 0 ? (\n              <Alert severity=\"info\">Nessuna comanda disponibile</Alert>\n            ) : (\n              <List>\n                {comande.map((comanda) => (\n                  <ListItem\n                    button\n                    key={comanda.id_comanda}\n                    onClick={() => handleComandaSelect(comanda)}\n                  >\n                    <ListItemText\n                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}\n                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo || 'Non assegnato'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavoComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Cavo per Assegnare Comanda</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : (\n              <List>\n                {cavi.map((cavo) => (\n                  <ListItem\n                    button\n                    key={cavo.id_cavo}\n                    onClick={() => handleCavoSelect(cavo)}\n                  >\n                    <ListItemText\n                      primary={cavo.id_cavo}\n                      secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaComandaPerCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Comanda da Assegnare al Cavo {selectedCavo?.id_cavo}</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : comande.length === 0 ? (\n              <Alert severity=\"info\">Nessuna comanda disponibile</Alert>\n            ) : (\n              <List>\n                {comande.map((comanda) => (\n                  <ListItem\n                    button\n                    key={comanda.id_comanda}\n                    onClick={() => setSelectedComanda(comanda)}\n                  >\n                    <ListItemText\n                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}\n                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedComanda && (\n              <Button\n                onClick={handleAssegnaComanda}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Assegna\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaComande' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Comande\n          </Typography>\n\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            renderComandeTable()\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaComanda' && 'Crea nuova comanda'}\n                {selectedOption === 'modificaComanda' && 'Modifica comanda'}\n                {selectedOption === 'eliminaComanda' && 'Elimina comanda'}\n                {selectedOption === 'stampaComanda' && 'Stampa comanda'}\n                {selectedOption === 'assegnaComanda' && 'Assegna comanda a cavo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default GestioneComande;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,QACL,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,eAAe,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+D,IAAI,EAAEC,OAAO,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiE,cAAc,EAAEC,iBAAiB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmE,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwB,IAAI,GAAG,MAAMlC,cAAc,CAACmC,UAAU,CAAC9B,UAAU,CAAC;MACxDO,UAAU,CAACsB,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd7B,OAAO,CAAC,sCAAsC,CAAC;MAC/C8B,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwB,IAAI,GAAG,MAAMjC,WAAW,CAACsC,OAAO,CAAClC,UAAU,CAAC;MAClDS,OAAO,CAACoB,IAAI,CAAC;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd7B,OAAO,CAAC,iCAAiC,CAAC;MAC1C8B,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA3D,SAAS,CAAC,MAAM;IACdkF,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC5B,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMmC,kBAAkB,GAAIC,MAAM,IAAK;IACrCzB,iBAAiB,CAACyB,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,mBAAmB,EAAE;MAClCR,WAAW,CAAC,CAAC;IACf,CAAC,MAAM,IAAIQ,MAAM,KAAK,aAAa,EAAE;MACnCH,QAAQ,CAAC,CAAC;MACVlB,aAAa,CAAC,aAAa,CAAC;MAC5B;MACA,MAAMsB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDnB,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXG,YAAY,EAAEc;MAChB,CAAC,CAAC;MACFxB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIuB,MAAM,KAAK,iBAAiB,EAAE;MACvCR,WAAW,CAAC,CAAC;MACbb,aAAa,CAAC,kBAAkB,CAAC;MACjCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIuB,MAAM,KAAK,gBAAgB,EAAE;MACtCR,WAAW,CAAC,CAAC;MACbb,aAAa,CAAC,gBAAgB,CAAC;MAC/BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIuB,MAAM,KAAK,eAAe,EAAE;MACrCR,WAAW,CAAC,CAAC;MACbb,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIuB,MAAM,KAAK,gBAAgB,EAAE;MACtCR,WAAW,CAAC,CAAC;MACbK,QAAQ,CAAC,CAAC;MACVlB,aAAa,CAAC,sBAAsB,CAAC;MACrCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM4B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B5B,aAAa,CAAC,KAAK,CAAC;IACpBI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMe,mBAAmB,GAAIC,OAAO,IAAK;IACvC1B,kBAAkB,CAAC0B,OAAO,CAAC;IAE3B,IAAI7B,UAAU,KAAK,kBAAkB,EAAE;MACrCC,aAAa,CAAC,iBAAiB,CAAC;MAChCM,WAAW,CAAC;QACVC,cAAc,EAAEqB,OAAO,CAACrB,cAAc;QACtCC,YAAY,EAAEoB,OAAO,CAACpB,YAAY,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChDhB,YAAY,EAAEmB,OAAO,CAACnB,YAAY;QAClCC,OAAO,EAAEkB,OAAO,CAAClB,OAAO,IAAI,EAAE;QAC9BC,SAAS,EAAEiB,OAAO,CAACjB,SAAS,IAAI,EAAE;QAClCC,IAAI,EAAEgB,OAAO,CAAChB,IAAI,IAAI;MACxB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIb,UAAU,KAAK,eAAe,EAAE;MACzC8B,mBAAmB,CAACD,OAAO,CAACE,UAAU,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,IAAI,IAAK;IACjC5B,eAAe,CAAC4B,IAAI,CAAC;IACrB1B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXK,OAAO,EAAEsB,IAAI,CAACtB;IAChB,CAAC,CAAC;IAEF,IAAIX,UAAU,KAAK,sBAAsB,EAAE;MACzCC,aAAa,CAAC,yBAAyB,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMiC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC/B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC8B,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,IAAI,CAACjC,QAAQ,CAACE,cAAc,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,EAAE;QAChFtB,OAAO,CAAC,mCAAmC,CAAC;QAC5C;MACF;MAEAG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMV,cAAc,CAAC2D,aAAa,CAACtD,UAAU,EAAEoB,QAAQ,CAAC;MACxDnB,SAAS,CAAC,6BAA6B,CAAC;MACxCwC,iBAAiB,CAAC,CAAC;MACnBb,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd7B,OAAO,CAAC,wCAAwC,IAAI6B,KAAK,CAACwB,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAC3FvB,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,IAAI,CAACpC,QAAQ,CAACE,cAAc,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,EAAE;QAChFtB,OAAO,CAAC,mCAAmC,CAAC;QAC5C;MACF;MAEAG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMV,cAAc,CAAC8D,aAAa,CAACzD,UAAU,EAAEgB,eAAe,CAAC6B,UAAU,EAAEzB,QAAQ,CAAC;MACpFnB,SAAS,CAAC,iCAAiC,CAAC;MAC5CwC,iBAAiB,CAAC,CAAC;MACnBb,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd7B,OAAO,CAAC,uCAAuC,IAAI6B,KAAK,CAACwB,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAC1FvB,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,IAAI,CAAC1C,eAAe,EAAE;QACpBd,OAAO,CAAC,oCAAoC,CAAC;QAC7C;MACF;MAEAG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMV,cAAc,CAACgE,aAAa,CAAC3D,UAAU,EAAEgB,eAAe,CAAC6B,UAAU,CAAC;MAC1E5C,SAAS,CAAC,gCAAgC,CAAC;MAC3CwC,iBAAiB,CAAC,CAAC;MACnBb,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd7B,OAAO,CAAC,2CAA2C,IAAI6B,KAAK,CAACwB,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAC9FvB,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAClE,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,IAAI,CAAC1C,YAAY,IAAI,CAACF,eAAe,EAAE;QACrCd,OAAO,CAAC,iCAAiC,CAAC;QAC1C;MACF;MAEAG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMV,cAAc,CAACkE,mBAAmB,CAAC7D,UAAU,EAAEgB,eAAe,CAAC6B,UAAU,EAAE3B,YAAY,CAACO,OAAO,CAAC;MACtGxB,SAAS,CAAC,wCAAwC,CAAC;MACnDwC,iBAAiB,CAAC,CAAC;MACnBb,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd7B,OAAO,CAAC,2CAA2C,IAAI6B,KAAK,CAACwB,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAC9FvB,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAClE,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuC,mBAAmB,GAAG,MAAOkB,SAAS,IAAK;IAC/C,IAAI;MACFzD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM0D,QAAQ,GAAG,MAAMpE,cAAc,CAACqE,YAAY,CAAChE,UAAU,EAAE8D,SAAS,CAAC;;MAEzE;MACAG,MAAM,CAACC,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE,QAAQ,CAAC;MAExClE,SAAS,CAAC,yCAAyC,CAAC;MACpDwC,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd7B,OAAO,CAAC,kDAAkD,IAAI6B,KAAK,CAACwB,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrGvB,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;IACzE,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+D,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI9D,OAAO,CAAC+D,MAAM,KAAK,CAAC,EAAE;MACxB,oBACEvE,OAAA,CAAC5B,KAAK;QAACoG,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE1D;IAEA,oBACE7E,OAAA,CAACvB,cAAc;MAACqG,SAAS,EAAE9H,KAAM;MAAAyH,QAAA,eAC/BzE,OAAA,CAAC1B,KAAK;QAACyG,IAAI,EAAC,OAAO;QAAAN,QAAA,gBACjBzE,OAAA,CAACtB,SAAS;UAAA+F,QAAA,eACRzE,OAAA,CAACrB,QAAQ;YAAA8F,QAAA,gBACPzE,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzB7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7B7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChC7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ7E,OAAA,CAACzB,SAAS;UAAAkG,QAAA,EACPjE,OAAO,CAACwE,GAAG,CAAEnC,OAAO,iBACnB7C,OAAA,CAACrB,QAAQ;YAAA8F,QAAA,gBACPzE,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAE5B,OAAO,CAACE;YAAU;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAE5B,OAAO,CAACrB;YAAc;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/C7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAE,IAAIjC,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACwD,kBAAkB,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5E7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAE5B,OAAO,CAACnB;YAAY;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7C7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAE5B,OAAO,CAAClB,OAAO,IAAI;YAAG;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/C7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,EAAE5B,OAAO,CAACjB,SAAS,IAAI;YAAG;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjD7E,OAAA,CAACxB,SAAS;cAAAiG,QAAA,gBACRzE,OAAA,CAACpB,UAAU;gBACTmG,IAAI,EAAC,OAAO;gBACZG,OAAO,EAAEA,CAAA,KAAM;kBACb/D,kBAAkB,CAAC0B,OAAO,CAAC;kBAC3B5B,aAAa,CAAC,iBAAiB,CAAC;kBAChCM,WAAW,CAAC;oBACVC,cAAc,EAAEqB,OAAO,CAACrB,cAAc;oBACtCC,YAAY,EAAEoB,OAAO,CAACpB,YAAY,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAChDhB,YAAY,EAAEmB,OAAO,CAACnB,YAAY;oBAClCC,OAAO,EAAEkB,OAAO,CAAClB,OAAO,IAAI,EAAE;oBAC9BC,SAAS,EAAEiB,OAAO,CAACjB,SAAS,IAAI,EAAE;oBAClCC,IAAI,EAAEgB,OAAO,CAAChB,IAAI,IAAI;kBACxB,CAAC,CAAC;kBACFd,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBAAA0D,QAAA,eAEFzE,OAAA,CAAChB,QAAQ;kBAACmG,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACb7E,OAAA,CAACpB,UAAU;gBACTmG,IAAI,EAAC,OAAO;gBACZG,OAAO,EAAEA,CAAA,KAAMpC,mBAAmB,CAACD,OAAO,CAACE,UAAU,CAAE;gBAAA0B,QAAA,eAEvDzE,OAAA,CAACR,SAAS;kBAAC2F,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACb7E,OAAA,CAACpB,UAAU;gBACTmG,IAAI,EAAC,OAAO;gBACZK,KAAK,EAAC,OAAO;gBACbF,OAAO,EAAEA,CAAA,KAAM;kBACb/D,kBAAkB,CAAC0B,OAAO,CAAC;kBAC3B5B,aAAa,CAAC,gBAAgB,CAAC;kBAC/BF,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBAAA0D,QAAA,eAEFzE,OAAA,CAACd,UAAU;kBAACiG,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GA3CChC,OAAO,CAACE,UAAU;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4CvB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAErB,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIrE,UAAU,KAAK,aAAa,IAAIA,UAAU,KAAK,iBAAiB,EAAE;MACpE,oBACEhB,OAAA,CAAC3C,MAAM;QAAC+G,IAAI,EAAEtD,UAAW;QAACwE,OAAO,EAAE3C,iBAAkB;QAAC4C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAf,QAAA,gBAC3EzE,OAAA,CAAC1C,WAAW;UAAAmH,QAAA,EACTzD,UAAU,KAAK,aAAa,GAAG,oBAAoB,GAAG;QAAkB;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACd7E,OAAA,CAACzC,aAAa;UAAAkH,QAAA,eACZzE,OAAA,CAAC/C,IAAI;YAACwI,SAAS;YAACC,OAAO,EAAE,CAAE;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAnB,QAAA,gBACxCzE,OAAA,CAAC/C,IAAI;cAAC4I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eACvBzE,OAAA,CAACvC,SAAS;gBACR2F,IAAI,EAAC,gBAAgB;gBACrB4C,KAAK,EAAC,gBAAgB;gBACtBR,SAAS;gBACTS,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAE/B,QAAQ,CAACE,cAAe;gBAC/B0E,QAAQ,EAAEhD,gBAAiB;gBAC3BiD,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP7E,OAAA,CAAC/C,IAAI;cAAC4I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eACvBzE,OAAA,CAACvC,SAAS;gBACR2F,IAAI,EAAC,cAAc;gBACnB4C,KAAK,EAAC,cAAc;gBACpBI,IAAI,EAAC,MAAM;gBACXZ,SAAS;gBACTS,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAE/B,QAAQ,CAACG,YAAa;gBAC7ByE,QAAQ,EAAEhD,gBAAiB;gBAC3BmD,eAAe,EAAE;kBAAEC,MAAM,EAAE;gBAAK,CAAE;gBAClCH,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP7E,OAAA,CAAC/C,IAAI;cAAC4I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eACvBzE,OAAA,CAACtC,WAAW;gBAAC8H,SAAS;gBAACS,OAAO,EAAC,UAAU;gBAAAxB,QAAA,gBACvCzE,OAAA,CAACrC,UAAU;kBAAA8G,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC7E,OAAA,CAACpC,MAAM;kBACLwF,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAE/B,QAAQ,CAACI,YAAa;kBAC7BwE,QAAQ,EAAEhD,gBAAiB;kBAC3B8C,KAAK,EAAC,cAAc;kBACpBG,QAAQ;kBAAA1B,QAAA,gBAERzE,OAAA,CAACnC,QAAQ;oBAACwF,KAAK,EAAC,MAAM;oBAAAoB,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACtC7E,OAAA,CAACnC,QAAQ;oBAACwF,KAAK,EAAC,UAAU;oBAAAoB,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9C7E,OAAA,CAACnC,QAAQ;oBAACwF,KAAK,EAAC,QAAQ;oBAAAoB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACP7E,OAAA,CAAC/C,IAAI;cAAC4I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eACvBzE,OAAA,CAACvC,SAAS;gBACR2F,IAAI,EAAC,SAAS;gBACd4C,KAAK,EAAC,qBAAqB;gBAC3BR,SAAS;gBACTS,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAE/B,QAAQ,CAACK,OAAQ;gBACxBuE,QAAQ,EAAEhD;cAAiB;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP7E,OAAA,CAAC/C,IAAI;cAAC4I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eACvBzE,OAAA,CAACvC,SAAS;gBACR2F,IAAI,EAAC,WAAW;gBAChB4C,KAAK,EAAC,WAAW;gBACjBR,SAAS;gBACTS,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAE/B,QAAQ,CAACM,SAAU;gBAC1BsE,QAAQ,EAAEhD;cAAiB;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP7E,OAAA,CAAC/C,IAAI;cAAC4I,IAAI;cAACC,EAAE,EAAE,EAAG;cAAArB,QAAA,eAChBzE,OAAA,CAACvC,SAAS;gBACR2F,IAAI,EAAC,MAAM;gBACX4C,KAAK,EAAC,MAAM;gBACZR,SAAS;gBACTe,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRP,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAE/B,QAAQ,CAACO,IAAK;gBACrBqE,QAAQ,EAAEhD;cAAiB;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChB7E,OAAA,CAACxC,aAAa;UAAAiH,QAAA,gBACZzE,OAAA,CAACjD,MAAM;YAACmI,OAAO,EAAEvC,iBAAkB;YAAA8B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD7E,OAAA,CAACjD,MAAM;YACLmI,OAAO,EAAElE,UAAU,KAAK,aAAa,GAAGuC,iBAAiB,GAAGG,qBAAsB;YAClF+C,QAAQ,EAAEnG,OAAO,IAAI,CAACgB,QAAQ,CAACE,cAAc,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAa;YAClGgF,SAAS,EAAEpG,OAAO,gBAAGN,OAAA,CAAC3B,gBAAgB;cAAC0G,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACZ,QAAQ;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7D,UAAU,KAAK,kBAAkB,EAAE;MAC5C,oBACEhB,OAAA,CAAC3C,MAAM;QAAC+G,IAAI,EAAEtD,UAAW;QAACwE,OAAO,EAAE3C,iBAAkB;QAAC4C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAf,QAAA,gBAC3EzE,OAAA,CAAC1C,WAAW;UAAAmH,QAAA,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1D7E,OAAA,CAACzC,aAAa;UAAAkH,QAAA,EACXnE,OAAO,gBACNN,OAAA,CAAC3B,gBAAgB;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBrE,OAAO,CAAC+D,MAAM,KAAK,CAAC,gBACtBvE,OAAA,CAAC5B,KAAK;YAACoG,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE1D7E,OAAA,CAAClC,IAAI;YAAA2G,QAAA,EACFjE,OAAO,CAACwE,GAAG,CAAEnC,OAAO,iBACnB7C,OAAA,CAACjC,QAAQ;cACP4I,MAAM;cAENzB,OAAO,EAAEA,CAAA,KAAMtC,mBAAmB,CAACC,OAAO,CAAE;cAAA4B,QAAA,eAE5CzE,OAAA,CAAChC,YAAY;gBACX4I,OAAO,EAAE,GAAG/D,OAAO,CAACrB,cAAc,MAAMqB,OAAO,CAACnB,YAAY,EAAG;gBAC/DmF,SAAS,EAAE,SAAS,IAAIrE,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACwD,kBAAkB,CAAC,CAAC,YAAYpC,OAAO,CAAClB,OAAO,IAAI,eAAe;cAAG;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzH;YAAC,GANGhC,OAAO,CAACE,UAAU;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB7E,OAAA,CAACxC,aAAa;UAAAiH,QAAA,eACZzE,OAAA,CAACjD,MAAM;YAACmI,OAAO,EAAEvC,iBAAkB;YAAA8B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7D,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACEhB,OAAA,CAAC3C,MAAM;QAAC+G,IAAI,EAAEtD,UAAW;QAACwE,OAAO,EAAE3C,iBAAkB;QAAC4C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAf,QAAA,gBAC3EzE,OAAA,CAAC1C,WAAW;UAAAmH,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1C7E,OAAA,CAACzC,aAAa;UAAAkH,QAAA,EACX,CAACvD,eAAe,GACfZ,OAAO,gBACLN,OAAA,CAAC3B,gBAAgB;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBrE,OAAO,CAAC+D,MAAM,KAAK,CAAC,gBACtBvE,OAAA,CAAC5B,KAAK;YAACoG,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE1D7E,OAAA,CAAClC,IAAI;YAAA2G,QAAA,EACFjE,OAAO,CAACwE,GAAG,CAAEnC,OAAO,iBACnB7C,OAAA,CAACjC,QAAQ;cACP4I,MAAM;cAENzB,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC0B,OAAO,CAAE;cAAA4B,QAAA,eAE3CzE,OAAA,CAAChC,YAAY;gBACX4I,OAAO,EAAE,GAAG/D,OAAO,CAACrB,cAAc,MAAMqB,OAAO,CAACnB,YAAY,EAAG;gBAC/DmF,SAAS,EAAE,SAAS,IAAIrE,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACwD,kBAAkB,CAAC,CAAC;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC,GANGhC,OAAO,CAACE,UAAU;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAED7E,OAAA,CAACnD,GAAG;YAAA4H,QAAA,gBACFzE,OAAA,CAAC5B,KAAK;cAACoG,QAAQ,EAAC,SAAS;cAACmB,EAAE,EAAE;gBAAEmB,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,GAAC,2CACE,EAACvD,eAAe,CAACM,cAAc,EAAC,GAC3E;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7E,OAAA,CAAClD,UAAU;cAACmJ,OAAO,EAAC,OAAO;cAAAxB,QAAA,EAAC;YAE5B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB7E,OAAA,CAACxC,aAAa;UAAAiH,QAAA,gBACZzE,OAAA,CAACjD,MAAM;YAACmI,OAAO,EAAEvC,iBAAkB;YAAA8B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnD3D,eAAe,iBACdlB,OAAA,CAACjD,MAAM;YACLmI,OAAO,EAAEtB,oBAAqB;YAC9B6C,QAAQ,EAAEnG,OAAQ;YAClB8E,KAAK,EAAC,OAAO;YACbsB,SAAS,EAAEpG,OAAO,gBAAGN,OAAA,CAAC3B,gBAAgB;cAAC0G,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACd,UAAU;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7D,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEhB,OAAA,CAAC3C,MAAM;QAAC+G,IAAI,EAAEtD,UAAW;QAACwE,OAAO,EAAE3C,iBAAkB;QAAC4C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAf,QAAA,gBAC3EzE,OAAA,CAAC1C,WAAW;UAAAmH,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxD7E,OAAA,CAACzC,aAAa;UAAAkH,QAAA,EACXnE,OAAO,gBACNN,OAAA,CAAC3B,gBAAgB;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBrE,OAAO,CAAC+D,MAAM,KAAK,CAAC,gBACtBvE,OAAA,CAAC5B,KAAK;YAACoG,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE1D7E,OAAA,CAAClC,IAAI;YAAA2G,QAAA,EACFjE,OAAO,CAACwE,GAAG,CAAEnC,OAAO,iBACnB7C,OAAA,CAACjC,QAAQ;cACP4I,MAAM;cAENzB,OAAO,EAAEA,CAAA,KAAMtC,mBAAmB,CAACC,OAAO,CAAE;cAAA4B,QAAA,eAE5CzE,OAAA,CAAChC,YAAY;gBACX4I,OAAO,EAAE,GAAG/D,OAAO,CAACrB,cAAc,MAAMqB,OAAO,CAACnB,YAAY,EAAG;gBAC/DmF,SAAS,EAAE,SAAS,IAAIrE,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACwD,kBAAkB,CAAC,CAAC,YAAYpC,OAAO,CAAClB,OAAO,IAAI,eAAe;cAAG;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzH;YAAC,GANGhC,OAAO,CAACE,UAAU;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB7E,OAAA,CAACxC,aAAa;UAAAiH,QAAA,eACZzE,OAAA,CAACjD,MAAM;YAACmI,OAAO,EAAEvC,iBAAkB;YAAA8B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7D,UAAU,KAAK,sBAAsB,EAAE;MAChD,oBACEhB,OAAA,CAAC3C,MAAM;QAAC+G,IAAI,EAAEtD,UAAW;QAACwE,OAAO,EAAE3C,iBAAkB;QAAC4C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAf,QAAA,gBAC3EzE,OAAA,CAAC1C,WAAW;UAAAmH,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC/D7E,OAAA,CAACzC,aAAa;UAAAkH,QAAA,EACXnE,OAAO,gBACNN,OAAA,CAAC3B,gBAAgB;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBnE,IAAI,CAAC6D,MAAM,KAAK,CAAC,gBACnBvE,OAAA,CAAC5B,KAAK;YAACoG,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEtD7E,OAAA,CAAClC,IAAI;YAAA2G,QAAA,EACF/D,IAAI,CAACsE,GAAG,CAAE/B,IAAI,iBACbjD,OAAA,CAACjC,QAAQ;cACP4I,MAAM;cAENzB,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAACC,IAAI,CAAE;cAAAwB,QAAA,eAEtCzE,OAAA,CAAChC,YAAY;gBACX4I,OAAO,EAAE3D,IAAI,CAACtB,OAAQ;gBACtBkF,SAAS,EAAE,GAAG5D,IAAI,CAAC8D,SAAS,IAAI,KAAK,UAAU9D,IAAI,CAAC+D,mBAAmB,IAAI,KAAK,OAAO/D,IAAI,CAACgE,iBAAiB,IAAI,KAAK;cAAG;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1H;YAAC,GANG5B,IAAI,CAACtB,OAAO;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB7E,OAAA,CAACxC,aAAa;UAAAiH,QAAA,eACZzE,OAAA,CAACjD,MAAM;YAACmI,OAAO,EAAEvC,iBAAkB;YAAA8B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7D,UAAU,KAAK,yBAAyB,EAAE;MACnD,oBACEhB,OAAA,CAAC3C,MAAM;QAAC+G,IAAI,EAAEtD,UAAW;QAACwE,OAAO,EAAE3C,iBAAkB;QAAC4C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAf,QAAA,gBAC3EzE,OAAA,CAAC1C,WAAW;UAAAmH,QAAA,GAAC,yCAAuC,EAACrD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEO,OAAO;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACzF7E,OAAA,CAACzC,aAAa;UAAAkH,QAAA,EACXnE,OAAO,gBACNN,OAAA,CAAC3B,gBAAgB;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBrE,OAAO,CAAC+D,MAAM,KAAK,CAAC,gBACtBvE,OAAA,CAAC5B,KAAK;YAACoG,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE1D7E,OAAA,CAAClC,IAAI;YAAA2G,QAAA,EACFjE,OAAO,CAACwE,GAAG,CAAEnC,OAAO,iBACnB7C,OAAA,CAACjC,QAAQ;cACP4I,MAAM;cAENzB,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC0B,OAAO,CAAE;cAAA4B,QAAA,eAE3CzE,OAAA,CAAChC,YAAY;gBACX4I,OAAO,EAAE,GAAG/D,OAAO,CAACrB,cAAc,MAAMqB,OAAO,CAACnB,YAAY,EAAG;gBAC/DmF,SAAS,EAAE,SAAS,IAAIrE,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACwD,kBAAkB,CAAC,CAAC;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC,GANGhC,OAAO,CAACE,UAAU;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB7E,OAAA,CAACxC,aAAa;UAAAiH,QAAA,gBACZzE,OAAA,CAACjD,MAAM;YAACmI,OAAO,EAAEvC,iBAAkB;YAAA8B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnD3D,eAAe,iBACdlB,OAAA,CAACjD,MAAM;YACLmI,OAAO,EAAEpB,oBAAqB;YAC9B2C,QAAQ,EAAEnG,OAAQ;YAClBoG,SAAS,EAAEpG,OAAO,gBAAGN,OAAA,CAAC3B,gBAAgB;cAAC0G,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACZ,QAAQ;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACE7E,OAAA,CAACnD,GAAG;IAAA4H,QAAA,GACD7D,cAAc,KAAK,mBAAmB,IAAI,CAACE,UAAU,gBACpDd,OAAA,CAAChD,KAAK;MAAC2I,EAAE,EAAE;QAAEuB,CAAC,EAAE;MAAE,CAAE;MAAAzC,QAAA,gBAClBzE,OAAA,CAAClD,UAAU;QAACmJ,OAAO,EAAC,IAAI;QAACkB,YAAY;QAAA1C,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZvE,OAAO,gBACNN,OAAA,CAACnD,GAAG;QAAC8I,EAAE,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA7C,QAAA,eAC5DzE,OAAA,CAAC3B,gBAAgB;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GAENP,kBAAkB,CAAC,CACpB;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,CAAC/D,UAAU,gBACbd,OAAA,CAAChD,KAAK;MAAC2I,EAAE,EAAE;QAAEuB,CAAC,EAAE,CAAC;QAAEK,SAAS,EAAE,OAAO;QAAEH,OAAO,EAAE,MAAM;QAAEI,UAAU,EAAE,QAAQ;QAAEH,cAAc,EAAE;MAAS,CAAE;MAAA5C,QAAA,EACtG,CAAC7D,cAAc,gBACdZ,OAAA,CAAClD,UAAU;QAACmJ,OAAO,EAAC,OAAO;QAAAxB,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEb7E,OAAA,CAACnD,GAAG;QAAC8I,EAAE,EAAE;UAAE8B,SAAS,EAAE;QAAS,CAAE;QAAAhD,QAAA,gBAC/BzE,OAAA,CAAClD,UAAU;UAACmJ,OAAO,EAAC,IAAI;UAACkB,YAAY;UAAA1C,QAAA,GAClC7D,cAAc,KAAK,aAAa,IAAI,oBAAoB,EACxDA,cAAc,KAAK,iBAAiB,IAAI,kBAAkB,EAC1DA,cAAc,KAAK,gBAAgB,IAAI,iBAAiB,EACxDA,cAAc,KAAK,eAAe,IAAI,gBAAgB,EACtDA,cAAc,KAAK,gBAAgB,IAAI,wBAAwB;QAAA;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACb7E,OAAA,CAAC3B,gBAAgB;UAACsH,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,IAAI,EAEPQ,YAAY,CAAC,CAAC;EAAA;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACxE,EAAA,CAxoBIJ,eAAe;AAAAyH,EAAA,GAAfzH,eAAe;AA0oBrB,eAAeA,eAAe;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}