{"ast": null, "code": "import{useEffect,useState}from'react';import userService from'../../services/userService';import{useAuth}from'../../context/AuthContext';/**\n * Componente invisibile che verifica periodicamente gli utenti scaduti.\n * Viene eseguito solo quando un amministratore è loggato.\n */const UserExpirationChecker=()=>{const{user}=useAuth();const[lastCheck,setLastCheck]=useState(null);// Verifica gli utenti scaduti all'avvio e ogni ora\nuseEffect(()=>{// Esegui solo se l'utente è un amministratore\nif(user&&user.role==='owner'){// Funzione per verificare gli utenti scaduti\nconst checkExpiredUsers=async()=>{try{const result=await userService.checkExpiredUsers();console.log('Verifica utenti scaduti:',result);setLastCheck(new Date());}catch(error){console.error('Errore durante la verifica degli utenti scaduti:',error);// Non fare nulla in caso di errore, per evitare di bloccare l'applicazione\n// L'errore potrebbe essere dovuto a un problema di autorizzazione\n// quando si impersona un utente non amministratore\n}};// Esegui subito la verifica\ncheckExpiredUsers();// Imposta un intervallo per verificare ogni ora\nconst interval=setInterval(checkExpiredUsers,60*60*1000);// Pulisci l'intervallo quando il componente viene smontato\nreturn()=>clearInterval(interval);}},[user]);// Questo componente non renderizza nulla\nreturn null;};export default UserExpirationChecker;", "map": {"version": 3, "names": ["useEffect", "useState", "userService", "useAuth", "UserExpirationChecker", "user", "<PERSON><PERSON><PERSON><PERSON>", "setLastCheck", "role", "checkExpiredUsers", "result", "console", "log", "Date", "error", "interval", "setInterval", "clearInterval"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/admin/UserExpirationChecker.js"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport userService from '../../services/userService';\nimport { useAuth } from '../../context/AuthContext';\n\n/**\n * Componente invisibile che verifica periodicamente gli utenti scaduti.\n * Viene eseguito solo quando un amministratore è loggato.\n */\nconst UserExpirationChecker = () => {\n  const { user } = useAuth();\n  const [lastCheck, setLastCheck] = useState(null);\n\n  // Verifica gli utenti scaduti all'avvio e ogni ora\n  useEffect(() => {\n    // Esegui solo se l'utente è un amministratore\n    if (user && user.role === 'owner') {\n      // Funzione per verificare gli utenti scaduti\n      const checkExpiredUsers = async () => {\n        try {\n          const result = await userService.checkExpiredUsers();\n          console.log('Verifica utenti scaduti:', result);\n          setLastCheck(new Date());\n        } catch (error) {\n          console.error('Errore durante la verifica degli utenti scaduti:', error);\n          // Non fare nulla in caso di errore, per evitare di bloccare l'applicazione\n          // L'errore potrebbe essere dovuto a un problema di autorizzazione\n          // quando si impersona un utente non amministratore\n        }\n      };\n\n      // Esegui subito la verifica\n      checkExpiredUsers();\n\n      // Imposta un intervallo per verificare ogni ora\n      const interval = setInterval(checkExpiredUsers, 60 * 60 * 1000);\n\n      // Pulisci l'intervallo quando il componente viene smontato\n      return () => clearInterval(interval);\n    }\n  }, [user]);\n\n  // Questo componente non renderizza nulla\n  return null;\n};\n\nexport default UserExpirationChecker;\n"], "mappings": "AAAA,OAASA,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC3C,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,OAASC,OAAO,KAAQ,2BAA2B,CAEnD;AACA;AACA;AACA,GACA,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAEC,IAAK,CAAC,CAAGF,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAGN,QAAQ,CAAC,IAAI,CAAC,CAEhD;AACAD,SAAS,CAAC,IAAM,CACd;AACA,GAAIK,IAAI,EAAIA,IAAI,CAACG,IAAI,GAAK,OAAO,CAAE,CACjC;AACA,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAR,WAAW,CAACO,iBAAiB,CAAC,CAAC,CACpDE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEF,MAAM,CAAC,CAC/CH,YAAY,CAAC,GAAI,CAAAM,IAAI,CAAC,CAAC,CAAC,CAC1B,CAAE,MAAOC,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,kDAAkD,CAAEA,KAAK,CAAC,CACxE;AACA;AACA;AACF,CACF,CAAC,CAED;AACAL,iBAAiB,CAAC,CAAC,CAEnB;AACA,KAAM,CAAAM,QAAQ,CAAGC,WAAW,CAACP,iBAAiB,CAAE,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAE/D;AACA,MAAO,IAAMQ,aAAa,CAACF,QAAQ,CAAC,CACtC,CACF,CAAC,CAAE,CAACV,IAAI,CAAC,CAAC,CAEV;AACA,MAAO,KAAI,CACb,CAAC,CAED,cAAe,CAAAD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}