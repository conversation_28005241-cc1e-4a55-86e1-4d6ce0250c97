{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\CreaComandaMultipla.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, Box, Grid, MenuItem, Alert, CircularProgress, List, ListItem, ListItemText, Chip, Divider } from '@mui/material';\nimport { Assignment as AssignmentIcon, Build as BuildIcon, Link as LinkIcon, Verified as VerifiedIcon, Cable as CableIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\n\n/**\n * Componente per la creazione rapida di comande multiple\n * Ottimizzato per il workflow: cavi già selezionati -> dettagli comanda -> creazione\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreaComandaMultipla = ({\n  open,\n  onClose,\n  onSuccess,\n  onError,\n  tipoComanda,\n  caviSelezionati = [],\n  cantiereId\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Dati del form\n  const [formData, setFormData] = useState({\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  // Funzione per ottenere l'etichetta del tipo comanda\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      default:\n        return tipo;\n    }\n  };\n\n  // Funzione per ottenere l'icona del tipo comanda\n  const getTipoComandaIcon = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return /*#__PURE__*/_jsxDEV(BuildIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 27\n        }, this);\n      case 'COLLEGAMENTO_PARTENZA':\n        return /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 44\n        }, this);\n      case 'COLLEGAMENTO_ARRIVO':\n        return /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 42\n        }, this);\n      case 'CERTIFICAZIONE':\n        return /*#__PURE__*/_jsxDEV(VerifiedIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 37\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 23\n        }, this);\n    }\n  };\n\n  // Funzione per ottenere la descrizione del tipo comanda\n  const getTipoComandaDescription = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Comanda per la posa fisica dei cavi';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Comanda per il collegamento lato partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Comanda per il collegamento lato arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Comanda per la certificazione e test dei cavi';\n      default:\n        return 'Comanda generica';\n    }\n  };\n\n  // Reset del form quando si chiude il dialog\n  const handleClose = () => {\n    setFormData({\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    onClose();\n  };\n\n  // Gestione del submit\n  const handleSubmit = async () => {\n    try {\n      // Validazione\n      if (!formData.responsabile.trim()) {\n        setError('Il responsabile è obbligatorio');\n        return;\n      }\n      if (caviSelezionati.length === 0) {\n        setError('Nessun cavo selezionato');\n        return;\n      }\n      setLoading(true);\n      setError(null);\n\n      // Prepara i dati della comanda\n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: formData.descrizione || `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`,\n        responsabile: formData.responsabile,\n        data_scadenza: formData.data_scadenza || null,\n        priorita: formData.priorita,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n\n      // Lista degli ID dei cavi\n      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);\n      console.log('Creazione comanda multipla:', {\n        cantiereId,\n        comandaData,\n        listaIdCavi\n      });\n\n      // Crea la comanda con i cavi\n      const response = await comandeService.createComandaConCavi(cantiereId, comandaData, listaIdCavi);\n      console.log('Comanda creata con successo:', response);\n      if (onSuccess) {\n        onSuccess(response);\n      }\n      handleClose();\n    } catch (err) {\n      console.error('Errore nella creazione della comanda:', err);\n      const errorMessage = (err === null || err === void 0 ? void 0 : err.detail) || (err === null || err === void 0 ? void 0 : err.message) || 'Errore nella creazione della comanda';\n      setError(errorMessage);\n      if (onError) {\n        onError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 1,\n        children: [getTipoComandaIcon(tipoComanda), \"Crea Comanda \", getTipoComandaLabel(tipoComanda)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: getTipoComandaDescription(tipoComanda)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              mt: 1\n            },\n            children: [\"Verranno assegnati \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [caviSelezionati.length, \" cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 34\n            }, this), \" a questa comanda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formData.responsabile,\n              onChange: e => setFormData({\n                ...formData,\n                responsabile: e.target.value\n              }),\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formData.priorita,\n              onChange: e => setFormData({\n                ...formData,\n                priorita: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Descrizione\",\n          value: formData.descrizione,\n          onChange: e => setFormData({\n            ...formData,\n            descrizione: e.target.value\n          }),\n          margin: \"normal\",\n          multiline: true,\n          rows: 2,\n          placeholder: `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Note Capo Cantiere\",\n          value: formData.note_capo_cantiere,\n          onChange: e => setFormData({\n            ...formData,\n            note_capo_cantiere: e.target.value\n          }),\n          margin: \"normal\",\n          multiline: true,\n          rows: 2,\n          helperText: \"Istruzioni specifiche per il responsabile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Data Scadenza\",\n          type: \"date\",\n          value: formData.data_scadenza,\n          onChange: e => setFormData({\n            ...formData,\n            data_scadenza: e.target.value\n          }),\n          margin: \"normal\",\n          InputLabelProps: {\n            shrink: true\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n              sx: {\n                mr: 1,\n                verticalAlign: 'middle'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), \"Cavi Selezionati (\", caviSelezionati.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxHeight: 200,\n              overflow: 'auto',\n              border: '1px solid #e0e0e0',\n              borderRadius: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: caviSelezionati.map((cavo, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: cavo.id_cavo,\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        component: \"span\",\n                        children: [cavo.tipologia, \" \\u2022 \", cavo.sezione, \" \\u2022 \", cavo.metri_teorici, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: [cavo.ubicazione_partenza, \" \\u2192 \", cavo.ubicazione_arrivo]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 264,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione || 'N/A',\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), index < caviSelezionati.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 60\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mt: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        disabled: loading,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: loading || !formData.responsabile.trim() || caviSelezionati.length === 0,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 32\n        }, this) : getTipoComandaIcon(tipoComanda),\n        children: loading ? 'Creazione...' : `Crea Comanda ${getTipoComandaLabel(tipoComanda)}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaComandaMultipla, \"uYicWJeZn9gB0+PjP74gmsHD06k=\");\n_c = CreaComandaMultipla;\nexport default CreaComandaMultipla;\nvar _c;\n$RefreshReg$(_c, \"CreaComandaMultipla\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Typography", "Box", "Grid", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "Chip", "Divider", "Assignment", "AssignmentIcon", "Build", "BuildIcon", "Link", "LinkIcon", "Verified", "VerifiedIcon", "Cable", "CableIcon", "comandeService", "jsxDEV", "_jsxDEV", "CreaComandaMultipla", "open", "onClose", "onSuccess", "onError", "tipoComanda", "caviSelezionati", "cantiereId", "_s", "loading", "setLoading", "error", "setError", "formData", "setFormData", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "getTipoComandaLabel", "tipo", "getTipoComandaIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTipoComandaDescription", "handleClose", "handleSubmit", "trim", "length", "comandaData", "tipo_comanda", "listaIdCavi", "map", "c", "id_cavo", "console", "log", "response", "createComandaConCavi", "err", "errorMessage", "detail", "message", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "display", "alignItems", "gap", "sx", "pt", "severity", "mb", "variant", "mt", "container", "spacing", "item", "xs", "sm", "label", "value", "onChange", "e", "target", "required", "helperText", "select", "margin", "multiline", "rows", "placeholder", "type", "InputLabelProps", "shrink", "gutterBottom", "mr", "verticalAlign", "maxHeight", "overflow", "border", "borderRadius", "dense", "cavo", "index", "Fragment", "primary", "secondary", "component", "tipologia", "sezione", "metri_te<PERSON>ci", "color", "ubicazione_partenza", "ubicazione_arrivo", "size", "stato_installazione", "onClick", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/CreaComandaMultipla.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Typography,\n  Box,\n  Grid,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  List,\n  ListItem,\n  ListItemText,\n  Chip,\n  Divider\n} from '@mui/material';\nimport {\n  Assignment as AssignmentIcon,\n  Build as BuildIcon,\n  Link as LinkIcon,\n  Verified as VerifiedIcon,\n  Cable as CableIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\n\n/**\n * Componente per la creazione rapida di comande multiple\n * Ottimizzato per il workflow: cavi già selezionati -> dettagli comanda -> creazione\n */\nconst CreaComandaMultipla = ({ \n  open, \n  onClose, \n  onSuccess, \n  onError,\n  tipoComanda,\n  caviSelezionati = [],\n  cantiereId \n}) => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  \n  // Dati del form\n  const [formData, setFormData] = useState({\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  // Funzione per ottenere l'etichetta del tipo comanda\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      default: return tipo;\n    }\n  };\n\n  // Funzione per ottenere l'icona del tipo comanda\n  const getTipoComandaIcon = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return <BuildIcon />;\n      case 'COLLEGAMENTO_PARTENZA': return <LinkIcon />;\n      case 'COLLEGAMENTO_ARRIVO': return <LinkIcon />;\n      case 'CERTIFICAZIONE': return <VerifiedIcon />;\n      default: return <AssignmentIcon />;\n    }\n  };\n\n  // Funzione per ottenere la descrizione del tipo comanda\n  const getTipoComandaDescription = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Comanda per la posa fisica dei cavi';\n      case 'COLLEGAMENTO_PARTENZA': return 'Comanda per il collegamento lato partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Comanda per il collegamento lato arrivo';\n      case 'CERTIFICAZIONE': return 'Comanda per la certificazione e test dei cavi';\n      default: return 'Comanda generica';\n    }\n  };\n\n  // Reset del form quando si chiude il dialog\n  const handleClose = () => {\n    setFormData({\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    onClose();\n  };\n\n  // Gestione del submit\n  const handleSubmit = async () => {\n    try {\n      // Validazione\n      if (!formData.responsabile.trim()) {\n        setError('Il responsabile è obbligatorio');\n        return;\n      }\n\n      if (caviSelezionati.length === 0) {\n        setError('Nessun cavo selezionato');\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n\n      // Prepara i dati della comanda\n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: formData.descrizione || `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`,\n        responsabile: formData.responsabile,\n        data_scadenza: formData.data_scadenza || null,\n        priorita: formData.priorita,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n\n      // Lista degli ID dei cavi\n      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);\n\n      console.log('Creazione comanda multipla:', {\n        cantiereId,\n        comandaData,\n        listaIdCavi\n      });\n\n      // Crea la comanda con i cavi\n      const response = await comandeService.createComandaConCavi(\n        cantiereId,\n        comandaData,\n        listaIdCavi\n      );\n\n      console.log('Comanda creata con successo:', response);\n      \n      if (onSuccess) {\n        onSuccess(response);\n      }\n      \n      handleClose();\n    } catch (err) {\n      console.error('Errore nella creazione della comanda:', err);\n      const errorMessage = err?.detail || err?.message || 'Errore nella creazione della comanda';\n      setError(errorMessage);\n      \n      if (onError) {\n        onError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Dialog open={open} onClose={handleClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n          {getTipoComandaIcon(tipoComanda)}\n          Crea Comanda {getTipoComandaLabel(tipoComanda)}\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ pt: 2 }}>\n          {/* Informazioni tipo comanda */}\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle2\">\n              {getTipoComandaDescription(tipoComanda)}\n            </Typography>\n            <Typography variant=\"body2\" sx={{ mt: 1 }}>\n              Verranno assegnati <strong>{caviSelezionati.length} cavi</strong> a questa comanda\n            </Typography>\n          </Alert>\n\n          {/* Form dati comanda */}\n          <Grid container spacing={2}>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                label=\"Responsabile\"\n                value={formData.responsabile}\n                onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}\n                required\n                helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                select\n                label=\"Priorità\"\n                value={formData.priorita}\n                onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}\n              >\n                <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                <MenuItem value=\"ALTA\">Alta</MenuItem>\n                <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n              </TextField>\n            </Grid>\n          </Grid>\n\n          <TextField\n            fullWidth\n            label=\"Descrizione\"\n            value={formData.descrizione}\n            onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n            margin=\"normal\"\n            multiline\n            rows={2}\n            placeholder={`Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`}\n          />\n\n          <TextField\n            fullWidth\n            label=\"Note Capo Cantiere\"\n            value={formData.note_capo_cantiere}\n            onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n            margin=\"normal\"\n            multiline\n            rows={2}\n            helperText=\"Istruzioni specifiche per il responsabile\"\n          />\n\n          <TextField\n            fullWidth\n            label=\"Data Scadenza\"\n            type=\"date\"\n            value={formData.data_scadenza}\n            onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n            margin=\"normal\"\n            InputLabelProps={{ shrink: true }}\n          />\n\n          {/* Lista cavi selezionati */}\n          <Box sx={{ mt: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              <CableIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\n              Cavi Selezionati ({caviSelezionati.length})\n            </Typography>\n            <Box sx={{ maxHeight: 200, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>\n              <List dense>\n                {caviSelezionati.map((cavo, index) => (\n                  <React.Fragment key={cavo.id_cavo}>\n                    <ListItem>\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={\n                          <Box>\n                            <Typography variant=\"caption\" component=\"span\">\n                              {cavo.tipologia} • {cavo.sezione} • {cavo.metri_teorici}m\n                            </Typography>\n                            <br />\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {cavo.ubicazione_partenza} → {cavo.ubicazione_arrivo}\n                            </Typography>\n                          </Box>\n                        }\n                      />\n                      <Chip \n                        size=\"small\" \n                        label={cavo.stato_installazione || 'N/A'} \n                        variant=\"outlined\"\n                      />\n                    </ListItem>\n                    {index < caviSelezionati.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          </Box>\n\n          {/* Errore */}\n          {error && (\n            <Alert severity=\"error\" sx={{ mt: 2 }}>\n              {error}\n            </Alert>\n          )}\n        </Box>\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={handleClose} disabled={loading}>\n          Annulla\n        </Button>\n        <Button \n          onClick={handleSubmit} \n          variant=\"contained\"\n          disabled={loading || !formData.responsabile.trim() || caviSelezionati.length === 0}\n          startIcon={loading ? <CircularProgress size={20} /> : getTipoComandaIcon(tipoComanda)}\n        >\n          {loading ? 'Creazione...' : `Crea Comanda ${getTipoComandaLabel(tipoComanda)}`}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default CreaComandaMultipla;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;;AAE1D;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,IAAI;EACJC,OAAO;EACPC,SAAS;EACTC,OAAO;EACPC,WAAW;EACXC,eAAe,GAAG,EAAE;EACpBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC;IACvC8C,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAID,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,oBAAOtB,OAAA,CAACT,SAAS;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,uBAAuB;QAAE,oBAAO3B,OAAA,CAACP,QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjD,KAAK,qBAAqB;QAAE,oBAAO3B,OAAA,CAACP,QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C,KAAK,gBAAgB;QAAE,oBAAO3B,OAAA,CAACL,YAAY;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9C;QAAS,oBAAO3B,OAAA,CAACX,cAAc;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAIN,IAAI,IAAK;IAC1C,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,qCAAqC;MACzD,KAAK,uBAAuB;QAAE,OAAO,2CAA2C;MAChF,KAAK,qBAAqB;QAAE,OAAO,yCAAyC;MAC5E,KAAK,gBAAgB;QAAE,OAAO,+CAA+C;MAC7E;QAAS,OAAO,kBAAkB;IACpC;EACF,CAAC;;EAED;EACA,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxBd,WAAW,CAAC;MACVC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACFP,QAAQ,CAAC,IAAI,CAAC;IACdV,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,IAAI,CAAChB,QAAQ,CAACG,YAAY,CAACc,IAAI,CAAC,CAAC,EAAE;QACjClB,QAAQ,CAAC,gCAAgC,CAAC;QAC1C;MACF;MAEA,IAAIN,eAAe,CAACyB,MAAM,KAAK,CAAC,EAAE;QAChCnB,QAAQ,CAAC,yBAAyB,CAAC;QACnC;MACF;MAEAF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMoB,WAAW,GAAG;QAClBC,YAAY,EAAE5B,WAAW;QACzBU,WAAW,EAAEF,QAAQ,CAACE,WAAW,IAAI,WAAWK,mBAAmB,CAACf,WAAW,CAAC,QAAQC,eAAe,CAACyB,MAAM,OAAO;QACrHf,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCC,aAAa,EAAEJ,QAAQ,CAACI,aAAa,IAAI,IAAI;QAC7CC,QAAQ,EAAEL,QAAQ,CAACK,QAAQ;QAC3BC,kBAAkB,EAAEN,QAAQ,CAACM;MAC/B,CAAC;;MAED;MACA,MAAMe,WAAW,GAAG5B,eAAe,CAAC6B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC;MAEvDC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;QACzChC,UAAU;QACVyB,WAAW;QACXE;MACF,CAAC,CAAC;;MAEF;MACA,MAAMM,QAAQ,GAAG,MAAM3C,cAAc,CAAC4C,oBAAoB,CACxDlC,UAAU,EACVyB,WAAW,EACXE,WACF,CAAC;MAEDI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,QAAQ,CAAC;MAErD,IAAIrC,SAAS,EAAE;QACbA,SAAS,CAACqC,QAAQ,CAAC;MACrB;MAEAZ,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZJ,OAAO,CAAC3B,KAAK,CAAC,uCAAuC,EAAE+B,GAAG,CAAC;MAC3D,MAAMC,YAAY,GAAG,CAAAD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,MAAIF,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEG,OAAO,KAAI,sCAAsC;MAC1FjC,QAAQ,CAAC+B,YAAY,CAAC;MAEtB,IAAIvC,OAAO,EAAE;QACXA,OAAO,CAACuC,YAAY,CAAC;MACvB;IACF,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEX,OAAA,CAAC7B,MAAM;IAAC+B,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAE0B,WAAY;IAACkB,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC/DjD,OAAA,CAAC5B,WAAW;MAAA6E,QAAA,eACVjD,OAAA,CAACtB,GAAG;QAACwE,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAH,QAAA,GAC5C1B,kBAAkB,CAACjB,WAAW,CAAC,EAAC,eACpB,EAACe,mBAAmB,CAACf,WAAW,CAAC;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd3B,OAAA,CAAC3B,aAAa;MAAA4E,QAAA,eACZjD,OAAA,CAACtB,GAAG;QAAC2E,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBAEjBjD,OAAA,CAACnB,KAAK;UAAC0E,QAAQ,EAAC,MAAM;UAACF,EAAE,EAAE;YAAEG,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,gBACnCjD,OAAA,CAACvB,UAAU;YAACgF,OAAO,EAAC,WAAW;YAAAR,QAAA,EAC5BrB,yBAAyB,CAACtB,WAAW;UAAC;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACb3B,OAAA,CAACvB,UAAU;YAACgF,OAAO,EAAC,OAAO;YAACJ,EAAE,EAAE;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,GAAC,qBACtB,eAAAjD,OAAA;cAAAiD,QAAA,GAAS1C,eAAe,CAACyB,MAAM,EAAC,OAAK;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,qBACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGR3B,OAAA,CAACrB,IAAI;UAACgF,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBjD,OAAA,CAACrB,IAAI;YAACkF,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACvBjD,OAAA,CAACxB,SAAS;cACRwE,SAAS;cACTgB,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAEnD,QAAQ,CAACG,YAAa;cAC7BiD,QAAQ,EAAGC,CAAC,IAAKpD,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,YAAY,EAAEkD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,QAAQ;cACRC,UAAU,EAAC;YAAuC;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP3B,OAAA,CAACrB,IAAI;YAACkF,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACvBjD,OAAA,CAACxB,SAAS;cACRwE,SAAS;cACTuB,MAAM;cACNP,KAAK,EAAC,aAAU;cAChBC,KAAK,EAAEnD,QAAQ,CAACK,QAAS;cACzB+C,QAAQ,EAAGC,CAAC,IAAKpD,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,QAAQ,EAAEgD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAAAhB,QAAA,gBAExEjD,OAAA,CAACpB,QAAQ;gBAACqF,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAK;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxC3B,OAAA,CAACpB,QAAQ;gBAACqF,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5C3B,OAAA,CAACpB,QAAQ;gBAACqF,KAAK,EAAC,MAAM;gBAAAhB,QAAA,EAAC;cAAI;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC3B,OAAA,CAACpB,QAAQ;gBAACqF,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP3B,OAAA,CAACxB,SAAS;UACRwE,SAAS;UACTgB,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAEnD,QAAQ,CAACE,WAAY;UAC5BkD,QAAQ,EAAGC,CAAC,IAAKpD,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEE,WAAW,EAAEmD,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAE;UAC3EO,MAAM,EAAC,QAAQ;UACfC,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,WAAW,EAAE,WAAWtD,mBAAmB,CAACf,WAAW,CAAC,QAAQC,eAAe,CAACyB,MAAM;QAAQ;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eAEF3B,OAAA,CAACxB,SAAS;UACRwE,SAAS;UACTgB,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAEnD,QAAQ,CAACM,kBAAmB;UACnC8C,QAAQ,EAAGC,CAAC,IAAKpD,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEM,kBAAkB,EAAE+C,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAE;UAClFO,MAAM,EAAC,QAAQ;UACfC,SAAS;UACTC,IAAI,EAAE,CAAE;UACRJ,UAAU,EAAC;QAA2C;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAEF3B,OAAA,CAACxB,SAAS;UACRwE,SAAS;UACTgB,KAAK,EAAC,eAAe;UACrBY,IAAI,EAAC,MAAM;UACXX,KAAK,EAAEnD,QAAQ,CAACI,aAAc;UAC9BgD,QAAQ,EAAGC,CAAC,IAAKpD,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEI,aAAa,EAAEiD,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAE;UAC7EO,MAAM,EAAC,QAAQ;UACfK,eAAe,EAAE;YAAEC,MAAM,EAAE;UAAK;QAAE;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAGF3B,OAAA,CAACtB,GAAG;UAAC2E,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACjBjD,OAAA,CAACvB,UAAU;YAACgF,OAAO,EAAC,IAAI;YAACsB,YAAY;YAAA9B,QAAA,gBACnCjD,OAAA,CAACH,SAAS;cAACwD,EAAE,EAAE;gBAAE2B,EAAE,EAAE,CAAC;gBAAEC,aAAa,EAAE;cAAS;YAAE;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBACnC,EAACpB,eAAe,CAACyB,MAAM,EAAC,GAC5C;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3B,OAAA,CAACtB,GAAG;YAAC2E,EAAE,EAAE;cAAE6B,SAAS,EAAE,GAAG;cAAEC,QAAQ,EAAE,MAAM;cAAEC,MAAM,EAAE,mBAAmB;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAApC,QAAA,eAC1FjD,OAAA,CAACjB,IAAI;cAACuG,KAAK;cAAArC,QAAA,EACR1C,eAAe,CAAC6B,GAAG,CAAC,CAACmD,IAAI,EAAEC,KAAK,kBAC/BxF,OAAA,CAAC/B,KAAK,CAACwH,QAAQ;gBAAAxC,QAAA,gBACbjD,OAAA,CAAChB,QAAQ;kBAAAiE,QAAA,gBACPjD,OAAA,CAACf,YAAY;oBACXyG,OAAO,EAAEH,IAAI,CAACjD,OAAQ;oBACtBqD,SAAS,eACP3F,OAAA,CAACtB,GAAG;sBAAAuE,QAAA,gBACFjD,OAAA,CAACvB,UAAU;wBAACgF,OAAO,EAAC,SAAS;wBAACmC,SAAS,EAAC,MAAM;wBAAA3C,QAAA,GAC3CsC,IAAI,CAACM,SAAS,EAAC,UAAG,EAACN,IAAI,CAACO,OAAO,EAAC,UAAG,EAACP,IAAI,CAACQ,aAAa,EAAC,GAC1D;sBAAA;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACb3B,OAAA;wBAAAwB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN3B,OAAA,CAACvB,UAAU;wBAACgF,OAAO,EAAC,SAAS;wBAACuC,KAAK,EAAC,gBAAgB;wBAAA/C,QAAA,GACjDsC,IAAI,CAACU,mBAAmB,EAAC,UAAG,EAACV,IAAI,CAACW,iBAAiB;sBAAA;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACF3B,OAAA,CAACd,IAAI;oBACHiH,IAAI,EAAC,OAAO;oBACZnC,KAAK,EAAEuB,IAAI,CAACa,mBAAmB,IAAI,KAAM;oBACzC3C,OAAO,EAAC;kBAAU;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,EACV6D,KAAK,GAAGjF,eAAe,CAACyB,MAAM,GAAG,CAAC,iBAAIhC,OAAA,CAACb,OAAO;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GAtB/B4D,IAAI,CAACjD,OAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBjB,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLf,KAAK,iBACJZ,OAAA,CAACnB,KAAK;UAAC0E,QAAQ,EAAC,OAAO;UAACF,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACnCrC;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhB3B,OAAA,CAAC1B,aAAa;MAAA2E,QAAA,gBACZjD,OAAA,CAACzB,MAAM;QAAC8H,OAAO,EAAExE,WAAY;QAACyE,QAAQ,EAAE5F,OAAQ;QAAAuC,QAAA,EAAC;MAEjD;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3B,OAAA,CAACzB,MAAM;QACL8H,OAAO,EAAEvE,YAAa;QACtB2B,OAAO,EAAC,WAAW;QACnB6C,QAAQ,EAAE5F,OAAO,IAAI,CAACI,QAAQ,CAACG,YAAY,CAACc,IAAI,CAAC,CAAC,IAAIxB,eAAe,CAACyB,MAAM,KAAK,CAAE;QACnFuE,SAAS,EAAE7F,OAAO,gBAAGV,OAAA,CAAClB,gBAAgB;UAACqH,IAAI,EAAE;QAAG;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAGJ,kBAAkB,CAACjB,WAAW,CAAE;QAAA2C,QAAA,EAErFvC,OAAO,GAAG,cAAc,GAAG,gBAAgBW,mBAAmB,CAACf,WAAW,CAAC;MAAE;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAClB,EAAA,CAjRIR,mBAAmB;AAAAuG,EAAA,GAAnBvG,mBAAmB;AAmRzB,eAAeA,mBAAmB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}