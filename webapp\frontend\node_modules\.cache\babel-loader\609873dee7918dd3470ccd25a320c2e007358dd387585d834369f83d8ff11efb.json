{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * The locale string (see: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locales_argument).\n */\n\n/**\n * The format options (see: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#options)\n */\n\n/**\n * The locale options.\n */\n\n/**\n * @name intlFormat\n * @category Common Helpers\n * @summary Format the date with Intl.DateTimeFormat (https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat).\n *\n * @description\n * Return the formatted date string in the given format.\n * The method uses [`Intl.DateTimeFormat`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat) inside.\n * formatOptions are the same as [`Intl.DateTimeFormat` options](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat#using_options)\n *\n * > ⚠️ Please note that before Node version 13.0.0, only the locale data for en-US is available by default.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to format\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in middle-endian format:\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456))\n * //=> 10/4/2019\n */\n\n/**\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to format\n * @param localeOptions - An object with locale\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in Korean.\n * // Convert the date with locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   locale: 'ko-KR',\n * })\n * //=> 2019. 10. 4.\n */\n\n/**\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to format\n * @param formatOptions - The format options\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019.\n * // Convert the date with format's options.\n * const result = intlFormat.default(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   year: 'numeric',\n *   month: 'numeric',\n *   day: 'numeric',\n *   hour: 'numeric',\n * })\n * //=> 10/4/2019, 12 PM\n */\n\n/**\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to format\n * @param formatOptions - The format options\n * @param localeOptions - An object with locale\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in German.\n * // Convert the date with format's options and locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   weekday: 'long',\n *   year: 'numeric',\n *   month: 'long',\n *   day: 'numeric',\n * }, {\n *   locale: 'de-DE',\n * })\n * //=> Freitag, 4. Oktober 2019\n */\n\nexport function intlFormat(date, formatOrLocale, localeOptions) {\n  let formatOptions;\n  if (isFormatOptions(formatOrLocale)) {\n    formatOptions = formatOrLocale;\n  } else {\n    localeOptions = formatOrLocale;\n  }\n  return new Intl.DateTimeFormat(localeOptions?.locale, formatOptions).format(toDate(date));\n}\nfunction isFormatOptions(opts) {\n  return opts !== undefined && !(\"locale\" in opts);\n}\n\n// Fallback for modularized imports:\nexport default intlFormat;", "map": {"version": 3, "names": ["toDate", "intlFormat", "date", "formatOrLocale", "localeOptions", "formatOptions", "isFormatOptions", "Intl", "DateTimeFormat", "locale", "format", "opts", "undefined"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/intlFormat.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * The locale string (see: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locales_argument).\n */\n\n/**\n * The format options (see: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#options)\n */\n\n/**\n * The locale options.\n */\n\n/**\n * @name intlFormat\n * @category Common Helpers\n * @summary Format the date with Intl.DateTimeFormat (https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat).\n *\n * @description\n * Return the formatted date string in the given format.\n * The method uses [`Intl.DateTimeFormat`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat) inside.\n * formatOptions are the same as [`Intl.DateTimeFormat` options](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat#using_options)\n *\n * > ⚠️ Please note that before Node version 13.0.0, only the locale data for en-US is available by default.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to format\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in middle-endian format:\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456))\n * //=> 10/4/2019\n */\n\n/**\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to format\n * @param localeOptions - An object with locale\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in Korean.\n * // Convert the date with locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   locale: 'ko-KR',\n * })\n * //=> 2019. 10. 4.\n */\n\n/**\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to format\n * @param formatOptions - The format options\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019.\n * // Convert the date with format's options.\n * const result = intlFormat.default(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   year: 'numeric',\n *   month: 'numeric',\n *   day: 'numeric',\n *   hour: 'numeric',\n * })\n * //=> 10/4/2019, 12 PM\n */\n\n/**\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to format\n * @param formatOptions - The format options\n * @param localeOptions - An object with locale\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 4 October 2019 in German.\n * // Convert the date with format's options and locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *   weekday: 'long',\n *   year: 'numeric',\n *   month: 'long',\n *   day: 'numeric',\n * }, {\n *   locale: 'de-DE',\n * })\n * //=> Freitag, 4. Oktober 2019\n */\n\nexport function intlFormat(date, formatOrLocale, localeOptions) {\n  let formatOptions;\n\n  if (isFormatOptions(formatOrLocale)) {\n    formatOptions = formatOrLocale;\n  } else {\n    localeOptions = formatOrLocale;\n  }\n\n  return new Intl.DateTimeFormat(localeOptions?.locale, formatOptions).format(\n    toDate(date),\n  );\n}\n\nfunction isFormatOptions(opts) {\n  return opts !== undefined && !(\"locale\" in opts);\n}\n\n// Fallback for modularized imports:\nexport default intlFormat;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,cAAc,EAAEC,aAAa,EAAE;EAC9D,IAAIC,aAAa;EAEjB,IAAIC,eAAe,CAACH,cAAc,CAAC,EAAE;IACnCE,aAAa,GAAGF,cAAc;EAChC,CAAC,MAAM;IACLC,aAAa,GAAGD,cAAc;EAChC;EAEA,OAAO,IAAII,IAAI,CAACC,cAAc,CAACJ,aAAa,EAAEK,MAAM,EAAEJ,aAAa,CAAC,CAACK,MAAM,CACzEV,MAAM,CAACE,IAAI,CACb,CAAC;AACH;AAEA,SAASI,eAAeA,CAACK,IAAI,EAAE;EAC7B,OAAOA,IAAI,KAAKC,SAAS,IAAI,EAAE,QAAQ,IAAID,IAAI,CAAC;AAClD;;AAEA;AACA,eAAeV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}