{"ast": null, "code": "'use client';\n\nexport { default } from './FormControl';\nexport { default as useFormControl } from './useFormControl';\nexport { default as formControlClasses } from './formControlClasses';\nexport * from './formControlClasses';", "map": {"version": 3, "names": ["default", "useFormControl", "formControlClasses"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/FormControl/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './FormControl';\nexport { default as useFormControl } from './useFormControl';\nexport { default as formControlClasses } from './formControlClasses';\nexport * from './formControlClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,cAAc,QAAQ,kBAAkB;AAC5D,SAASD,OAAO,IAAIE,kBAAkB,QAAQ,sBAAsB;AACpE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}