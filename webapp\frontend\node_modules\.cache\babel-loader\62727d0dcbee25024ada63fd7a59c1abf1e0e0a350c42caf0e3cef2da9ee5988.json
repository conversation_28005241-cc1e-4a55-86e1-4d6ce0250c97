{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 15000,\n  // Timeout aumentato a 15 secondi\n  withCredentials: false // Modificato per risolvere problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', {\n        cantiereId,\n        tipoCavo,\n        filters\n      });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, {\n          timeout: 30000\n        });\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n        return response.data;\n      } catch (apiError) {\n        var _apiError$response, _apiError$response2, _apiError$response3, _apiError$response4;\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n          statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n          data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data,\n          headers: (_apiError$response4 = apiError.response) === null || _apiError$response4 === void 0 ? void 0 : _apiError$response4.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n        throw apiError;\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message || 'Errore sconosciuto');\n      enhancedError.status = (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status;\n      enhancedError.data = (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n      throw enhancedError;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`, {\n        force: force\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Mark cavo as SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const config = {};\n      if (mode) {\n        config.params = {\n          mode\n        };\n      }\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}`, config);\n      return response.data;\n    } catch (error) {\n      var _error$response7, _error$response8, _error$response9;\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.status,\n        statusText: (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.statusText,\n        data: (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data,\n        url: `/cavi/${cantiereIdNum}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "config", "API_URL", "axiosInstance", "create", "baseURL", "headers", "timeout", "withCredentials", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "filters", "console", "log", "cantiereIdNum", "parseInt", "isNaN", "Error", "url", "queryParams", "push", "stato_installazione", "encodeURIComponent", "tipologia", "sort_by", "sort_order", "length", "join", "response", "get", "data", "status", "Array", "isArray", "warn", "apiError", "_apiError$response", "_apiError$response2", "_apiError$response3", "_apiError$response4", "message", "statusText", "code", "isAxiosError", "method", "testResponse", "fetch", "testError", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "stack", "enhancedError", "detail", "originalError", "createCavo", "cavoData", "post", "updateCavo", "cavoId", "put", "getRevisioneCorrente", "revisione_corrente", "markCavoAsSpare", "force", "deleteCavo", "mode", "params", "delete", "_error$response7", "_error$response8", "_error$response9", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_posati", "updateBobina", "idBobina", "id_bobina", "getCaviInstallati", "getCaviStats", "collegaCavo", "lato", "responsabile", "scollegaCavo"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\nimport config from '../config';\n\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 15000, // Timeout aumentato a 15 secondi\n  withCredentials: false // Modificato per risolvere problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', { cantiereId, tipoCavo, filters });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, { timeout: 30000 });\n\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n\n        return response.data;\n      } catch (apiError) {\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: apiError.response?.status,\n          statusText: apiError.response?.statusText,\n          data: apiError.response?.data,\n          headers: apiError.response?.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n\n        throw apiError;\n      }\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(error.response?.data?.detail || error.message || 'Errore sconosciuto');\n      enhancedError.status = error.response?.status;\n      enhancedError.data = error.response?.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n\n      throw enhancedError;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`, {\n        force: force\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Mark cavo as SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const config = {};\n      if (mode) {\n        config.params = { mode };\n      }\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}`, config);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereIdNum}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAE9B,MAAMC,OAAO,GAAGD,MAAM,CAACC,OAAO;;AAE9B;AACA,MAAMC,aAAa,GAAGH,KAAK,CAACI,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK;EAAE;EAChBC,eAAe,EAAE,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAL,aAAa,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCV,MAAM,IAAK;EACV,MAAMW,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTX,MAAM,CAACK,OAAO,CAACS,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOX,MAAM;AACf,CAAC,EACAe,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5D,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEJ,UAAU;QAAEC,QAAQ;QAAEC;MAAQ,CAAC,CAAC;MACvEC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,OAAOJ,UAAU,CAAC;;MAErD;MACA,IAAIK,aAAa,GAAGL,UAAU;MAC9B,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;QAClCK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;QACxCG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEC,aAAa,CAAC;MAC1E;MAEA,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;QACxBF,OAAO,CAACR,KAAK,CAAC,qCAAqC,EAAEK,UAAU,CAAC;QAChE,MAAM,IAAIQ,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,IAAIS,GAAG,GAAG,SAASJ,aAAa,EAAE;MAClC,MAAMK,WAAW,GAAG,EAAE;MAEtB,IAAIT,QAAQ,KAAK,IAAI,EAAE;QACrBS,WAAW,CAACC,IAAI,CAAC,aAAaV,QAAQ,EAAE,CAAC;MAC3C;;MAEA;MACA,IAAIC,OAAO,CAACU,mBAAmB,EAAE;QAC/BF,WAAW,CAACC,IAAI,CAAC,uBAAuBE,kBAAkB,CAACX,OAAO,CAACU,mBAAmB,CAAC,EAAE,CAAC;MAC5F;MAEA,IAAIV,OAAO,CAACY,SAAS,EAAE;QACrBJ,WAAW,CAACC,IAAI,CAAC,aAAaE,kBAAkB,CAACX,OAAO,CAACY,SAAS,CAAC,EAAE,CAAC;MACxE;MAEA,IAAIZ,OAAO,CAACa,OAAO,EAAE;QACnBL,WAAW,CAACC,IAAI,CAAC,WAAWE,kBAAkB,CAACX,OAAO,CAACa,OAAO,CAAC,EAAE,CAAC;QAClE,IAAIb,OAAO,CAACc,UAAU,EAAE;UACtBN,WAAW,CAACC,IAAI,CAAC,cAAcE,kBAAkB,CAACX,OAAO,CAACc,UAAU,CAAC,EAAE,CAAC;QAC1E;MACF;;MAEA;MACA,IAAIN,WAAW,CAACO,MAAM,GAAG,CAAC,EAAE;QAC1BR,GAAG,IAAI,IAAIC,WAAW,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE;MACpC;;MAEA;MACAf,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,GAAG,CAAC;MACrCN,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEM,WAAW,CAAC;MAE/CP,OAAO,CAACC,GAAG,CAAC,qBAAqBK,GAAG,EAAE,CAAC;MACvCN,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAC9EU,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAGvB,OAAO,GAAG4B,GAAG,EAAE,CAAC;MAEhD,IAAI;QACFN,OAAO,CAACC,GAAG,CAAC,kCAAkCK,GAAG,eAAejB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE,CAAC;QAC1HU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;UACtC,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC,CAAC;;QAEF;QACA,MAAM0B,QAAQ,GAAG,MAAMrC,aAAa,CAACsC,GAAG,CAACX,GAAG,EAAE;UAAEvB,OAAO,EAAE;QAAM,CAAC,CAAC;QAEjEiB,OAAO,CAACC,GAAG,CAAC,iBAAiBK,GAAG,EAAE,EAAEU,QAAQ,CAACE,IAAI,CAAC;QAClDlB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEe,QAAQ,CAACG,MAAM,CAAC;QACtDnB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEe,QAAQ,CAAClC,OAAO,CAAC;QAExD,IAAIsC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAAC,EAAE;UAChClB,OAAO,CAACC,GAAG,CAAC,4BAA4Be,QAAQ,CAACE,IAAI,CAACJ,MAAM,EAAE,CAAC;UAC/D,IAAIE,QAAQ,CAACE,IAAI,CAACJ,MAAM,GAAG,CAAC,EAAE;YAC5Bd,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEe,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,MAAM;YACLlB,OAAO,CAACsB,IAAI,CAAC,uCAAuCpB,aAAa,aAAaJ,QAAQ,EAAE,CAAC;UAC3F;QACF,CAAC,MAAM;UACLE,OAAO,CAACsB,IAAI,CAAC,4BAA4B,OAAON,QAAQ,CAACE,IAAI,EAAE,EAAEF,QAAQ,CAACE,IAAI,CAAC;QACjF;QAEA,OAAOF,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOK,QAAQ,EAAE;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QACjB3B,OAAO,CAACR,KAAK,CAAC,iCAAiCc,GAAG,GAAG,EAAEiB,QAAQ,CAAC;QAChEvB,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAE;UACpCoC,OAAO,EAAEL,QAAQ,CAACK,OAAO;UACzBT,MAAM,GAAAK,kBAAA,GAAED,QAAQ,CAACP,QAAQ,cAAAQ,kBAAA,uBAAjBA,kBAAA,CAAmBL,MAAM;UACjCU,UAAU,GAAAJ,mBAAA,GAAEF,QAAQ,CAACP,QAAQ,cAAAS,mBAAA,uBAAjBA,mBAAA,CAAmBI,UAAU;UACzCX,IAAI,GAAAQ,mBAAA,GAAEH,QAAQ,CAACP,QAAQ,cAAAU,mBAAA,uBAAjBA,mBAAA,CAAmBR,IAAI;UAC7BpC,OAAO,GAAA6C,mBAAA,GAAEJ,QAAQ,CAACP,QAAQ,cAAAW,mBAAA,uBAAjBA,mBAAA,CAAmB7C,OAAO;UACnCgD,IAAI,EAAEP,QAAQ,CAACO,IAAI;UACnBC,YAAY,EAAER,QAAQ,CAACQ,YAAY;UACnCtD,MAAM,EAAE8C,QAAQ,CAAC9C,MAAM,GAAG;YACxB6B,GAAG,EAAEiB,QAAQ,CAAC9C,MAAM,CAAC6B,GAAG;YACxB0B,MAAM,EAAET,QAAQ,CAAC9C,MAAM,CAACuD,MAAM;YAC9BjD,OAAO,EAAEwC,QAAQ,CAAC9C,MAAM,CAACM,OAAO;YAChCD,OAAO,EAAEyC,QAAQ,CAAC9C,MAAM,CAACK;UAC3B,CAAC,GAAG;QACN,CAAC,CAAC;;QAEF;QACA,IAAIyC,QAAQ,CAACO,IAAI,KAAK,aAAa,EAAE;UACnC9B,OAAO,CAACR,KAAK,CAAC,0EAA0E,CAAC;UACzF;UACA,IAAI;YACFQ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D,MAAMgC,YAAY,GAAG,MAAMC,KAAK,CAACxD,OAAO,CAAC;YACzCsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEgC,YAAY,CAACd,MAAM,CAAC;UACrE,CAAC,CAAC,OAAOgB,SAAS,EAAE;YAClBnC,OAAO,CAACR,KAAK,CAAC,yCAAyC,EAAE2C,SAAS,CAAC;UACrE;QACF;QAEA,MAAMZ,QAAQ;MAChB;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MAAA,IAAA4C,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACd1C,OAAO,CAACR,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCQ,OAAO,CAACR,KAAK,CAAC,gBAAgB,EAAE;QAC9BoC,OAAO,EAAEpC,KAAK,CAACoC,OAAO;QACtBT,MAAM,GAAAiB,eAAA,GAAE5C,KAAK,CAACwB,QAAQ,cAAAoB,eAAA,uBAAdA,eAAA,CAAgBjB,MAAM;QAC9BU,UAAU,GAAAQ,gBAAA,GAAE7C,KAAK,CAACwB,QAAQ,cAAAqB,gBAAA,uBAAdA,gBAAA,CAAgBR,UAAU;QACtCX,IAAI,GAAAoB,gBAAA,GAAE9C,KAAK,CAACwB,QAAQ,cAAAsB,gBAAA,uBAAdA,gBAAA,CAAgBpB,IAAI;QAC1BZ,GAAG,EAAE,SAAST,UAAU,GAAGC,QAAQ,KAAK,IAAI,GAAG,cAAcA,QAAQ,EAAE,GAAG,EAAE,EAAE;QAC9E6C,KAAK,EAAEnD,KAAK,CAACmD;MACf,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,IAAIvC,KAAK,CAAC,EAAAkC,gBAAA,GAAA/C,KAAK,CAACwB,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBK,MAAM,KAAIrD,KAAK,CAACoC,OAAO,IAAI,oBAAoB,CAAC;MACtGgB,aAAa,CAACzB,MAAM,IAAAsB,gBAAA,GAAGjD,KAAK,CAACwB,QAAQ,cAAAyB,gBAAA,uBAAdA,gBAAA,CAAgBtB,MAAM;MAC7CyB,aAAa,CAAC1B,IAAI,IAAAwB,gBAAA,GAAGlD,KAAK,CAACwB,QAAQ,cAAA0B,gBAAA,uBAAdA,gBAAA,CAAgBxB,IAAI;MACzC0B,aAAa,CAAC5B,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ;MACvC4B,aAAa,CAACE,aAAa,GAAGtD,KAAK;MACnCoD,aAAa,CAACd,IAAI,GAAGtC,KAAK,CAACsC,IAAI;MAC/Bc,aAAa,CAACb,YAAY,GAAGvC,KAAK,CAACuC,YAAY;MAE/C,MAAMa,aAAa;IACrB;EACF,CAAC;EAED;EACAG,UAAU,EAAE,MAAAA,CAAOlD,UAAU,EAAEmD,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAM9C,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACsE,IAAI,CAAC,SAAS/C,aAAa,EAAE,EAAE8C,QAAQ,CAAC;MAC7E,OAAOhC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF,CAAC;EAED;EACA0D,UAAU,EAAE,MAAAA,CAAOrD,UAAU,EAAEsD,MAAM,EAAEH,QAAQ,KAAK;IAClD,IAAI;MACF;MACA,MAAM9C,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACyE,GAAG,CAAC,SAASlD,aAAa,IAAIiD,MAAM,EAAE,EAAEH,QAAQ,CAAC;MACtF,OAAOhC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF,CAAC;EAED;EACA6D,oBAAoB,EAAE,MAAOxD,UAAU,IAAK;IAC1C,IAAI;MACF;MACA,MAAMK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACsC,GAAG,CAAC,SAASf,aAAa,qBAAqB,CAAC;MACrF,OAAOc,QAAQ,CAACE,IAAI,CAACoC,kBAAkB;IACzC,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED;EACA+D,eAAe,EAAE,MAAAA,CAAO1D,UAAU,EAAEsD,MAAM,EAAEK,KAAK,GAAG,KAAK,KAAK;IAC5D,IAAI;MACF;MACA,MAAMtD,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACsE,IAAI,CAAC,SAAS/C,aAAa,IAAIiD,MAAM,gBAAgB,EAAE;QAC1FK,KAAK,EAAEA;MACT,CAAC,CAAC;MACF,OAAOxC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF,CAAC;EAED;EACAiE,UAAU,EAAE,MAAAA,CAAO5D,UAAU,EAAEsD,MAAM,EAAEO,IAAI,GAAG,IAAI,KAAK;IACrD,IAAI;MACF;MACA,MAAMxD,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAMpB,MAAM,GAAG,CAAC,CAAC;MACjB,IAAIiF,IAAI,EAAE;QACRjF,MAAM,CAACkF,MAAM,GAAG;UAAED;QAAK,CAAC;MAC1B;MAEA,MAAM1C,QAAQ,GAAG,MAAMrC,aAAa,CAACiF,MAAM,CAAC,SAAS1D,aAAa,IAAIiD,MAAM,EAAE,EAAE1E,MAAM,CAAC;MACvF,OAAOuC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MAAA,IAAAqE,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACd/D,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CQ,OAAO,CAACR,KAAK,CAAC,gBAAgB,EAAE;QAC9BoC,OAAO,EAAEpC,KAAK,CAACoC,OAAO;QACtBT,MAAM,GAAA0C,gBAAA,GAAErE,KAAK,CAACwB,QAAQ,cAAA6C,gBAAA,uBAAdA,gBAAA,CAAgB1C,MAAM;QAC9BU,UAAU,GAAAiC,gBAAA,GAAEtE,KAAK,CAACwB,QAAQ,cAAA8C,gBAAA,uBAAdA,gBAAA,CAAgBjC,UAAU;QACtCX,IAAI,GAAA6C,gBAAA,GAAEvE,KAAK,CAACwB,QAAQ,cAAA+C,gBAAA,uBAAdA,gBAAA,CAAgB7C,IAAI;QAC1BZ,GAAG,EAAE,SAASJ,aAAa,IAAIiD,MAAM,EAAE;QACvC1E,MAAM,EAAEe,KAAK,CAACf;MAChB,CAAC,CAAC;;MAEF;MACA,IAAIe,KAAK,CAACwB,QAAQ,IAAIxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,EAAE;QACzC,MAAM1B,KAAK,CAACwB,QAAQ,CAACE,IAAI;MAC3B,CAAC,MAAM,IAAI1B,KAAK,CAACoC,OAAO,EAAE;QACxB,MAAM,IAAIvB,KAAK,CAACb,KAAK,CAACoC,OAAO,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAIvB,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF;EACF,CAAC;EAED;EACA2D,iBAAiB,EAAE,MAAAA,CAAOnE,UAAU,EAAEsD,MAAM,EAAEc,WAAW,KAAK;IAC5D,IAAI;MACF;MACA,MAAM/D,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACsE,IAAI,CAAC,SAAS/C,aAAa,IAAIiD,MAAM,eAAe,EAAE;QACzFe,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAOjD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF,CAAC;EAED;EACA2E,YAAY,EAAE,MAAAA,CAAOtE,UAAU,EAAEsD,MAAM,EAAEiB,QAAQ,KAAK;IACpD,IAAI;MACF;MACA,MAAMlE,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACsE,IAAI,CAAC,SAAS/C,aAAa,IAAIiD,MAAM,SAAS,EAAE;QACnFkB,SAAS,EAAED;MACb,CAAC,CAAC;MACF,OAAOpD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF,CAAC;EAED;EACA8E,iBAAiB,EAAE,MAAOzE,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAMK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACsC,GAAG,CAAC,SAASf,aAAa,aAAa,CAAC;MAC7E,OAAOc,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF,CAAC;EAED;EACA+E,YAAY,EAAE,MAAO1E,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACsC,GAAG,CAAC,SAASf,aAAa,QAAQ,CAAC;MACxE,OAAOc,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF,CAAC;EAED;EACAgF,WAAW,EAAE,MAAAA,CAAO3E,UAAU,EAAEsD,MAAM,EAAEsB,IAAI,EAAEC,YAAY,KAAK;IAC7D,IAAI;MACF;MACA,MAAMxE,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACsE,IAAI,CAAC,SAAS/C,aAAa,IAAIiD,MAAM,eAAe,EAAE;QACzFsB,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY,IAAI;MAChC,CAAC,CAAC;MACF,OAAO1D,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF,CAAC;EAED;EACAmF,YAAY,EAAE,MAAAA,CAAO9E,UAAU,EAAEsD,MAAM,EAAEsB,IAAI,KAAK;IAChD,IAAI;MACF;MACA,MAAMvE,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmB,QAAQ,GAAG,MAAMrC,aAAa,CAACiF,MAAM,CAAC,SAAS1D,aAAa,IAAIiD,MAAM,iBAAiBsB,IAAI,EAAE,CAAC;MACpG,OAAOzD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,CAACE,IAAI,GAAG1B,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}