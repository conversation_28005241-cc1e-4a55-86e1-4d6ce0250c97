{"ast": null, "code": "function buildLocalizeTokenFn(schema) {\n  return (count, options) => {\n    if (count === 1) {\n      if (options?.addSuffix) {\n        return schema.one[0].replace(\"{{time}}\", schema.one[2]);\n      } else {\n        return schema.one[0].replace(\"{{time}}\", schema.one[1]);\n      }\n    } else {\n      const rem = count % 10 === 1 && count % 100 !== 11;\n      if (options?.addSuffix) {\n        return schema.other[0].replace(\"{{time}}\", rem ? schema.other[3] : schema.other[4]).replace(\"{{count}}\", String(count));\n      } else {\n        return schema.other[0].replace(\"{{time}}\", rem ? schema.other[1] : schema.other[2]).replace(\"{{count}}\", String(count));\n      }\n    }\n  };\n}\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    one: [\"mazāk par {{time}}\", \"sekundi\", \"sekundi\"],\n    other: [\"mazāk nekā {{count}} {{time}}\", \"sekunde\", \"sekundes\", \"sekundes\", \"sekundēm\"]\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"sekunde\", \"sekundes\"],\n    other: [\"{{count}} {{time}}\", \"sekunde\", \"sekundes\", \"sekundes\", \"sekundēm\"]\n  }),\n  halfAMinute: (_count, options) => {\n    if (options?.addSuffix) {\n      return \"pusminūtes\";\n    } else {\n      return \"pusminūte\";\n    }\n  },\n  lessThanXMinutes: buildLocalizeTokenFn({\n    one: [\"mazāk par {{time}}\", \"minūti\", \"minūti\"],\n    other: [\"mazāk nekā {{count}} {{time}}\", \"minūte\", \"minūtes\", \"minūtes\", \"minūtēm\"]\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"minūte\", \"minūtes\"],\n    other: [\"{{count}} {{time}}\", \"minūte\", \"minūtes\", \"minūtes\", \"minūtēm\"]\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    one: [\"apmēram 1 {{time}}\", \"stunda\", \"stundas\"],\n    other: [\"apmēram {{count}} {{time}}\", \"stunda\", \"stundas\", \"stundas\", \"stundām\"]\n  }),\n  xHours: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"stunda\", \"stundas\"],\n    other: [\"{{count}} {{time}}\", \"stunda\", \"stundas\", \"stundas\", \"stundām\"]\n  }),\n  xDays: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"diena\", \"dienas\"],\n    other: [\"{{count}} {{time}}\", \"diena\", \"dienas\", \"dienas\", \"dienām\"]\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    one: [\"apmēram 1 {{time}}\", \"nedēļa\", \"nedēļas\"],\n    other: [\"apmēram {{count}} {{time}}\", \"nedēļa\", \"nedēļu\", \"nedēļas\", \"nedēļām\"]\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"nedēļa\", \"nedēļas\"],\n    other: [\"{{count}} {{time}}\",\n    // TODO\n    \"nedēļa\", \"nedēļu\", \"nedēļas\", \"nedēļām\"]\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    one: [\"apmēram 1 {{time}}\", \"mēnesis\", \"mēneša\"],\n    other: [\"apmēram {{count}} {{time}}\", \"mēnesis\", \"mēneši\", \"mēneša\", \"mēnešiem\"]\n  }),\n  xMonths: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"mēnesis\", \"mēneša\"],\n    other: [\"{{count}} {{time}}\", \"mēnesis\", \"mēneši\", \"mēneša\", \"mēnešiem\"]\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    one: [\"apmēram 1 {{time}}\", \"gads\", \"gada\"],\n    other: [\"apmēram {{count}} {{time}}\", \"gads\", \"gadi\", \"gada\", \"gadiem\"]\n  }),\n  xYears: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"gads\", \"gada\"],\n    other: [\"{{count}} {{time}}\", \"gads\", \"gadi\", \"gada\", \"gadiem\"]\n  }),\n  overXYears: buildLocalizeTokenFn({\n    one: [\"ilgāk par 1 {{time}}\", \"gadu\", \"gadu\"],\n    other: [\"vairāk nekā {{count}} {{time}}\", \"gads\", \"gadi\", \"gada\", \"gadiem\"]\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    one: [\"gandrīz 1 {{time}}\", \"gads\", \"gada\"],\n    other: [\"vairāk nekā {{count}} {{time}}\", \"gads\", \"gadi\", \"gada\", \"gadiem\"]\n  })\n};\nexport const formatDistance = (token, count, options) => {\n  const result = formatDistanceLocale[token](count, options);\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"pēc \" + result;\n    } else {\n      return \"pirms \" + result;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["buildLocalizeTokenFn", "schema", "count", "options", "addSuffix", "one", "replace", "rem", "other", "String", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "_count", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "result", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/lv/_lib/formatDistance.js"], "sourcesContent": ["function buildLocalizeTokenFn(schema) {\n  return (count, options) => {\n    if (count === 1) {\n      if (options?.addSuffix) {\n        return schema.one[0].replace(\"{{time}}\", schema.one[2]);\n      } else {\n        return schema.one[0].replace(\"{{time}}\", schema.one[1]);\n      }\n    } else {\n      const rem = count % 10 === 1 && count % 100 !== 11;\n      if (options?.addSuffix) {\n        return schema.other[0]\n          .replace(\"{{time}}\", rem ? schema.other[3] : schema.other[4])\n          .replace(\"{{count}}\", String(count));\n      } else {\n        return schema.other[0]\n          .replace(\"{{time}}\", rem ? schema.other[1] : schema.other[2])\n          .replace(\"{{count}}\", String(count));\n      }\n    }\n  };\n}\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    one: [\"mazāk par {{time}}\", \"sekundi\", \"sekundi\"],\n    other: [\n      \"mazāk nekā {{count}} {{time}}\",\n      \"sekunde\",\n      \"sekundes\",\n      \"sekundes\",\n      \"sekundēm\",\n    ],\n  }),\n\n  xSeconds: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"sekunde\", \"sekundes\"],\n    other: [\n      \"{{count}} {{time}}\",\n      \"sekunde\",\n      \"sekundes\",\n      \"sekundes\",\n      \"sekundēm\",\n    ],\n  }),\n\n  halfAMinute: (_count, options) => {\n    if (options?.addSuffix) {\n      return \"pusminūtes\";\n    } else {\n      return \"pusminūte\";\n    }\n  },\n\n  lessThanXMinutes: buildLocalizeTokenFn({\n    one: [\"mazāk par {{time}}\", \"minūti\", \"minūti\"],\n    other: [\n      \"mazāk nekā {{count}} {{time}}\",\n      \"minūte\",\n      \"minūtes\",\n      \"minūtes\",\n      \"minūtēm\",\n    ],\n  }),\n\n  xMinutes: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"minūte\", \"minūtes\"],\n    other: [\"{{count}} {{time}}\", \"minūte\", \"minūtes\", \"minūtes\", \"minūtēm\"],\n  }),\n\n  aboutXHours: buildLocalizeTokenFn({\n    one: [\"apmēram 1 {{time}}\", \"stunda\", \"stundas\"],\n    other: [\n      \"apmēram {{count}} {{time}}\",\n      \"stunda\",\n      \"stundas\",\n      \"stundas\",\n      \"stundām\",\n    ],\n  }),\n\n  xHours: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"stunda\", \"stundas\"],\n    other: [\"{{count}} {{time}}\", \"stunda\", \"stundas\", \"stundas\", \"stundām\"],\n  }),\n\n  xDays: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"diena\", \"dienas\"],\n    other: [\"{{count}} {{time}}\", \"diena\", \"dienas\", \"dienas\", \"dienām\"],\n  }),\n\n  aboutXWeeks: buildLocalizeTokenFn({\n    one: [\"apmēram 1 {{time}}\", \"nedēļa\", \"nedēļas\"],\n    other: [\n      \"apmēram {{count}} {{time}}\",\n      \"nedēļa\",\n      \"nedēļu\",\n      \"nedēļas\",\n      \"nedēļām\",\n    ],\n  }),\n\n  xWeeks: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"nedēļa\", \"nedēļas\"],\n    other: [\n      \"{{count}} {{time}}\", // TODO\n      \"nedēļa\",\n      \"nedēļu\",\n      \"nedēļas\",\n      \"nedēļām\",\n    ],\n  }),\n\n  aboutXMonths: buildLocalizeTokenFn({\n    one: [\"apmēram 1 {{time}}\", \"mēnesis\", \"mēneša\"],\n    other: [\n      \"apmēram {{count}} {{time}}\",\n      \"mēnesis\",\n      \"mēneši\",\n      \"mēneša\",\n      \"mēnešiem\",\n    ],\n  }),\n\n  xMonths: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"mēnesis\", \"mēneša\"],\n    other: [\"{{count}} {{time}}\", \"mēnesis\", \"mēneši\", \"mēneša\", \"mēnešiem\"],\n  }),\n\n  aboutXYears: buildLocalizeTokenFn({\n    one: [\"apmēram 1 {{time}}\", \"gads\", \"gada\"],\n    other: [\"apmēram {{count}} {{time}}\", \"gads\", \"gadi\", \"gada\", \"gadiem\"],\n  }),\n\n  xYears: buildLocalizeTokenFn({\n    one: [\"1 {{time}}\", \"gads\", \"gada\"],\n    other: [\"{{count}} {{time}}\", \"gads\", \"gadi\", \"gada\", \"gadiem\"],\n  }),\n\n  overXYears: buildLocalizeTokenFn({\n    one: [\"ilgāk par 1 {{time}}\", \"gadu\", \"gadu\"],\n    other: [\"vairāk nekā {{count}} {{time}}\", \"gads\", \"gadi\", \"gada\", \"gadiem\"],\n  }),\n\n  almostXYears: buildLocalizeTokenFn({\n    one: [\"gandrīz 1 {{time}}\", \"gads\", \"gada\"],\n    other: [\"vairāk nekā {{count}} {{time}}\", \"gads\", \"gadi\", \"gada\", \"gadiem\"],\n  }),\n};\n\nexport const formatDistance = (token, count, options) => {\n  const result = formatDistanceLocale[token](count, options);\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"pēc \" + result;\n    } else {\n      return \"pirms \" + result;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,SAASA,oBAAoBA,CAACC,MAAM,EAAE;EACpC,OAAO,CAACC,KAAK,EAAEC,OAAO,KAAK;IACzB,IAAID,KAAK,KAAK,CAAC,EAAE;MACf,IAAIC,OAAO,EAAEC,SAAS,EAAE;QACtB,OAAOH,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU,EAAEL,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,MAAM;QACL,OAAOJ,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU,EAAEL,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;MACzD;IACF,CAAC,MAAM;MACL,MAAME,GAAG,GAAGL,KAAK,GAAG,EAAE,KAAK,CAAC,IAAIA,KAAK,GAAG,GAAG,KAAK,EAAE;MAClD,IAAIC,OAAO,EAAEC,SAAS,EAAE;QACtB,OAAOH,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CACnBF,OAAO,CAAC,UAAU,EAAEC,GAAG,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,GAAGP,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAC5DF,OAAO,CAAC,WAAW,EAAEG,MAAM,CAACP,KAAK,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,OAAOD,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CACnBF,OAAO,CAAC,UAAU,EAAEC,GAAG,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,GAAGP,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAC5DF,OAAO,CAAC,WAAW,EAAEG,MAAM,CAACP,KAAK,CAAC,CAAC;MACxC;IACF;EACF,CAAC;AACH;AAEA,MAAMQ,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAEX,oBAAoB,CAAC;IACrCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,SAAS,CAAC;IACjDG,KAAK,EAAE,CACL,+BAA+B,EAC/B,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU;EAEd,CAAC,CAAC;EAEFI,QAAQ,EAAEZ,oBAAoB,CAAC;IAC7BK,GAAG,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,UAAU,CAAC;IAC1CG,KAAK,EAAE,CACL,oBAAoB,EACpB,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU;EAEd,CAAC,CAAC;EAEFK,WAAW,EAAEA,CAACC,MAAM,EAAEX,OAAO,KAAK;IAChC,IAAIA,OAAO,EAAEC,SAAS,EAAE;MACtB,OAAO,YAAY;IACrB,CAAC,MAAM;MACL,OAAO,WAAW;IACpB;EACF,CAAC;EAEDW,gBAAgB,EAAEf,oBAAoB,CAAC;IACrCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAC/CG,KAAK,EAAE,CACL,+BAA+B,EAC/B,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS;EAEb,CAAC,CAAC;EAEFQ,QAAQ,EAAEhB,oBAAoB,CAAC;IAC7BK,GAAG,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;IACxCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;EACzE,CAAC,CAAC;EAEFS,WAAW,EAAEjB,oBAAoB,CAAC;IAChCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,SAAS,CAAC;IAChDG,KAAK,EAAE,CACL,4BAA4B,EAC5B,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS;EAEb,CAAC,CAAC;EAEFU,MAAM,EAAElB,oBAAoB,CAAC;IAC3BK,GAAG,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;IACxCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;EACzE,CAAC,CAAC;EAEFW,KAAK,EAAEnB,oBAAoB,CAAC;IAC1BK,GAAG,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;IACtCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;EACrE,CAAC,CAAC;EAEFY,WAAW,EAAEpB,oBAAoB,CAAC;IAChCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,SAAS,CAAC;IAChDG,KAAK,EAAE,CACL,4BAA4B,EAC5B,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS;EAEb,CAAC,CAAC;EAEFa,MAAM,EAAErB,oBAAoB,CAAC;IAC3BK,GAAG,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;IACxCG,KAAK,EAAE,CACL,oBAAoB;IAAE;IACtB,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS;EAEb,CAAC,CAAC;EAEFc,YAAY,EAAEtB,oBAAoB,CAAC;IACjCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,QAAQ,CAAC;IAChDG,KAAK,EAAE,CACL,4BAA4B,EAC5B,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU;EAEd,CAAC,CAAC;EAEFe,OAAO,EAAEvB,oBAAoB,CAAC;IAC5BK,GAAG,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC;IACxCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU;EACzE,CAAC,CAAC;EAEFgB,WAAW,EAAExB,oBAAoB,CAAC;IAChCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3CG,KAAK,EAAE,CAAC,4BAA4B,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;EACxE,CAAC,CAAC;EAEFiB,MAAM,EAAEzB,oBAAoB,CAAC;IAC3BK,GAAG,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC;IACnCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;EAChE,CAAC,CAAC;EAEFkB,UAAU,EAAE1B,oBAAoB,CAAC;IAC/BK,GAAG,EAAE,CAAC,sBAAsB,EAAE,MAAM,EAAE,MAAM,CAAC;IAC7CG,KAAK,EAAE,CAAC,gCAAgC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;EAC5E,CAAC,CAAC;EAEFmB,YAAY,EAAE3B,oBAAoB,CAAC;IACjCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3CG,KAAK,EAAE,CAAC,gCAAgC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;EAC5E,CAAC;AACH,CAAC;AAED,OAAO,MAAMoB,cAAc,GAAGA,CAACC,KAAK,EAAE3B,KAAK,EAAEC,OAAO,KAAK;EACvD,MAAM2B,MAAM,GAAGpB,oBAAoB,CAACmB,KAAK,CAAC,CAAC3B,KAAK,EAAEC,OAAO,CAAC;EAE1D,IAAIA,OAAO,EAAEC,SAAS,EAAE;IACtB,IAAID,OAAO,CAAC4B,UAAU,IAAI5B,OAAO,CAAC4B,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,MAAM,GAAGD,MAAM;IACxB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}