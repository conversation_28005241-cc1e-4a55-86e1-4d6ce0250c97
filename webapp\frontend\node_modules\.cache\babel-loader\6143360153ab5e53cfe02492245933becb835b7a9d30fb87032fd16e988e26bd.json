{"ast": null, "code": "export default function count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}", "map": {"version": 3, "names": ["count", "values", "valueof", "undefined", "value", "index"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-array/src/count.js"], "sourcesContent": ["export default function count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n"], "mappings": "AAAA,eAAe,SAASA,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC7C,IAAIF,KAAK,GAAG,CAAC;EACb,IAAIE,OAAO,KAAKC,SAAS,EAAE;IACzB,KAAK,IAAIC,KAAK,IAAIH,MAAM,EAAE;MACxB,IAAIG,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAC9C,EAAEJ,KAAK;MACT;IACF;EACF,CAAC,MAAM;IACL,IAAIK,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAID,KAAK,IAAIH,MAAM,EAAE;MACxB,IAAI,CAACG,KAAK,GAAGF,OAAO,CAACE,KAAK,EAAE,EAAEC,KAAK,EAAEJ,MAAM,CAAC,KAAK,IAAI,IAAI,CAACG,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAClF,EAAEJ,KAAK;MACT;IACF;EACF;EACA,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}