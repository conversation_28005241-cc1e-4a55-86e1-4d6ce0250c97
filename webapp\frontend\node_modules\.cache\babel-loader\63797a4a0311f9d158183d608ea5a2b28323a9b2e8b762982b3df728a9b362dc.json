{"ast": null, "code": "// Same as fr\nimport { formatDistance } from \"./fr/_lib/formatDistance.js\";\nimport { localize } from \"./fr/_lib/localize.js\";\nimport { match } from \"./fr/_lib/match.js\";\n\n// Unique for fr-CH\nimport { formatLong } from \"./fr-CH/_lib/formatLong.js\";\nimport { formatRelative } from \"./fr-CH/_lib/formatRelative.js\";\n\n/**\n * @category Locales\n * @summary French locale (Switzerland).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau](https://github.com/izeau)\n * <AUTHOR> [@fbonzon](https://github.com/fbonzon)\n * <AUTHOR> [@vanvuongngo](https://github.com/vanvuongngo)\n * <AUTHOR> [@dcbn](https://github.com/dcbn)\n */\nexport const frCH = {\n  code: \"fr-CH\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default frCH;", "map": {"version": 3, "names": ["formatDistance", "localize", "match", "formatLong", "formatRelative", "frCH", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/fr-CH.js"], "sourcesContent": ["// Same as fr\nimport { formatDistance } from \"./fr/_lib/formatDistance.js\";\nimport { localize } from \"./fr/_lib/localize.js\";\nimport { match } from \"./fr/_lib/match.js\";\n\n// Unique for fr-CH\nimport { formatLong } from \"./fr-CH/_lib/formatLong.js\";\nimport { formatRelative } from \"./fr-CH/_lib/formatRelative.js\";\n\n/**\n * @category Locales\n * @summary French locale (Switzerland).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau](https://github.com/izeau)\n * <AUTHOR> [@fbonzon](https://github.com/fbonzon)\n * <AUTHOR> [@vanvuongngo](https://github.com/vanvuongngo)\n * <AUTHOR> [@dcbn](https://github.com/dcbn)\n */\nexport const frCH = {\n  code: \"fr-CH\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default frCH;\n"], "mappings": "AAAA;AACA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,gCAAgC;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BG,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BH,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZK,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}