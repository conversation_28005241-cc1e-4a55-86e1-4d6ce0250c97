{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Load reports that don't need additional parameters\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([reportService.getProgressReport(cantiereId, 'video'), reportService.getBillOfQuantities(cantiereId, 'video'), reportService.getBobineReport(cantiereId, 'video'), reportService.getCaviStatoReport(cantiereId, 'video')]);\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n        setError(null);\n      } catch (err) {\n        console.error('Error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(), dialogType === 'boq' && renderBoqReport(), dialogType === 'bobine' && renderBobineReport(), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(), dialogType === 'posa-periodo' && renderPosaPeriodoReport(), dialogType === 'cavi-stato' && renderCaviStatoReport()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Avanzamento Generale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_posati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_da_posare, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 46\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Avanzamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.percentuale_avanzamento, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Totale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.totale_cavi\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Cavi Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.cavi_posati\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Cavi Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.cavi_rimanenti\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Percentuale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 47\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.media_giornaliera, \"m/giorno\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 48\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this), data.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Giorni Stimati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.giorni_stimati, \" giorni\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Data Completamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: data.data_completamento\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Posa Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Typography, {\n                children: [posa.data, \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [posa.metri, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 36\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => {\n    var _data$cavi_per_tipo, _data$bobine_per_tipo;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Cavi per Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$cavi_per_tipo = data.cavi_per_tipo) === null || _data$cavi_per_tipo === void 0 ? void 0 : _data$cavi_per_tipo.map((cavo, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: cavo.tipologia\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [\"Sezione: \", cavo.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Cavi: \", cavo.num_cavi]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Teorici: \", cavo.metri_teorici, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Reali: \", cavo.metri_reali, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Da Posare: \", cavo.metri_da_posare, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Bobine Disponibili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$bobine_per_tipo = data.bobine_per_tipo) === null || _data$bobine_per_tipo === void 0 ? void 0 : _data$bobine_per_tipo.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: bobina.tipologia\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [\"Sezione: \", bobina.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Bobine: \", bobina.num_bobine]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Disponibili: \", bobina.metri_disponibili, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobineReport = data => {\n    var _data$bobine;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"Bobine del Cantiere (\", data.totale_bobine, \" totali)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$bobine = data.bobine) === null || _data$bobine === void 0 ? void 0 : _data$bobine.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: bobina.id_bobina\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.tipologia, \" - \", bobina.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato,\n                      color: bobina.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                      size: \"small\",\n                      sx: {\n                        mb: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Totali: \", bobina.metri_totali, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Residui: \", bobina.metri_residui, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Utilizzati: \", bobina.metri_utilizzati, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Utilizzo: \", bobina.percentuale_utilizzo, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobinaSpecificaReport = data => {\n    var _data$bobina, _data$bobina2, _data$bobina3, _data$bobina4, _data$bobina5, _data$bobina6, _data$bobina7, _data$bobina8, _data$bobina9, _data$cavi_associati;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Dettagli Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"ID: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina = data.bobina) === null || _data$bobina === void 0 ? void 0 : _data$bobina.id_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Tipologia: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina2 = data.bobina) === null || _data$bobina2 === void 0 ? void 0 : _data$bobina2.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Sezione: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina3 = data.bobina) === null || _data$bobina3 === void 0 ? void 0 : _data$bobina3.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: (_data$bobina4 = data.bobina) === null || _data$bobina4 === void 0 ? void 0 : _data$bobina4.stato,\n                  color: ((_data$bobina5 = data.bobina) === null || _data$bobina5 === void 0 ? void 0 : _data$bobina5.stato) === 'DISPONIBILE' ? 'success' : 'warning',\n                  sx: {\n                    my: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina6 = data.bobina) === null || _data$bobina6 === void 0 ? void 0 : _data$bobina6.metri_totali, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 43\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Residui: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina7 = data.bobina) === null || _data$bobina7 === void 0 ? void 0 : _data$bobina7.metri_residui, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Utilizzati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina8 = data.bobina) === null || _data$bobina8 === void 0 ? void 0 : _data$bobina8.metri_utilizzati, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 47\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Utilizzo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina9 = data.bobina) === null || _data$bobina9 === void 0 ? void 0 : _data$bobina9.percentuale_utilizzo, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 39\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"Cavi Associati (\", data.totale_cavi, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  },\n                  children: (_data$cavi_associati = data.cavi_associati) === null || _data$cavi_associati === void 0 ? void 0 : _data$cavi_associati.map((cavo, index) => /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mb: 1,\n                      p: 1,\n                      border: '1px solid #e0e0e0',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: cavo.id_cavo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 543,\n                        columnNumber: 51\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [cavo.sistema, \" - \", cavo.utility, \" - \", cavo.tipologia]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      display: \"block\",\n                      children: [\"Teorici: \", cavo.metri_teorici, \"m | Reali: \", cavo.metri_reali, \"m | Stato: \", cavo.stato]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 5\n    }, this);\n  };\n  const renderPosaPeriodoReport = data => {\n    var _data$posa_giornalier;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Statistiche Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Periodo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.data_inizio, \" - \", data.data_fine]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Totale Metri: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.totale_metri_periodo, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 43\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Giorni Attivi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: data.giorni_attivi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.media_giornaliera, \"m/giorno\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 48\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Posa Giornaliera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  },\n                  children: (_data$posa_giornalier = data.posa_giornaliera) === null || _data$posa_giornalier === void 0 ? void 0 : _data$posa_giornalier.map((posa, index) => /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      py: 0.5\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      children: posa.data\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [posa.metri, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 593,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 5\n    }, this);\n  };\n  const renderCaviStatoReport = data => {\n    var _reportData$cavi_per_;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Cavi per Stato di Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_reportData$cavi_per_ = reportData.cavi_per_stato) === null || _reportData$cavi_per_ === void 0 ? void 0 : _reportData$cavi_per_.map((stato, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      gutterBottom: true,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: stato.stato,\n                        color: stato.stato === 'Installato' ? 'success' : 'warning',\n                        sx: {\n                          mb: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 619,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Numero Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: stato.num_cavi\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 625,\n                        columnNumber: 48\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Teorici: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [stato.metri_teorici, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 626,\n                        columnNumber: 50\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Reali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [stato.metri_reali, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 48\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 5\n    }, this);\n  };\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 640,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(-1),\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Report e Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 723,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 3\n      },\n      children: \"Seleziona il tipo di report che desideri generare. Ogni report offre analisi specifiche per monitorare l'avanzamento del progetto e ottimizzare le operazioni.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: reportTypes.map(report => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            cursor: 'pointer',\n            transition: 'transform 0.2s, box-shadow 0.2s',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 4\n            }\n          },\n          onClick: () => handleReportSelect(report),\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  color: `${report.color}.main`,\n                  mr: 2\n                },\n                children: report.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h2\",\n                children: report.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 2\n              },\n              children: report.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: report.features.map((feature, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: feature,\n                size: \"small\",\n                variant: \"outlined\",\n                sx: {\n                  mr: 0.5,\n                  mb: 0.5\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: report.color,\n              startIcon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 30\n              }, this),\n              fullWidth: true,\n              children: \"Genera Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 13\n        }, this)\n      }, report.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 7\n    }, this), renderReportContent(), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 721,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"8wJhDlMpstDJI8p20f2IqC2nUho=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "loadAllReports", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "getProgressReport", "getBillOfQuantities", "getBobineReport", "getCaviStatoReport", "content", "err", "console", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "data", "container", "spacing", "item", "xs", "defaultExpanded", "expandIcon", "metri_totali", "metri_posati", "metri_da_posare", "percentuale_avanzamento", "totale_cavi", "cavi_posati", "cavi_rimanenti", "percentuale_cavi", "media_giornaliera", "giorni_stimati", "data_completamento", "posa_recente", "length", "slice", "map", "posa", "index", "metri", "_data$cavi_per_tipo", "_data$bobine_per_tipo", "cavi_per_tipo", "cavo", "md", "lg", "tipologia", "sezione", "num_cavi", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "bobina", "num_bobine", "metri_disponibili", "_data$bobine", "totale_bobine", "label", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "_data$bobina", "_data$bobina2", "_data$bobina3", "_data$bobina4", "_data$bobina5", "_data$bobina6", "_data$bobina7", "_data$bobina8", "_data$bobina9", "_data$cavi_associati", "my", "maxHeight", "overflow", "cavi_associati", "border", "borderRadius", "id_cavo", "sistema", "utility", "_data$posa_giornalier", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "py", "_reportData$cavi_per_", "cavi_per_stato", "gutterBottom", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "onChange", "e", "target", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "component", "report", "height", "flexDirection", "cursor", "transition", "transform", "boxShadow", "flexGrow", "mr", "feature", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  Bar<PERSON>hart as BarChartIcon,\n  <PERSON>Chart as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Load reports that don't need additional parameters\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          reportService.getProgressReport(cantiereId, 'video'),\n          reportService.getBillOfQuantities(cantiereId, 'video'),\n          reportService.getBobineReport(cantiereId, 'video'),\n          reportService.getCaviStatoReport(cantiereId, 'video')\n        ]);\n\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        setError(null);\n      } catch (err) {\n        console.error('Error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport()}\n        {dialogType === 'boq' && renderBoqReport()}\n        {dialogType === 'bobine' && renderBobineReport()}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport()}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport()}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport()}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Avanzamento Generale</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Metri Totali: <strong>{data.metri_totali}m</strong></Typography>\n                <Typography>Metri Posati: <strong>{data.metri_posati}m</strong></Typography>\n                <Typography>Metri Rimanenti: <strong>{data.metri_da_posare}m</strong></Typography>\n                <Typography>Avanzamento: <strong>{data.percentuale_avanzamento}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Totale Cavi: <strong>{data.totale_cavi}</strong></Typography>\n                <Typography>Cavi Posati: <strong>{data.cavi_posati}</strong></Typography>\n                <Typography>Cavi Rimanenti: <strong>{data.cavi_rimanenti}</strong></Typography>\n                <Typography>Percentuale Cavi: <strong>{data.percentuale_cavi}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Performance</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Media Giornaliera: <strong>{data.media_giornaliera}m/giorno</strong></Typography>\n                {data.giorni_stimati && (\n                  <>\n                    <Typography>Giorni Stimati: <strong>{data.giorni_stimati} giorni</strong></Typography>\n                    <Typography>Data Completamento: <strong>{data.data_completamento}</strong></Typography>\n                  </>\n                )}\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Grid item xs={12}>\n          <Accordion defaultExpanded>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">Posa Recente</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Card>\n                <CardContent>\n                  {data.posa_recente.slice(0, 5).map((posa, index) => (\n                    <Typography key={index}>\n                      {posa.data}: <strong>{posa.metri}m</strong>\n                    </Typography>\n                  ))}\n                </CardContent>\n              </Card>\n            </AccordionDetails>\n          </Accordion>\n        </Grid>\n      )}\n    </Grid>\n  );\n\n  const renderBoqReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Tipologia</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.cavi_per_tipo?.map((cavo, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{cavo.tipologia}</Typography>\n                      <Typography variant=\"body2\">Sezione: {cavo.sezione}</Typography>\n                      <Typography>Cavi: {cavo.num_cavi}</Typography>\n                      <Typography>Metri Teorici: {cavo.metri_teorici}m</Typography>\n                      <Typography>Metri Reali: {cavo.metri_reali}m</Typography>\n                      <Typography>Da Posare: {cavo.metri_da_posare}m</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Bobine Disponibili</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.bobine_per_tipo?.map((bobina, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{bobina.tipologia}</Typography>\n                      <Typography variant=\"body2\">Sezione: {bobina.sezione}</Typography>\n                      <Typography>Bobine: {bobina.num_bobine}</Typography>\n                      <Typography>Metri Disponibili: {bobina.metri_disponibili}m</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobineReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Bobine del Cantiere ({data.totale_bobine} totali)\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.bobine?.map((bobina, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{bobina.id_bobina}</Typography>\n                      <Typography variant=\"body2\">{bobina.tipologia} - {bobina.sezione}</Typography>\n                      <Chip\n                        label={bobina.stato}\n                        color={bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                        size=\"small\"\n                        sx={{ mb: 1 }}\n                      />\n                      <Typography>Metri Totali: {bobina.metri_totali}m</Typography>\n                      <Typography>Metri Residui: {bobina.metri_residui}m</Typography>\n                      <Typography>Metri Utilizzati: {bobina.metri_utilizzati}m</Typography>\n                      <Typography>Utilizzo: {bobina.percentuale_utilizzo}%</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobinaSpecificaReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Dettagli Bobina</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>ID: <strong>{data.bobina?.id_bobina}</strong></Typography>\n                <Typography>Tipologia: <strong>{data.bobina?.tipologia}</strong></Typography>\n                <Typography>Sezione: <strong>{data.bobina?.sezione}</strong></Typography>\n                <Chip\n                  label={data.bobina?.stato}\n                  color={data.bobina?.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                  sx={{ my: 1 }}\n                />\n                <Typography>Metri Totali: <strong>{data.bobina?.metri_totali}m</strong></Typography>\n                <Typography>Metri Residui: <strong>{data.bobina?.metri_residui}m</strong></Typography>\n                <Typography>Metri Utilizzati: <strong>{data.bobina?.metri_utilizzati}m</strong></Typography>\n                <Typography>Utilizzo: <strong>{data.bobina?.percentuale_utilizzo}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Cavi Associati ({data.totale_cavi})\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n                  {data.cavi_associati?.map((cavo, index) => (\n                    <Box key={index} sx={{ mb: 1, p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                      <Typography variant=\"body2\"><strong>{cavo.id_cavo}</strong></Typography>\n                      <Typography variant=\"caption\">\n                        {cavo.sistema} - {cavo.utility} - {cavo.tipologia}\n                      </Typography>\n                      <Typography variant=\"caption\" display=\"block\">\n                        Teorici: {cavo.metri_teorici}m | Reali: {cavo.metri_reali}m | Stato: {cavo.stato}\n                      </Typography>\n                    </Box>\n                  ))}\n                </Box>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderPosaPeriodoReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Statistiche Periodo</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Periodo: <strong>{data.data_inizio} - {data.data_fine}</strong></Typography>\n                <Typography>Totale Metri: <strong>{data.totale_metri_periodo}m</strong></Typography>\n                <Typography>Giorni Attivi: <strong>{data.giorni_attivi}</strong></Typography>\n                <Typography>Media Giornaliera: <strong>{data.media_giornaliera}m/giorno</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Posa Giornaliera</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n                  {data.posa_giornaliera?.map((posa, index) => (\n                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>\n                      <Typography>{posa.data}</Typography>\n                      <Typography><strong>{posa.metri}m</strong></Typography>\n                    </Box>\n                  ))}\n                </Box>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderCaviStatoReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Stato di Installazione</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {reportData.cavi_per_stato?.map((stato, index) => (\n                <Grid item xs={12} md={6} lg={3} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"h6\" gutterBottom>\n                        <Chip\n                          label={stato.stato}\n                          color={stato.stato === 'Installato' ? 'success' : 'warning'}\n                          sx={{ mb: 1 }}\n                        />\n                      </Typography>\n                      <Typography>Numero Cavi: <strong>{stato.num_cavi}</strong></Typography>\n                      <Typography>Metri Teorici: <strong>{stato.metri_teorici}m</strong></Typography>\n                      <Typography>Metri Reali: <strong>{stato.metri_reali}m</strong></Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report e Analytics\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Descrizione */}\n      <Alert severity=\"info\" sx={{ mb: 3 }}>\n        Seleziona il tipo di report che desideri generare. Ogni report offre analisi specifiche\n        per monitorare l'avanzamento del progetto e ottimizzare le operazioni.\n      </Alert>\n\n      {/* Griglia dei report */}\n      <Grid container spacing={3}>\n        {reportTypes.map((report) => (\n          <Grid item xs={12} md={6} lg={4} key={report.id}>\n            <Card\n              sx={{\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                cursor: 'pointer',\n                transition: 'transform 0.2s, box-shadow 0.2s',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                  boxShadow: 4\n                }\n              }}\n              onClick={() => handleReportSelect(report)}\n            >\n              <CardContent sx={{ flexGrow: 1 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <Box sx={{ color: `${report.color}.main`, mr: 2 }}>\n                    {report.icon}\n                  </Box>\n                  <Typography variant=\"h6\" component=\"h2\">\n                    {report.title}\n                  </Typography>\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                  {report.description}\n                </Typography>\n\n                <Box>\n                  {report.features.map((feature, index) => (\n                    <Chip\n                      key={index}\n                      label={feature}\n                      size=\"small\"\n                      variant=\"outlined\"\n                      sx={{ mr: 0.5, mb: 0.5 }}\n                    />\n                  ))}\n                </Box>\n              </CardContent>\n\n              <CardActions>\n                <Button\n                  size=\"small\"\n                  color={report.color}\n                  startIcon={<AssessmentIcon />}\n                  fullWidth\n                >\n                  Genera Report\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Contenuto del report */}\n      {renderReportContent()}\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAW,CAAC,GAAGX,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEY;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,KAAK,EAAEC,QAAQ,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgF,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,QAAQ,CAAC;IACvCkF,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC;IAC7CwF,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA5F,SAAS,CAAC,MAAM;IACd,MAAM6F,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjCzB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM,CAAC0B,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3E1C,aAAa,CAAC2C,iBAAiB,CAACnC,UAAU,EAAE,OAAO,CAAC,EACpDR,aAAa,CAAC4C,mBAAmB,CAACpC,UAAU,EAAE,OAAO,CAAC,EACtDR,aAAa,CAAC6C,eAAe,CAACrC,UAAU,EAAE,OAAO,CAAC,EAClDR,aAAa,CAAC8C,kBAAkB,CAACtC,UAAU,EAAE,OAAO,CAAC,CACtD,CAAC;QAEFqB,cAAc,CAAC;UACbC,QAAQ,EAAEO,YAAY,CAACU,OAAO;UAC9BhB,GAAG,EAAEO,OAAO,CAACS,OAAO;UACpBf,MAAM,EAAEO,UAAU,CAACQ,OAAO;UAC1Bd,SAAS,EAAEO,aAAa,CAACO,OAAO;UAChCb,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;QAEFtB,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOmC,GAAG,EAAE;QACZC,OAAO,CAACrC,KAAK,CAAC,wBAAwB,EAAEoC,GAAG,CAAC;QAC5CnC,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,UAAU,EAAE;MACd4B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC5B,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM0C,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAEpD,OAAA,CAAC/B,cAAc;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAEpD,OAAA,CAACvB,QAAQ;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAEpD,OAAA,CAACT,aAAa;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAEpD,OAAA,CAACX,SAAS;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAEpD,OAAA,CAACzB,YAAY;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAEpD,OAAA,CAAC7B,YAAY;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACFpD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAImD,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAMhE,aAAa,CAAC2C,iBAAiB,CAACnC,UAAU,EAAEuD,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAMhE,aAAa,CAAC4C,mBAAmB,CAACpC,UAAU,EAAEuD,MAAM,CAAC;UACtE;QACF,KAAK,QAAQ;UACXC,QAAQ,GAAG,MAAMhE,aAAa,CAAC6C,eAAe,CAACrC,UAAU,EAAEuD,MAAM,CAAC;UAClE;QACF,KAAK,YAAY;UACfC,QAAQ,GAAG,MAAMhE,aAAa,CAAC8C,kBAAkB,CAACtC,UAAU,EAAEuD,MAAM,CAAC;UACrE;QACF,KAAK,kBAAkB;UACrB,IAAI,CAACzC,QAAQ,CAACK,SAAS,EAAE;YACvBd,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACAmD,QAAQ,GAAG,MAAMhE,aAAa,CAACiE,eAAe,CAACzD,UAAU,EAAEc,QAAQ,CAACK,SAAS,EAAEoC,MAAM,CAAC;UACtF;QACF,KAAK,cAAc;UACjB,IAAI,CAACzC,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDb,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACAmD,QAAQ,GAAG,MAAMhE,aAAa,CAACkE,uBAAuB,CACpD1D,UAAU,EACVc,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClBqC,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIJ,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtEjC,cAAc,CAACuC,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACN,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAACjB;UACpF,CAAC,CAAC,CAAC;QACL;QACAhC,aAAa,CAACiD,QAAQ,CAACjB,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAIiB,QAAQ,CAACK,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOrB,GAAG,EAAE;MACZC,OAAO,CAACrC,KAAK,CAAC,sCAAsC,EAAEoC,GAAG,CAAC;MAC1DnC,QAAQ,CAACmC,GAAG,CAACwB,MAAM,IAAIxB,GAAG,CAACyB,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACR9D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,kBAAkB,GAAIZ,UAAU,IAAK;IACzC7C,iBAAiB,CAAC6C,UAAU,CAAC;IAC7BzC,aAAa,CAACyC,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAMwB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExCxD,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAEoD,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClDvD,SAAS,EAAEiD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEA9D,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACA0C,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMrB,wBAAwB,CAACzC,UAAU,EAAEE,QAAQ,CAACE,OAAO,CAAC;IAC5DL,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMgE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhE,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdU,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACtE,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAACxD,KAAK;MAAC2I,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBtF,OAAA,CAAC1D,GAAG;QAAC6I,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzFtF,OAAA,CAACzD,UAAU;UAACoJ,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrBxE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEoC,KAAK,EAAC,KAAG,EAACtC,UAAU,CAACgF,aAAa;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACbxD,OAAA,CAAC1D,GAAG;UAAC6I,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnCtF,OAAA,CAACnD,MAAM;YACLiJ,SAAS,eAAE9F,OAAA,CAACrB,YAAY;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAACzC,UAAU,EAAE,KAAK,CAAE;YAC3DyE,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxD,OAAA,CAACnD,MAAM;YACLiJ,SAAS,eAAE9F,OAAA,CAACrB,YAAY;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAACzC,UAAU,EAAE,OAAO,CAAE;YAC7DyE,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxD,OAAA,CAACnD,MAAM;YACLiJ,SAAS,eAAE9F,OAAA,CAACjB,WAAW;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuC,OAAO,EAAEA,CAAA,KAAMlF,aAAa,CAAC,IAAI,CAAE;YACnC8E,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA,CAAC/C,OAAO;QAACkI,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzBtC,UAAU,KAAK,UAAU,IAAI+E,oBAAoB,CAAC,CAAC,EACnD/E,UAAU,KAAK,KAAK,IAAIgF,eAAe,CAAC,CAAC,EACzChF,UAAU,KAAK,QAAQ,IAAIiF,kBAAkB,CAAC,CAAC,EAC/CjF,UAAU,KAAK,kBAAkB,IAAIkF,2BAA2B,CAAC,CAAC,EAClElF,UAAU,KAAK,cAAc,IAAImF,uBAAuB,CAAC,CAAC,EAC1DnF,UAAU,KAAK,YAAY,IAAIoF,qBAAqB,CAAC,CAAC;IAAA;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEZ,CAAC;EAED,MAAMyC,oBAAoB,GAAIM,IAAI,iBAChCvG,OAAA,CAACvD,IAAI;IAAC+J,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAnB,QAAA,gBACzBtF,OAAA,CAACvD,IAAI;MAACiK,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;QAAC+I,eAAe;QAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;UAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAoB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;UAAAuH,QAAA,eACftF,OAAA,CAACtD,IAAI;YAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;cAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,gBAAc,eAAAtF,OAAA;kBAAAsF,QAAA,GAASiB,IAAI,CAACO,YAAY,EAAC,GAAC;gBAAA;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5ExD,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,gBAAc,eAAAtF,OAAA;kBAAAsF,QAAA,GAASiB,IAAI,CAACQ,YAAY,EAAC,GAAC;gBAAA;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5ExD,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,mBAAiB,eAAAtF,OAAA;kBAAAsF,QAAA,GAASiB,IAAI,CAACS,eAAe,EAAC,GAAC;gBAAA;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClFxD,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,eAAa,eAAAtF,OAAA;kBAAAsF,QAAA,GAASiB,IAAI,CAACU,uBAAuB,EAAC,GAAC;gBAAA;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEPxD,OAAA,CAACvD,IAAI;MAACiK,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;QAAC+I,eAAe;QAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;UAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAI;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;UAAAuH,QAAA,eACftF,OAAA,CAACtD,IAAI;YAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;cAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,eAAa,eAAAtF,OAAA;kBAAAsF,QAAA,EAASiB,IAAI,CAACW;gBAAW;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzExD,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,eAAa,eAAAtF,OAAA;kBAAAsF,QAAA,EAASiB,IAAI,CAACY;gBAAW;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzExD,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,kBAAgB,eAAAtF,OAAA;kBAAAsF,QAAA,EAASiB,IAAI,CAACa;gBAAc;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/ExD,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,oBAAkB,eAAAtF,OAAA;kBAAAsF,QAAA,GAASiB,IAAI,CAACc,gBAAgB,EAAC,GAAC;gBAAA;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEPxD,OAAA,CAACvD,IAAI;MAACiK,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;QAAC+I,eAAe;QAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;UAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAW;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;UAAAuH,QAAA,eACftF,OAAA,CAACtD,IAAI;YAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;cAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GAAC,qBAAmB,eAAAtF,OAAA;kBAAAsF,QAAA,GAASiB,IAAI,CAACe,iBAAiB,EAAC,UAAQ;gBAAA;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAC5F+C,IAAI,CAACgB,cAAc,iBAClBvH,OAAA,CAAAE,SAAA;gBAAAoF,QAAA,gBACEtF,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,kBAAgB,eAAAtF,OAAA;oBAAAsF,QAAA,GAASiB,IAAI,CAACgB,cAAc,EAAC,SAAO;kBAAA;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtFxD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,sBAAoB,eAAAtF,OAAA;oBAAAsF,QAAA,EAASiB,IAAI,CAACiB;kBAAkB;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,eACvF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEN+C,IAAI,CAACkB,YAAY,IAAIlB,IAAI,CAACkB,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChD1H,OAAA,CAACvD,IAAI;MAACiK,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;QAAC+I,eAAe;QAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;UAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;UAAAuH,QAAA,eACftF,OAAA,CAACtD,IAAI;YAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;cAAA2I,QAAA,EACTiB,IAAI,CAACkB,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7C9H,OAAA,CAACzD,UAAU;gBAAA+I,QAAA,GACRuC,IAAI,CAACtB,IAAI,EAAC,IAAE,eAAAvG,OAAA;kBAAAsF,QAAA,GAASuC,IAAI,CAACE,KAAK,EAAC,GAAC;gBAAA;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAD5BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACP;EAED,MAAM0C,eAAe,GAAIK,IAAI;IAAA,IAAAyB,mBAAA,EAAAC,qBAAA;IAAA,oBAC3BjI,OAAA,CAACvD,IAAI;MAAC+J,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBACzBtF,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;UAAC+I,eAAe;UAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;YAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;YAAAuH,QAAA,eACftF,OAAA,CAACvD,IAAI;cAAC+J,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAA0C,mBAAA,GACxBzB,IAAI,CAAC2B,aAAa,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBJ,GAAG,CAAC,CAACO,IAAI,EAAEL,KAAK,kBACnC9H,OAAA,CAACvD,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA/C,QAAA,eAC9BtF,OAAA,CAACtD,IAAI;kBAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;oBAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAE6C,IAAI,CAACG;oBAAS;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC7DxD,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAC,WAAS,EAAC6C,IAAI,CAACI,OAAO;oBAAA;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAChExD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,QAAM,EAAC6C,IAAI,CAACK,QAAQ;oBAAA;sBAAAnF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC9CxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,iBAAe,EAAC6C,IAAI,CAACM,aAAa,EAAC,GAAC;oBAAA;sBAAApF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7DxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,eAAa,EAAC6C,IAAI,CAACO,WAAW,EAAC,GAAC;oBAAA;sBAAArF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzDxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,aAAW,EAAC6C,IAAI,CAACnB,eAAe,EAAC,GAAC;oBAAA;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAV6BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEPxD,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;UAAC+I,eAAe;UAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;YAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;YAAAuH,QAAA,eACftF,OAAA,CAACvD,IAAI;cAAC+J,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAA2C,qBAAA,GACxB1B,IAAI,CAACoC,eAAe,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsBL,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBACvC9H,OAAA,CAACvD,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA/C,QAAA,eAC9BtF,OAAA,CAACtD,IAAI;kBAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;oBAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAEsD,MAAM,CAACN;oBAAS;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC/DxD,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAC,WAAS,EAACsD,MAAM,CAACL,OAAO;oBAAA;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAClExD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,UAAQ,EAACsD,MAAM,CAACC,UAAU;oBAAA;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACpDxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,qBAAmB,EAACsD,MAAM,CAACE,iBAAiB,EAAC,GAAC;oBAAA;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAR6BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM2C,kBAAkB,GAAII,IAAI;IAAA,IAAAwC,YAAA;IAAA,oBAC9B/I,OAAA,CAACvD,IAAI;MAAC+J,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,eACzBtF,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;UAAC+I,eAAe;UAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;YAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,IAAI;cAAAL,QAAA,GAAC,uBACF,EAACiB,IAAI,CAACyC,aAAa,EAAC,UAC3C;YAAA;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;YAAAuH,QAAA,eACftF,OAAA,CAACvD,IAAI;cAAC+J,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAAyD,YAAA,GACxBxC,IAAI,CAACzE,MAAM,cAAAiH,YAAA,uBAAXA,YAAA,CAAanB,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBAC9B9H,OAAA,CAACvD,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA/C,QAAA,eAC9BtF,OAAA,CAACtD,IAAI;kBAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;oBAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAEsD,MAAM,CAACnH;oBAAS;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC/DxD,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAEsD,MAAM,CAACN,SAAS,EAAC,KAAG,EAACM,MAAM,CAACL,OAAO;oBAAA;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC9ExD,OAAA,CAAClD,IAAI;sBACHmM,KAAK,EAAEL,MAAM,CAACM,KAAM;sBACpBzF,KAAK,EAAEmF,MAAM,CAACM,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;sBAC9DlD,IAAI,EAAC,OAAO;sBACZb,EAAE,EAAE;wBAAEO,EAAE,EAAE;sBAAE;oBAAE;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACFxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,gBAAc,EAACsD,MAAM,CAAC9B,YAAY,EAAC,GAAC;oBAAA;sBAAAzD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7DxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,iBAAe,EAACsD,MAAM,CAACO,aAAa,EAAC,GAAC;oBAAA;sBAAA9F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/DxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,oBAAkB,EAACsD,MAAM,CAACQ,gBAAgB,EAAC,GAAC;oBAAA;sBAAA/F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACrExD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,YAAU,EAACsD,MAAM,CAACS,oBAAoB,EAAC,GAAC;oBAAA;sBAAAhG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAhB6BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM4C,2BAA2B,GAAIG,IAAI;IAAA,IAAA+C,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,oBAAA;IAAA,oBACvC/J,OAAA,CAACvD,IAAI;MAAC+J,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBACzBtF,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;UAAC+I,eAAe;UAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;YAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAe;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;YAAAuH,QAAA,eACftF,OAAA,CAACtD,IAAI;cAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;gBAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,MAAI,eAAAtF,OAAA;oBAAAsF,QAAA,GAAAgE,YAAA,GAAS/C,IAAI,CAACqC,MAAM,cAAAU,YAAA,uBAAXA,YAAA,CAAa7H;kBAAS;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtExD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,aAAW,eAAAtF,OAAA;oBAAAsF,QAAA,GAAAiE,aAAA,GAAShD,IAAI,CAACqC,MAAM,cAAAW,aAAA,uBAAXA,aAAA,CAAajB;kBAAS;oBAAAjF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7ExD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,WAAS,eAAAtF,OAAA;oBAAAsF,QAAA,GAAAkE,aAAA,GAASjD,IAAI,CAACqC,MAAM,cAAAY,aAAA,uBAAXA,aAAA,CAAajB;kBAAO;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzExD,OAAA,CAAClD,IAAI;kBACHmM,KAAK,GAAAQ,aAAA,GAAElD,IAAI,CAACqC,MAAM,cAAAa,aAAA,uBAAXA,aAAA,CAAaP,KAAM;kBAC1BzF,KAAK,EAAE,EAAAiG,aAAA,GAAAnD,IAAI,CAACqC,MAAM,cAAAc,aAAA,uBAAXA,aAAA,CAAaR,KAAK,MAAK,aAAa,GAAG,SAAS,GAAG,SAAU;kBACpE/D,EAAE,EAAE;oBAAE6E,EAAE,EAAE;kBAAE;gBAAE;kBAAA3G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFxD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,gBAAc,eAAAtF,OAAA;oBAAAsF,QAAA,IAAAqE,aAAA,GAASpD,IAAI,CAACqC,MAAM,cAAAe,aAAA,uBAAXA,aAAA,CAAa7C,YAAY,EAAC,GAAC;kBAAA;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpFxD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,iBAAe,eAAAtF,OAAA;oBAAAsF,QAAA,IAAAsE,aAAA,GAASrD,IAAI,CAACqC,MAAM,cAAAgB,aAAA,uBAAXA,aAAA,CAAaT,aAAa,EAAC,GAAC;kBAAA;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtFxD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,oBAAkB,eAAAtF,OAAA;oBAAAsF,QAAA,IAAAuE,aAAA,GAAStD,IAAI,CAACqC,MAAM,cAAAiB,aAAA,uBAAXA,aAAA,CAAaT,gBAAgB,EAAC,GAAC;kBAAA;oBAAA/F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5FxD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,YAAU,eAAAtF,OAAA;oBAAAsF,QAAA,IAAAwE,aAAA,GAASvD,IAAI,CAACqC,MAAM,cAAAkB,aAAA,uBAAXA,aAAA,CAAaT,oBAAoB,EAAC,GAAC;kBAAA;oBAAAhG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEPxD,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;UAAC+I,eAAe;UAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;YAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,IAAI;cAAAL,QAAA,GAAC,kBACP,EAACiB,IAAI,CAACW,WAAW,EAAC,GACpC;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;YAAAuH,QAAA,eACftF,OAAA,CAACtD,IAAI;cAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;gBAAA2I,QAAA,eACVtF,OAAA,CAAC1D,GAAG;kBAAC6I,EAAE,EAAE;oBAAE8E,SAAS,EAAE,GAAG;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAA5E,QAAA,GAAAyE,oBAAA,GAC3CxD,IAAI,CAAC4D,cAAc,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBnC,GAAG,CAAC,CAACO,IAAI,EAAEL,KAAK,kBACpC9H,OAAA,CAAC1D,GAAG;oBAAa6I,EAAE,EAAE;sBAAEO,EAAE,EAAE,CAAC;sBAAEN,CAAC,EAAE,CAAC;sBAAEgF,MAAM,EAAE,mBAAmB;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAA/E,QAAA,gBACjFtF,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,OAAO;sBAAAL,QAAA,eAACtF,OAAA;wBAAAsF,QAAA,EAAS6C,IAAI,CAACmC;sBAAO;wBAAAjH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACxExD,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,SAAS;sBAAAL,QAAA,GAC1B6C,IAAI,CAACoC,OAAO,EAAC,KAAG,EAACpC,IAAI,CAACqC,OAAO,EAAC,KAAG,EAACrC,IAAI,CAACG,SAAS;oBAAA;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACbxD,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,SAAS;sBAACJ,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAC,WACnC,EAAC6C,IAAI,CAACM,aAAa,EAAC,aAAW,EAACN,IAAI,CAACO,WAAW,EAAC,aAAW,EAACP,IAAI,CAACe,KAAK;oBAAA;sBAAA7F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA,GAPLsE,KAAK;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM6C,uBAAuB,GAAIE,IAAI;IAAA,IAAAkE,qBAAA;IAAA,oBACnCzK,OAAA,CAACvD,IAAI;MAAC+J,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBACzBtF,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;UAAC+I,eAAe;UAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;YAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAmB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;YAAAuH,QAAA,eACftF,OAAA,CAACtD,IAAI;cAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;gBAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,WAAS,eAAAtF,OAAA;oBAAAsF,QAAA,GAASiB,IAAI,CAAChF,WAAW,EAAC,KAAG,EAACgF,IAAI,CAAC/E,SAAS;kBAAA;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxFxD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,gBAAc,eAAAtF,OAAA;oBAAAsF,QAAA,GAASiB,IAAI,CAACmE,oBAAoB,EAAC,GAAC;kBAAA;oBAAArH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpFxD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,iBAAe,eAAAtF,OAAA;oBAAAsF,QAAA,EAASiB,IAAI,CAACoE;kBAAa;oBAAAtH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7ExD,OAAA,CAACzD,UAAU;kBAAA+I,QAAA,GAAC,qBAAmB,eAAAtF,OAAA;oBAAAsF,QAAA,GAASiB,IAAI,CAACe,iBAAiB,EAAC,UAAQ;kBAAA;oBAAAjE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEPxD,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;UAAC+I,eAAe;UAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;YAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAgB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;YAAAuH,QAAA,eACftF,OAAA,CAACtD,IAAI;cAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;gBAAA2I,QAAA,eACVtF,OAAA,CAAC1D,GAAG;kBAAC6I,EAAE,EAAE;oBAAE8E,SAAS,EAAE,GAAG;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAA5E,QAAA,GAAAmF,qBAAA,GAC3ClE,IAAI,CAACqE,gBAAgB,cAAAH,qBAAA,uBAArBA,qBAAA,CAAuB7C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtC9H,OAAA,CAAC1D,GAAG;oBAAa6I,EAAE,EAAE;sBAAEI,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEqF,EAAE,EAAE;oBAAI,CAAE;oBAAAvF,QAAA,gBACjFtF,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,EAAEuC,IAAI,CAACtB;oBAAI;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACpCxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,eAACtF,OAAA;wBAAAsF,QAAA,GAASuC,IAAI,CAACE,KAAK,EAAC,GAAC;sBAAA;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,GAF/CsE,KAAK;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM8C,qBAAqB,GAAIC,IAAI;IAAA,IAAAuE,qBAAA;IAAA,oBACjC9K,OAAA,CAACvD,IAAI;MAAC+J,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,eACzBtF,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChBtF,OAAA,CAACnC,SAAS;UAAC+I,eAAe;UAAAtB,QAAA,gBACxBtF,OAAA,CAAClC,gBAAgB;YAAC+I,UAAU,eAAE7G,OAAA,CAACP,cAAc;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/CtF,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAA+B;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACnBxD,OAAA,CAACjC,gBAAgB;YAAAuH,QAAA,eACftF,OAAA,CAACvD,IAAI;cAAC+J,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAAwF,qBAAA,GACxBlK,UAAU,CAACmK,cAAc,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BlD,GAAG,CAAC,CAACsB,KAAK,EAAEpB,KAAK,kBAC3C9H,OAAA,CAACvD,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA/C,QAAA,eAC9BtF,OAAA,CAACtD,IAAI;kBAAA4I,QAAA,eACHtF,OAAA,CAACrD,WAAW;oBAAA2I,QAAA,gBACVtF,OAAA,CAACzD,UAAU;sBAACoJ,OAAO,EAAC,IAAI;sBAACqF,YAAY;sBAAA1F,QAAA,eACnCtF,OAAA,CAAClD,IAAI;wBACHmM,KAAK,EAAEC,KAAK,CAACA,KAAM;wBACnBzF,KAAK,EAAEyF,KAAK,CAACA,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;wBAC5D/D,EAAE,EAAE;0BAAEO,EAAE,EAAE;wBAAE;sBAAE;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eACbxD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,eAAa,eAAAtF,OAAA;wBAAAsF,QAAA,EAAS4D,KAAK,CAACV;sBAAQ;wBAAAnF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvExD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,iBAAe,eAAAtF,OAAA;wBAAAsF,QAAA,GAAS4D,KAAK,CAACT,aAAa,EAAC,GAAC;sBAAA;wBAAApF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/ExD,OAAA,CAACzD,UAAU;sBAAA+I,QAAA,GAAC,eAAa,eAAAtF,OAAA;wBAAAsF,QAAA,GAAS4D,KAAK,CAACR,WAAW,EAAC,GAAC;sBAAA;wBAAArF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAd6BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAerC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMyH,YAAY,GAAGA,CAAA,kBACnBjL,OAAA,CAAC5C,MAAM;IAACiH,IAAI,EAAErD,UAAW;IAACkK,OAAO,EAAEjG,iBAAkB;IAACkG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAA9F,QAAA,gBAC3EtF,OAAA,CAAC3C,WAAW;MAAAiI,QAAA,EACTxE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEoC;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACdxD,OAAA,CAAC1C,aAAa;MAAAgI,QAAA,GACX5E,KAAK,iBACJV,OAAA,CAACjD,KAAK;QAACsO,QAAQ,EAAC,OAAO;QAAClG,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnC5E;MAAK;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDxD,OAAA,CAACvD,IAAI;QAAC+J,SAAS;QAACC,OAAO,EAAE,CAAE;QAACtB,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxCtF,OAAA,CAACvD,IAAI;UAACiK,IAAI;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChBtF,OAAA,CAACxC,WAAW;YAAC4N,SAAS;YAAA9F,QAAA,gBACpBtF,OAAA,CAACvC,UAAU;cAAA6H,QAAA,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCxD,OAAA,CAACtC,MAAM;cACL4N,KAAK,EAAElK,QAAQ,CAACE,OAAQ;cACxB2H,KAAK,EAAC,SAAS;cACfsC,QAAQ,EAAGC,CAAC,IAAKnK,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEkK,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAAAhG,QAAA,gBAEvEtF,OAAA,CAACrC,QAAQ;gBAAC2N,KAAK,EAAC,OAAO;gBAAAhG,QAAA,EAAC;cAAoB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDxD,OAAA,CAACrC,QAAQ;gBAAC2N,KAAK,EAAC,KAAK;gBAAAhG,QAAA,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7CxD,OAAA,CAACrC,QAAQ;gBAAC2N,KAAK,EAAC,OAAO;gBAAAhG,QAAA,EAAC;cAAc;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAENtC,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAACvD,IAAI;UAACiK,IAAI;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChBtF,OAAA,CAACpC,SAAS;YACRwN,SAAS;YACTnC,KAAK,EAAC,WAAW;YACjBqC,KAAK,EAAElK,QAAQ,CAACK,SAAU;YAC1B8J,QAAQ,EAAGC,CAAC,IAAKnK,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAE+J,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzEI,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAAtI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEAtC,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAAoF,QAAA,gBACEtF,OAAA,CAACvD,IAAI;YAACiK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACftF,OAAA,CAACpC,SAAS;cACRwN,SAAS;cACTQ,IAAI,EAAC,MAAM;cACX3C,KAAK,EAAC,aAAa;cACnBqC,KAAK,EAAElK,QAAQ,CAACG,WAAY;cAC5BgK,QAAQ,EAAGC,CAAC,IAAKnK,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEiK,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxD,OAAA,CAACvD,IAAI;YAACiK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACftF,OAAA,CAACpC,SAAS;cACRwN,SAAS;cACTQ,IAAI,EAAC,MAAM;cACX3C,KAAK,EAAC,WAAW;cACjBqC,KAAK,EAAElK,QAAQ,CAACI,SAAU;cAC1B+J,QAAQ,EAAGC,CAAC,IAAKnK,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAEgK,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACzEO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBxD,OAAA,CAACzC,aAAa;MAAA+H,QAAA,gBACZtF,OAAA,CAACnD,MAAM;QAACkJ,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDxD,OAAA,CAACnD,MAAM;QACLkJ,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnBoG,QAAQ,EAAEvL,OAAQ;QAClBsF,SAAS,EAAEtF,OAAO,gBAAGR,OAAA,CAAChD,gBAAgB;UAACgJ,IAAI,EAAE;QAAG;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGxD,OAAA,CAACnB,cAAc;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA8B,QAAA,EAExE9E,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACExD,OAAA,CAAC1D,GAAG;IAAC6I,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhBtF,OAAA,CAAC1D,GAAG;MAAC6I,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFtF,OAAA,CAAC1D,GAAG;QAAC6I,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzDtF,OAAA,CAAC9C,UAAU;UAAC6I,OAAO,EAAEA,CAAA,KAAM1F,QAAQ,CAAC,CAAC,CAAC,CAAE;UAACoD,KAAK,EAAC,SAAS;UAAA6B,QAAA,eACtDtF,OAAA,CAACf,aAAa;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbxD,OAAA,CAACzD,UAAU;UAACoJ,OAAO,EAAC,IAAI;UAACqG,SAAS,EAAC,IAAI;UAAA1G,QAAA,EAAC;QAExC;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNxD,OAAA,CAACH,eAAe;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAGNxD,OAAA,CAACjD,KAAK;MAACsO,QAAQ,EAAC,MAAM;MAAClG,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAC;IAGtC;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAGRxD,OAAA,CAACvD,IAAI;MAAC+J,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,EACxBtC,WAAW,CAAC4E,GAAG,CAAEqE,MAAM,iBACtBjM,OAAA,CAACvD,IAAI;QAACiK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACyB,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/C,QAAA,eAC9BtF,OAAA,CAACtD,IAAI;UACHyI,EAAE,EAAE;YACF+G,MAAM,EAAE,MAAM;YACd3G,OAAO,EAAE,MAAM;YACf4G,aAAa,EAAE,QAAQ;YACvBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,iCAAiC;YAC7C,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb;UACF,CAAE;UACFxG,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAACyH,MAAM,CAAE;UAAA3G,QAAA,gBAE1CtF,OAAA,CAACrD,WAAW;YAACwI,EAAE,EAAE;cAAEqH,QAAQ,EAAE;YAAE,CAAE;YAAAlH,QAAA,gBAC/BtF,OAAA,CAAC1D,GAAG;cAAC6I,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACxDtF,OAAA,CAAC1D,GAAG;gBAAC6I,EAAE,EAAE;kBAAE1B,KAAK,EAAE,GAAGwI,MAAM,CAACxI,KAAK,OAAO;kBAAEgJ,EAAE,EAAE;gBAAE,CAAE;gBAAAnH,QAAA,EAC/C2G,MAAM,CAAC7I;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxD,OAAA,CAACzD,UAAU;gBAACoJ,OAAO,EAAC,IAAI;gBAACqG,SAAS,EAAC,IAAI;gBAAA1G,QAAA,EACpC2G,MAAM,CAAC/I;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENxD,OAAA,CAACzD,UAAU;cAACoJ,OAAO,EAAC,OAAO;cAAClC,KAAK,EAAC,gBAAgB;cAAC0B,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAC9D2G,MAAM,CAAC9I;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEbxD,OAAA,CAAC1D,GAAG;cAAAgJ,QAAA,EACD2G,MAAM,CAACvI,QAAQ,CAACkE,GAAG,CAAC,CAAC8E,OAAO,EAAE5E,KAAK,kBAClC9H,OAAA,CAAClD,IAAI;gBAEHmM,KAAK,EAAEyD,OAAQ;gBACf1G,IAAI,EAAC,OAAO;gBACZL,OAAO,EAAC,UAAU;gBAClBR,EAAE,EAAE;kBAAEsH,EAAE,EAAE,GAAG;kBAAE/G,EAAE,EAAE;gBAAI;cAAE,GAJpBoC,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEdxD,OAAA,CAACpD,WAAW;YAAA0I,QAAA,eACVtF,OAAA,CAACnD,MAAM;cACLmJ,IAAI,EAAC,OAAO;cACZvC,KAAK,EAAEwI,MAAM,CAACxI,KAAM;cACpBqC,SAAS,eAAE9F,OAAA,CAAC/B,cAAc;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9B4H,SAAS;cAAA9F,QAAA,EACV;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GApD6ByI,MAAM,CAAChJ,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqDzC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGN0B,mBAAmB,CAAC,CAAC,EAGrB+F,YAAY,CAAC,CAAC;EAAA;IAAA5H,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACpD,EAAA,CAtvBID,iBAAiB;EAAA,QACJT,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAA+M,EAAA,GAHpBxM,iBAAiB;AAwvBvB,eAAeA,iBAAiB;AAAC,IAAAwM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}