{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\TopNavbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { AppBar, Toolbar, Box, Button, Menu, MenuItem, Typography, IconButton, Divider, Avatar, Tooltip, Snackbar, Alert } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, Logout as LogoutIcon, KeyboardArrowDown as ArrowDownIcon, FileUpload as FileUploadIcon, FileDownload as FileDownloadIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport ExcelPopup from './cavi/ExcelPopup';\nimport Logo from './Logo';\nimport './TopNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TopNavbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n  const {\n    setOpenEliminaCavoDialog,\n    setOpenModificaCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n\n  // Stato per il popup Excel\n  const [excelPopupOpen, setExcelPopupOpen] = useState(false);\n  const [excelOperationType, setExcelOperationType] = useState('');\n\n  // Stato per gli snackbar\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = setAnchorEl => {\n    setAnchorEl(null);\n  };\n\n  // Gestisce l'apertura del popup Excel\n  const handleOpenExcelPopup = operationType => {\n    setExcelOperationType(operationType);\n    setExcelPopupOpen(true);\n    handleMenuClose(setExcelAnchorEl);\n  };\n\n  // Gestisce la creazione diretta dei template senza popup\n  const handleCreateTemplateDirect = async templateType => {\n    try {\n      handleMenuClose(setExcelAnchorEl);\n      if (templateType === 'cavi') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createCaviTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      } else if (templateType === 'parco-bobine') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createParcoBobineTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      }\n    } catch (error) {\n      console.error(`Errore nella creazione del template ${templateType}:`, error);\n      setSnackbar({\n        open: true,\n        message: `Errore nella creazione del template ${templateType}: ${error.message || 'Errore sconosciuto'}`,\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la chiusura del popup Excel\n  const handleCloseExcelPopup = () => {\n    setExcelPopupOpen(false);\n  };\n\n  // Gestisce il successo delle operazioni Excel\n  const handleExcelSuccess = message => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce gli errori delle operazioni Excel\n  const handleExcelError = message => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'error'\n    });\n  };\n\n  // Gestisce la chiusura dello snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"static\",\n    color: \"default\",\n    elevation: 2,\n    sx: {\n      zIndex: 1100,\n      width: '100%',\n      overflowX: 'hidden'\n    },\n    className: \"excel-style-menu cablys-navbar\",\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n      sx: {\n        overflowX: 'hidden',\n        height: '60px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cablys-logo-container\",\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          width: 32,\n          height: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          className: \"cablys-logo-text\",\n          children: \"CABLYS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        color: \"inherit\",\n        onClick: () => navigateTo('/dashboard'),\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 22\n        }, this),\n        sx: {\n          mr: 1\n        },\n        className: isActive('/dashboard') ? 'active-button' : '',\n        children: isImpersonating ? \"Torna al Menu Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? \"Pannello Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? \"Lista Cantieri\" : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        orientation: \"vertical\",\n        flexItem: true,\n        sx: {\n          mx: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          onClick: () => navigateTo('/dashboard/cantieri'),\n          sx: {\n            mr: 1\n          },\n          className: isActive('/dashboard/cantieri') ? 'active-button' : '',\n          children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 15\n        }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [(user === null || user === void 0 ? void 0 : user.role) !== 'cantieri_user' && /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isActive('/dashboard/cavi/visualizza') ? 'active-button' : '',\n            children: \"Visualizza Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"posa-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setPosaAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : '',\n            children: \"Posa e Collegamenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"parco-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : '',\n            children: \"Parco Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"excel-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setExcelAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : '',\n            children: \"Gestione Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo(`/dashboard/cavi/${selectedCantiereId}/report`),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/report') ? 'active-button' : '',\n            children: \"Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/cavi/certificazione'),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : '',\n            children: \"Certificazione Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/cavi/comande'),\n            sx: {\n              mr: 1\n            },\n            className: isActive('/dashboard/cavi/comande') ? 'active-button' : '',\n            children: \"Gestione Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"posa-menu\",\n            anchorEl: posaAnchorEl,\n            keepMounted: true,\n            open: Boolean(posaAnchorEl),\n            onClose: () => handleMenuClose(setPosaAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n              children: \"Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => {\n                // Apre il dialogo di modifica cavi invece di navigare alla pagina\n                setOpenModificaCavoDialog(true);\n                // Chiude il menu\n                handleMenuClose(setPosaAnchorEl);\n                // Naviga alla pagina visualizza cavi\n                navigateTo('/dashboard/cavi/visualizza');\n              },\n              children: \"Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => {\n                // Apre il dialogo di aggiunta cavi invece di navigare alla pagina\n                setOpenAggiungiCavoDialog(true);\n                // Chiude il menu\n                handleMenuClose(setPosaAnchorEl);\n                // Naviga alla pagina visualizza cavi\n                navigateTo('/dashboard/cavi/visualizza');\n              },\n              children: \"Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => {\n                // Apre il dialogo di eliminazione cavi invece di navigare alla pagina\n                setOpenEliminaCavoDialog(true);\n                // Chiude il menu\n                handleMenuClose(setPosaAnchorEl);\n                // Naviga alla pagina visualizza cavi\n                navigateTo('/dashboard/cavi/visualizza');\n              },\n              children: \"Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n              children: \"Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n              children: \"Gestisci collegamenti cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"parco-menu\",\n            anchorEl: parcoAnchorEl,\n            keepMounted: true,\n            open: Boolean(parcoAnchorEl),\n            onClose: () => handleMenuClose(setParcoAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n              children: \"Visualizza Storico Utilizzo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"excel-menu\",\n            anchorEl: excelAnchorEl,\n            keepMounted: true,\n            open: Boolean(excelAnchorEl),\n            onClose: () => handleMenuClose(setExcelAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('importaCavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileUploadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 21\n              }, this), \"Importa Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('importaParcoBobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileUploadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 21\n              }, this), \"Importa Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleCreateTemplateDirect('cavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 21\n              }, this), \"Template Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleCreateTemplateDirect('parco-bobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 21\n              }, this), \"Template Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('esportaCavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this), \"Esporta Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('esportaParcoBobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 21\n              }, this), \"Esporta Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          height: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(SelectedCantiereDisplay, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          sx: {\n            mr: 2.5,\n            fontSize: '1rem'\n          },\n          children: [\"Accesso come: \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: impersonatedUser.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mr: 2.5,\n            fontWeight: 500,\n            fontSize: '1rem'\n          },\n          children: (user === null || user === void 0 ? void 0 : user.username) || ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Logout\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            edge: \"end\",\n            sx: {\n              '&:hover': {\n                backgroundColor: '#e9ecef'\n              },\n              padding: '10px'\n            },\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {\n              fontSize: \"medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ExcelPopup, {\n      open: excelPopupOpen,\n      onClose: handleCloseExcelPopup,\n      operationType: excelOperationType,\n      cantiereId: cantiereId,\n      onSuccess: handleExcelSuccess,\n      onError: handleExcelError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        variant: \"filled\",\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n};\n_s(TopNavbar, \"ONlviG92KtiaDX0KkLlLoSLbl8U=\", false, function () {\n  return [useNavigate, useLocation, useAuth, useGlobalContext];\n});\n_c = TopNavbar;\nexport default TopNavbar;\nvar _c;\n$RefreshReg$(_c, \"TopNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "IconButton", "Divider", "Avatar", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "Logout", "LogoutIcon", "KeyboardArrowDown", "ArrowDownIcon", "FileUpload", "FileUploadIcon", "FileDownload", "FileDownloadIcon", "useAuth", "useGlobalContext", "SelectedCantiereDisplay", "ExcelPopup", "Logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TopNavbar", "_s", "navigate", "location", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "setOpenEliminaCavoDialog", "setOpenModificaCavoDialog", "setOpenAggiungiCavoDialog", "excelPopupOpen", "setExcelPopupOpen", "excelOperationType", "setExcelOperationType", "snackbar", "setSnackbar", "open", "message", "severity", "cantiereId", "parseInt", "localStorage", "getItem", "homeAnchorEl", "setHomeAnchorEl", "adminAnchorEl", "setAdminAnchorEl", "cantieriAnchorEl", "setCantieriAnchorEl", "caviAnchorEl", "setCaviAnchorEl", "posaAnchorEl", "setPosaAnchorEl", "parcoAnchorEl", "setParcoAnchorEl", "excelAnchorEl", "setExcelAnchorEl", "selectedCantiereId", "selectedCantiereName", "handleMenuOpen", "event", "setAnchorEl", "currentTarget", "handleMenuClose", "handleOpenExcelPopup", "operationType", "handleCreateTemplateDirect", "templateType", "excelService", "default", "createCaviTemplate", "createParcoBobineTemplate", "error", "console", "handleCloseExcelPopup", "handleExcelSuccess", "handleExcelError", "handleCloseSnackbar", "navigateTo", "path", "role", "handleLogout", "isActive", "pathname", "isPartOfActive", "startsWith", "position", "color", "elevation", "sx", "zIndex", "width", "overflowX", "className", "children", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "startIcon", "mr", "orientation", "flexItem", "mx", "username", "e", "endIcon", "id", "anchorEl", "keepMounted", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "mt", "fontSize", "flexGrow", "display", "alignItems", "variant", "fontWeight", "title", "edge", "backgroundColor", "padding", "onSuccess", "onError", "autoHideDuration", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/TopNavbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Typography,\n  IconButton,\n  Divider,\n  Avatar,\n  Tooltip,\n  Snackbar,\n  Alert\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  Logout as LogoutIcon,\n  KeyboardArrowDown as ArrowDownIcon,\n  FileUpload as FileUploadIcon,\n  FileDownload as FileDownloadIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport ExcelPopup from './cavi/ExcelPopup';\nimport Logo from './Logo';\nimport './TopNavbar.css';\n\nconst TopNavbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n  const { setOpenEliminaCavoDialog, setOpenModificaCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n\n  // Stato per il popup Excel\n  const [excelPopupOpen, setExcelPopupOpen] = useState(false);\n  const [excelOperationType, setExcelOperationType] = useState('');\n\n  // Stato per gli snackbar\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n\n\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = (setAnchorEl) => {\n    setAnchorEl(null);\n  };\n\n  // Gestisce l'apertura del popup Excel\n  const handleOpenExcelPopup = (operationType) => {\n    setExcelOperationType(operationType);\n    setExcelPopupOpen(true);\n    handleMenuClose(setExcelAnchorEl);\n  };\n\n  // Gestisce la creazione diretta dei template senza popup\n  const handleCreateTemplateDirect = async (templateType) => {\n    try {\n      handleMenuClose(setExcelAnchorEl);\n\n      if (templateType === 'cavi') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createCaviTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      } else if (templateType === 'parco-bobine') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createParcoBobineTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      }\n    } catch (error) {\n      console.error(`Errore nella creazione del template ${templateType}:`, error);\n      setSnackbar({\n        open: true,\n        message: `Errore nella creazione del template ${templateType}: ${error.message || 'Errore sconosciuto'}`,\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la chiusura del popup Excel\n  const handleCloseExcelPopup = () => {\n    setExcelPopupOpen(false);\n  };\n\n  // Gestisce il successo delle operazioni Excel\n  const handleExcelSuccess = (message) => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce gli errori delle operazioni Excel\n  const handleExcelError = (message) => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'error'\n    });\n  };\n\n  // Gestisce la chiusura dello snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n\n\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <AppBar position=\"static\" color=\"default\" elevation={2} sx={{ zIndex: 1100, width: '100%', overflowX: 'hidden' }} className=\"excel-style-menu cablys-navbar\">\n      <Toolbar sx={{ overflowX: 'hidden', height: '60px' }}>\n        {/* CABLYS Logo */}\n        <div className=\"cablys-logo-container\">\n          <Logo width={32} height={32} />\n          <Typography className=\"cablys-logo-text\">CABLYS</Typography>\n        </div>\n\n        {/* Home - Testo personalizzato in base al tipo di utente */}\n        <Button\n          color=\"inherit\"\n          onClick={() => navigateTo('/dashboard')}\n          startIcon={<HomeIcon />}\n          sx={{ mr: 1 }}\n          className={isActive('/dashboard') ? 'active-button' : ''}\n        >\n          {isImpersonating ? \"Torna al Menu Admin\" :\n           user?.role === 'owner' ? \"Pannello Admin\" :\n           user?.role === 'user' ? \"Lista Cantieri\" :\n           user?.role === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"}\n        </Button>\n        <Divider orientation=\"vertical\" flexItem sx={{ mx: 0.5 }} />\n\n        {/* Il menu Amministratore è stato rimosso perché ridondante con il pulsante Home per gli amministratori */}\n\n        {/* Menu per utenti standard e cantieri */}\n        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n          <>\n            {/* Pulsante Lista Cantieri solo per utenti che impersonano */}\n            {isImpersonating && (\n              <Button\n                color=\"inherit\"\n                onClick={() => navigateTo('/dashboard/cantieri')}\n                sx={{ mr: 1 }}\n                className={isActive('/dashboard/cantieri') ? 'active-button' : ''}\n              >\n                {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"}\n              </Button>\n            )}\n\n            {/* Il cantiere selezionato è stato spostato nella parte destra della barra di navigazione */}\n\n            {/* Menu di gestione cavi - visibile solo se un cantiere è selezionato */}\n            {selectedCantiereId && (\n              <>\n                {/* Visualizza Cavi - nascosto per utenti cantiere perché ridondante con il tasto Home */}\n                {user?.role !== 'cantieri_user' && (\n                  <Button\n                    color=\"inherit\"\n                    onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                    sx={{ mr: 1 }}\n                    className={isActive('/dashboard/cavi/visualizza') ? 'active-button' : ''}\n                  >\n                    Visualizza Cavi\n                  </Button>\n                )}\n\n                {/* Posa e Collegamenti */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"posa-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setPosaAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : ''}\n                >\n                  Posa e Collegamenti\n                </Button>\n\n                {/* Parco Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"parco-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : ''}\n                >\n                  Parco Cavi\n                </Button>\n\n                {/* Gestione Excel */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"excel-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setExcelAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : ''}\n                >\n                  Gestione Excel\n                </Button>\n\n                {/* Report */}\n                <Button\n                  color=\"inherit\"\n                  onClick={() => navigateTo(`/dashboard/cavi/${selectedCantiereId}/report`)}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/report') ? 'active-button' : ''}\n                >\n                  Report\n                </Button>\n\n                {/* Certificazione Cavi */}\n                <Button\n                  color=\"inherit\"\n                  onClick={() => navigateTo('/dashboard/cavi/certificazione')}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : ''}\n                >\n                  Certificazione Cavi\n                </Button>\n\n                {/* Gestione Comande - semplificato */}\n                <Button\n                  color=\"inherit\"\n                  onClick={() => navigateTo('/dashboard/cavi/comande')}\n                  sx={{ mr: 1 }}\n                  className={isActive('/dashboard/cavi/comande') ? 'active-button' : ''}\n                >\n                  Gestione Comande\n                </Button>\n\n                {/* Sottomenu Posa e Collegamenti */}\n                <Menu\n                  id=\"posa-menu\"\n                  anchorEl={posaAnchorEl}\n                  keepMounted\n                  open={Boolean(posaAnchorEl)}\n                  onClose={() => handleMenuClose(setPosaAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}>Inserisci metri posati</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di modifica cavi invece di navigare alla pagina\n                    setOpenModificaCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Modifica cavo</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di aggiunta cavi invece di navigare alla pagina\n                    setOpenAggiungiCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Aggiungi nuovo cavo</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di eliminazione cavi invece di navigare alla pagina\n                    setOpenEliminaCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Elimina cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}>Modifica bobina cavo posato</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}>Gestisci collegamenti cavo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Parco Cavi */}\n                <Menu\n                  id=\"parco-menu\"\n                  anchorEl={parcoAnchorEl}\n                  keepMounted\n                  open={Boolean(parcoAnchorEl)}\n                  onClose={() => handleMenuClose(setParcoAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/storico')}>Visualizza Storico Utilizzo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Excel */}\n                <Menu\n                  id=\"excel-menu\"\n                  anchorEl={excelAnchorEl}\n                  keepMounted\n                  open={Boolean(excelAnchorEl)}\n                  onClose={() => handleMenuClose(setExcelAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => handleOpenExcelPopup('importaCavi')}>\n                    <FileUploadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Importa Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('importaParcoBobine')}>\n                    <FileUploadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Importa Parco Bobine\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={() => handleCreateTemplateDirect('cavi')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Template Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleCreateTemplateDirect('parco-bobine')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Template Parco Bobine\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={() => handleOpenExcelPopup('esportaCavi')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Esporta Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('esportaParcoBobine')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Esporta Parco Bobine\n                  </MenuItem>\n                </Menu>\n\n\n\n\n\n\n              </>\n            )}\n          </>\n        )}\n\n        {/* Spacer */}\n        <Box sx={{ flexGrow: 1 }} />\n\n        {/* Informazioni utente e logout */}\n        <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>\n          {/* Mostra il cantiere selezionato */}\n          <SelectedCantiereDisplay />\n\n          {isImpersonating && impersonatedUser && (\n            <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 2.5, fontSize: '1rem' }}>\n              Accesso come: <b>{impersonatedUser.username}</b>\n            </Typography>\n          )}\n          <Typography variant=\"body2\" sx={{ mr: 2.5, fontWeight: 500, fontSize: '1rem' }}>\n            {user?.username || ''}\n          </Typography>\n          <Tooltip title=\"Logout\">\n            <IconButton\n              color=\"inherit\"\n              onClick={handleLogout}\n              edge=\"end\"\n              sx={{ '&:hover': { backgroundColor: '#e9ecef' }, padding: '10px' }}\n            >\n              <LogoutIcon fontSize=\"medium\" />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Toolbar>\n\n      {/* Excel Popup */}\n      <ExcelPopup\n        open={excelPopupOpen}\n        onClose={handleCloseExcelPopup}\n        operationType={excelOperationType}\n        cantiereId={cantiereId}\n        onSuccess={handleExcelSuccess}\n        onError={handleExcelError}\n      />\n\n      {/* Snackbar per messaggi di successo/errore */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert\n          onClose={handleCloseSnackbar}\n          severity={snackbar.severity}\n          variant=\"filled\"\n          sx={{ width: '100%' }}\n        >\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </AppBar>\n  );\n};\n\nexport default TopNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,aAAa,EAClCC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,OAAOC,uBAAuB,MAAM,kCAAkC;AACtE,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6C,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGhB,OAAO,CAAC,CAAC;EACrE,MAAM;IAAEiB,wBAAwB;IAAEC,yBAAyB;IAAEC;EAA0B,CAAC,GAAGlB,gBAAgB,CAAC,CAAC;;EAE7G;EACA,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAAC0D,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAC;IACvC4D,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;;EAE3E;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2E,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;;EAIxD;EACA,MAAMiF,kBAAkB,GAAGhB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMgB,oBAAoB,GAAGjB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAMiB,cAAc,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IAC7CA,WAAW,CAACD,KAAK,CAACE,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAIF,WAAW,IAAK;IACvCA,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAIC,aAAa,IAAK;IAC9ChC,qBAAqB,CAACgC,aAAa,CAAC;IACpClC,iBAAiB,CAAC,IAAI,CAAC;IACvBgC,eAAe,CAACP,gBAAgB,CAAC;EACnC,CAAC;;EAED;EACA,MAAMU,0BAA0B,GAAG,MAAOC,YAAY,IAAK;IACzD,IAAI;MACFJ,eAAe,CAACP,gBAAgB,CAAC;MAEjC,IAAIW,YAAY,KAAK,MAAM,EAAE;QAC3B,MAAMC,YAAY,GAAG,MAAM,MAAM,CAAC,0BAA0B,CAAC;QAC7D,MAAMA,YAAY,CAACC,OAAO,CAACC,kBAAkB,CAAC,CAAC;QAC/CnC,WAAW,CAAC;UACVC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,oGAAoG;UAC7GC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI6B,YAAY,KAAK,cAAc,EAAE;QAC1C,MAAMC,YAAY,GAAG,MAAM,MAAM,CAAC,0BAA0B,CAAC;QAC7D,MAAMA,YAAY,CAACC,OAAO,CAACE,yBAAyB,CAAC,CAAC;QACtDpC,WAAW,CAAC;UACVC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,4GAA4G;UACrHC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuCL,YAAY,GAAG,EAAEK,KAAK,CAAC;MAC5ErC,WAAW,CAAC;QACVC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,uCAAuC8B,YAAY,KAAKK,KAAK,CAACnC,OAAO,IAAI,oBAAoB,EAAE;QACxGC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMoC,qBAAqB,GAAGA,CAAA,KAAM;IAClC3C,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAItC,OAAO,IAAK;IACtCF,WAAW,CAAC;MACVC,IAAI,EAAE,IAAI;MACVC,OAAO;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIvC,OAAO,IAAK;IACpCF,WAAW,CAAC;MACVC,IAAI,EAAE,IAAI;MACVC,OAAO;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMuC,mBAAmB,GAAGA,CAAA,KAAM;IAChC1C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0C,UAAU,GAAIC,IAAI,IAAK;IAC3B;IACA,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,IAAItD,eAAe,EAAE;QACnBJ,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,OAAO,EAAE;QAC/B3D,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,MAAM,EAAE;QAC9B3D,QAAQ,CAAC,qBAAqB,CAAC;MACjC;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,eAAe,EAAE;QACvC;QACA3D,QAAQ,CAAC,4BAA4B,CAAC;MACxC;MACA;MAAA,KACK;QACHA,QAAQ,CAAC0D,IAAI,CAAC;MAChB;IACF,CAAC,MAAM;MACL1D,QAAQ,CAAC0D,IAAI,CAAC;IAChB;;IAEA;IACAhB,eAAe,CAACnB,eAAe,CAAC;IAChCmB,eAAe,CAACjB,gBAAgB,CAAC;IACjCiB,eAAe,CAACf,mBAAmB,CAAC;IACpCe,eAAe,CAACb,eAAe,CAAC;IAChCa,eAAe,CAACX,eAAe,CAAC;IAChCW,eAAe,CAACT,gBAAgB,CAAC;IACjCS,eAAe,CAACP,gBAAgB,CAAC;EAGnC,CAAC;EAED,MAAMyB,YAAY,GAAGA,CAAA,KAAM;IACzBzD,MAAM,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAM0D,QAAQ,GAAIH,IAAI,IAAK;IACzB,OAAOzD,QAAQ,CAAC6D,QAAQ,KAAKJ,IAAI;EACnC,CAAC;;EAED;EACA,MAAMK,cAAc,GAAIL,IAAI,IAAK;IAC/B,OAAOzD,QAAQ,CAAC6D,QAAQ,CAACE,UAAU,CAACN,IAAI,CAAC;EAC3C,CAAC;EAED,oBACE/D,OAAA,CAACrC,MAAM;IAAC2G,QAAQ,EAAC,QAAQ;IAACC,KAAK,EAAC,SAAS;IAACC,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAACC,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAC1J9E,OAAA,CAACpC,OAAO;MAAC6G,EAAE,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEG,MAAM,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEnD9E,OAAA;QAAK6E,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC9E,OAAA,CAACF,IAAI;UAAC6E,KAAK,EAAE,EAAG;UAACI,MAAM,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BnF,OAAA,CAAC/B,UAAU;UAAC4G,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eAGNnF,OAAA,CAAClC,MAAM;QACLyG,KAAK,EAAC,SAAS;QACfa,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,YAAY,CAAE;QACxCuB,SAAS,eAAErF,OAAA,CAACvB,QAAQ;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBV,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QACdT,SAAS,EAAEX,QAAQ,CAAC,YAAY,CAAC,GAAG,eAAe,GAAG,EAAG;QAAAY,QAAA,EAExDrE,eAAe,GAAG,qBAAqB,GACvC,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,OAAO,GAAG,gBAAgB,GACzC,CAAAzD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,MAAM,GAAG,gBAAgB,GACxC,CAAAzD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,eAAe,GAAG,eAAe,GAAG;MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACTnF,OAAA,CAAC7B,OAAO;QAACoH,WAAW,EAAC,UAAU;QAACC,QAAQ;QAACf,EAAE,EAAE;UAAEgB,EAAE,EAAE;QAAI;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAK3D,CAAC,CAAA5E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,OAAO,IAAK,CAAAzD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,OAAO,IAAIvD,eAAe,IAAIC,gBAAiB,kBACzFV,OAAA,CAAAE,SAAA;QAAA4E,QAAA,GAEGrE,eAAe,iBACdT,OAAA,CAAClC,MAAM;UACLyG,KAAK,EAAC,SAAS;UACfa,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,qBAAqB,CAAE;UACjDW,EAAE,EAAE;YAAEa,EAAE,EAAE;UAAE,CAAE;UACdT,SAAS,EAAEX,QAAQ,CAAC,qBAAqB,CAAC,GAAG,eAAe,GAAG,EAAG;UAAAY,QAAA,EAEjErE,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACgF,QAAQ,EAAE,GAAG;QAAgB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CACT,EAKA1C,kBAAkB,iBACjBzC,OAAA,CAAAE,SAAA;UAAA4E,QAAA,GAEG,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,eAAe,iBAC7BhE,OAAA,CAAClC,MAAM;YACLyG,KAAK,EAAC,SAAS;YACfa,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,4BAA4B,CAAE;YACxDW,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEX,QAAQ,CAAC,4BAA4B,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAY,QAAA,EAC1E;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAGDnF,OAAA,CAAClC,MAAM;YACLyG,KAAK,EAAC,SAAS;YACf,iBAAc,WAAW;YACzB,iBAAc,MAAM;YACpBa,OAAO,EAAGO,CAAC,IAAKhD,cAAc,CAACgD,CAAC,EAAEvD,eAAe,CAAE;YACnDwD,OAAO,eAAE5F,OAAA,CAACX,aAAa;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,sBAAsB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC1E;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTnF,OAAA,CAAClC,MAAM;YACLyG,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBa,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,kCAAkC,CAAE;YAC9DW,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC3E;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTnF,OAAA,CAAClC,MAAM;YACLyG,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBa,OAAO,EAAGO,CAAC,IAAKhD,cAAc,CAACgD,CAAC,EAAEnD,gBAAgB,CAAE;YACpDoD,OAAO,eAAE5F,OAAA,CAACX,aAAa;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC3E;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTnF,OAAA,CAAClC,MAAM;YACLyG,KAAK,EAAC,SAAS;YACfa,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,mBAAmBrB,kBAAkB,SAAS,CAAE;YAC1EgC,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,wBAAwB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC5E;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTnF,OAAA,CAAClC,MAAM;YACLyG,KAAK,EAAC,SAAS;YACfa,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,gCAAgC,CAAE;YAC5DW,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,gCAAgC,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EACpF;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTnF,OAAA,CAAClC,MAAM;YACLyG,KAAK,EAAC,SAAS;YACfa,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,yBAAyB,CAAE;YACrDW,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEX,QAAQ,CAAC,yBAAyB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAY,QAAA,EACvE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTnF,OAAA,CAACjC,IAAI;YACH8H,EAAE,EAAC,WAAW;YACdC,QAAQ,EAAE3D,YAAa;YACvB4D,WAAW;YACX3E,IAAI,EAAE4E,OAAO,CAAC7D,YAAY,CAAE;YAC5B8D,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAACX,eAAe,CAAE;YAChD8D,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,gBAEhB9E,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,sCAAsC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC9GnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAM;gBACvB;gBACAxE,yBAAyB,CAAC,IAAI,CAAC;gBAC/B;gBACAmC,eAAe,CAACX,eAAe,CAAC;gBAChC;gBACA0B,UAAU,CAAC,4BAA4B,CAAC;cAC1C,CAAE;cAAAgB,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3BnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAM;gBACvB;gBACAvE,yBAAyB,CAAC,IAAI,CAAC;gBAC/B;gBACAkC,eAAe,CAACX,eAAe,CAAC;gBAChC;gBACA0B,UAAU,CAAC,4BAA4B,CAAC;cAC1C,CAAE;cAAAgB,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjCnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAM;gBACvB;gBACAzE,wBAAwB,CAAC,IAAI,CAAC;gBAC9B;gBACAoC,eAAe,CAACX,eAAe,CAAC;gBAChC;gBACA0B,UAAU,CAAC,4BAA4B,CAAC;cAC1C,CAAE;cAAAgB,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1BnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,sCAAsC,CAAE;cAAAgB,QAAA,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnHnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,mCAAmC,CAAE;cAAAgB,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC,eAGPnF,OAAA,CAACjC,IAAI;YACH8H,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEzD,aAAc;YACxB0D,WAAW;YACX3E,IAAI,EAAE4E,OAAO,CAAC3D,aAAa,CAAE;YAC7B4D,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAACT,gBAAgB,CAAE;YACjD4D,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,eAEhB9E,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,+BAA+B,CAAE;cAAAgB,QAAA,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eAGPnF,OAAA,CAACjC,IAAI;YACH8H,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEvD,aAAc;YACxBwD,WAAW;YACX3E,IAAI,EAAE4E,OAAO,CAACzD,aAAa,CAAE;YAC7B0D,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAACP,gBAAgB,CAAE;YACjD0D,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,gBAEhB9E,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAAC,aAAa,CAAE;cAAA8B,QAAA,gBAC3D9E,OAAA,CAACT,cAAc;gBAACgH,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAAC,oBAAoB,CAAE;cAAA8B,QAAA,gBAClE9E,OAAA,CAACT,cAAc;gBAACgH,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXnF,OAAA,CAAC7B,OAAO;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMlC,0BAA0B,CAAC,MAAM,CAAE;cAAA4B,QAAA,gBAC1D9E,OAAA,CAACP,gBAAgB;gBAAC8G,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMlC,0BAA0B,CAAC,cAAc,CAAE;cAAA4B,QAAA,gBAClE9E,OAAA,CAACP,gBAAgB;gBAAC8G,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXnF,OAAA,CAAC7B,OAAO;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAAC,aAAa,CAAE;cAAA8B,QAAA,gBAC3D9E,OAAA,CAACP,gBAAgB;gBAAC8G,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXnF,OAAA,CAAChC,QAAQ;cAACoH,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAAC,oBAAoB,CAAE;cAAA8B,QAAA,gBAClE9E,OAAA,CAACP,gBAAgB;gBAAC8G,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,eAOP,CACH;MAAA,eACD,CACH,eAGDnF,OAAA,CAACnC,GAAG;QAAC4G,EAAE,EAAE;UAAE+B,QAAQ,EAAE;QAAE;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG5BnF,OAAA,CAACnC,GAAG;QAAC4G,EAAE,EAAE;UAAEgC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAE3B,MAAM,EAAE;QAAO,CAAE;QAAAD,QAAA,gBAEjE9E,OAAA,CAACJ,uBAAuB;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAE1B1E,eAAe,IAAIC,gBAAgB,iBAClCV,OAAA,CAAC/B,UAAU;UAAC0I,OAAO,EAAC,OAAO;UAACpC,KAAK,EAAC,eAAe;UAACE,EAAE,EAAE;YAAEa,EAAE,EAAE,GAAG;YAAEiB,QAAQ,EAAE;UAAO,CAAE;UAAAzB,QAAA,GAAC,gBACrE,eAAA9E,OAAA;YAAA8E,QAAA,EAAIpE,gBAAgB,CAACgF;UAAQ;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACb,eACDnF,OAAA,CAAC/B,UAAU;UAAC0I,OAAO,EAAC,OAAO;UAAClC,EAAE,EAAE;YAAEa,EAAE,EAAE,GAAG;YAAEsB,UAAU,EAAE,GAAG;YAAEL,QAAQ,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAC5E,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,QAAQ,KAAI;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACbnF,OAAA,CAAC3B,OAAO;UAACwI,KAAK,EAAC,QAAQ;UAAA/B,QAAA,eACrB9E,OAAA,CAAC9B,UAAU;YACTqG,KAAK,EAAC,SAAS;YACfa,OAAO,EAAEnB,YAAa;YACtB6C,IAAI,EAAC,KAAK;YACVrC,EAAE,EAAE;cAAE,SAAS,EAAE;gBAAEsC,eAAe,EAAE;cAAU,CAAC;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAAlC,QAAA,eAEnE9E,OAAA,CAACb,UAAU;cAACoH,QAAQ,EAAC;YAAQ;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnF,OAAA,CAACH,UAAU;MACTuB,IAAI,EAAEN,cAAe;MACrBmF,OAAO,EAAEvC,qBAAsB;MAC/BT,aAAa,EAAEjC,kBAAmB;MAClCO,UAAU,EAAEA,UAAW;MACvB0F,SAAS,EAAEtD,kBAAmB;MAC9BuD,OAAO,EAAEtD;IAAiB;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAGFnF,OAAA,CAAC1B,QAAQ;MACP8C,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpB+F,gBAAgB,EAAE,IAAK;MACvBlB,OAAO,EAAEpC,mBAAoB;MAC7BqC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAtB,QAAA,eAE3D9E,OAAA,CAACzB,KAAK;QACJ0H,OAAO,EAAEpC,mBAAoB;QAC7BvC,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAC5BqF,OAAO,EAAC,QAAQ;QAChBlC,EAAE,EAAE;UAAEE,KAAK,EAAE;QAAO,CAAE;QAAAG,QAAA,EAErB5D,QAAQ,CAACG;MAAO;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEb,CAAC;AAAC/E,EAAA,CA/dID,SAAS;EAAA,QACI1C,WAAW,EACXC,WAAW,EACgCgC,OAAO,EACwBC,gBAAgB;AAAA;AAAAyH,EAAA,GAJvGjH,SAAS;AAief,eAAeA,SAAS;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}