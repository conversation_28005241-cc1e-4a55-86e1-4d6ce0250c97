{"ast": null, "code": "import formatDistance from \"../en-US/_lib/formatDistance/index.js\";\nimport formatRelative from \"../en-US/_lib/formatRelative/index.js\";\nimport localize from \"../en-US/_lib/localize/index.js\";\nimport match from \"../en-US/_lib/match/index.js\";\nimport formatLong from \"../en-GB/_lib/formatLong/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (Ireland).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@tan75]{@link https://github.com/tan75}\n */\nvar locale = {\n  code: 'en-IE',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "locale", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/en-IE/index.js"], "sourcesContent": ["import formatDistance from \"../en-US/_lib/formatDistance/index.js\";\nimport formatRelative from \"../en-US/_lib/formatRelative/index.js\";\nimport localize from \"../en-US/_lib/localize/index.js\";\nimport match from \"../en-US/_lib/match/index.js\";\nimport formatLong from \"../en-GB/_lib/formatLong/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (Ireland).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@tan75]{@link https://github.com/tan75}\n */\nvar locale = {\n  code: 'en-IE',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,uCAAuC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,QAAQ,MAAM,iCAAiC;AACtD,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,UAAU,MAAM,mCAAmC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}