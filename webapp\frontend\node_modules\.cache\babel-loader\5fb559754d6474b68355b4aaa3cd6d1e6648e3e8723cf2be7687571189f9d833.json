{"ast": null, "code": "// Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'હમણાં',\n    // CLDR #1461\n    other: '​આશરે {{count}} સેકંડ'\n  },\n  xSeconds: {\n    one: '1 સેકંડ',\n    other: '{{count}} સેકંડ'\n  },\n  halfAMinute: 'અડધી મિનિટ',\n  lessThanXMinutes: {\n    one: 'આ મિનિટ',\n    // CLDR #1448\n    other: '​આશરે {{count}} મિનિટ'\n  },\n  xMinutes: {\n    one: '1 મિનિટ',\n    other: '{{count}} મિનિટ'\n  },\n  aboutXHours: {\n    one: '​આશરે 1 કલાક',\n    other: '​આશરે {{count}} કલાક'\n  },\n  xHours: {\n    one: '1 કલાક',\n    other: '{{count}} કલાક'\n  },\n  xDays: {\n    one: '1 દિવસ',\n    other: '{{count}} દિવસ'\n  },\n  aboutXWeeks: {\n    one: 'આશરે 1 અઠવાડિયું',\n    other: 'આશરે {{count}} અઠવાડિયા'\n  },\n  xWeeks: {\n    one: '1 અઠવાડિયું',\n    other: '{{count}} અઠવાડિયા'\n  },\n  aboutXMonths: {\n    one: 'આશરે 1 મહિનો',\n    other: 'આશરે {{count}} મહિના'\n  },\n  xMonths: {\n    one: '1 મહિનો',\n    other: '{{count}} મહિના'\n  },\n  aboutXYears: {\n    one: 'આશરે 1 વર્ષ',\n    other: 'આશરે {{count}} વર્ષ'\n  },\n  xYears: {\n    one: '1 વર્ષ',\n    other: '{{count}} વર્ષ'\n  },\n  overXYears: {\n    one: '1 વર્ષથી વધુ',\n    other: '{{count}} વર્ષથી વધુ'\n  },\n  almostXYears: {\n    one: 'લગભગ 1 વર્ષ',\n    other: 'લગભગ {{count}} વર્ષ'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + 'માં';\n    } else {\n      return result + ' પહેલાં';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/gu/_lib/formatDistance/index.js"], "sourcesContent": ["// Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'હમણાં',\n    // CLDR #1461\n    other: '​આશરે {{count}} સેકંડ'\n  },\n  xSeconds: {\n    one: '1 સેકંડ',\n    other: '{{count}} સેકંડ'\n  },\n  halfAMinute: 'અડધી મિનિટ',\n  lessThanXMinutes: {\n    one: 'આ મિનિટ',\n    // CLDR #1448\n    other: '​આશરે {{count}} મિનિટ'\n  },\n  xMinutes: {\n    one: '1 મિનિટ',\n    other: '{{count}} મિનિટ'\n  },\n  aboutXHours: {\n    one: '​આશરે 1 કલાક',\n    other: '​આશરે {{count}} કલાક'\n  },\n  xHours: {\n    one: '1 કલાક',\n    other: '{{count}} કલાક'\n  },\n  xDays: {\n    one: '1 દિવસ',\n    other: '{{count}} દિવસ'\n  },\n  aboutXWeeks: {\n    one: 'આશરે 1 અઠવાડિયું',\n    other: 'આશરે {{count}} અઠવાડિયા'\n  },\n  xWeeks: {\n    one: '1 અઠવાડિયું',\n    other: '{{count}} અઠવાડિયા'\n  },\n  aboutXMonths: {\n    one: 'આશરે 1 મહિનો',\n    other: 'આશરે {{count}} મહિના'\n  },\n  xMonths: {\n    one: '1 મહિનો',\n    other: '{{count}} મહિના'\n  },\n  aboutXYears: {\n    one: 'આશરે 1 વર્ષ',\n    other: 'આશરે {{count}} વર્ષ'\n  },\n  xYears: {\n    one: '1 વર્ષ',\n    other: '{{count}} વર્ષ'\n  },\n  overXYears: {\n    one: '1 વર્ષથી વધુ',\n    other: '{{count}} વર્ષથી વધુ'\n  },\n  almostXYears: {\n    one: 'લગભગ 1 વર્ષ',\n    other: 'લગભગ {{count}} વર્ષ'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + 'માં';\n    } else {\n      return result + ' પહેલાં';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA;AACA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,OAAO;IACZ;IACAC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,YAAY;EACzBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,SAAS;IACd;IACAC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,KAAK;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,SAAS;IAC3B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}