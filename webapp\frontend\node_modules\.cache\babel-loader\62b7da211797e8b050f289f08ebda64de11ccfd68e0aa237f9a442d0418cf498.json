{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'ўтган' eeee p 'да'\",\n  yesterday: \"'кеча' p 'да'\",\n  today: \"'бугун' p 'да'\",\n  tomorrow: \"'эртага' p 'да'\",\n  nextWeek: \"eeee p 'да'\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/uz-Cyrl/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'ўтган' eeee p 'да'\",\n  yesterday: \"'кеча' p 'да'\",\n  today: \"'бугун' p 'да'\",\n  tomorrow: \"'эртага' p 'да'\",\n  nextWeek: \"eeee p 'да'\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}