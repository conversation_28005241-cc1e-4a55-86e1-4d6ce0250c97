{"ast": null, "code": "var parseIsSsrByDefault = function parseIsSsrByDefault() {\n  return !(typeof window !== 'undefined' && window.document && window.document.createElement && window.setTimeout);\n};\nexport var Global = {\n  isSsr: parseIsSsrByDefault(),\n  get: function get(key) {\n    return Global[key];\n  },\n  set: function set(key, value) {\n    if (typeof key === 'string') {\n      Global[key] = value;\n    } else {\n      var keys = Object.keys(key);\n      if (keys && keys.length) {\n        keys.forEach(function (k) {\n          Global[k] = key[k];\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["parseIsSsrByDefault", "window", "document", "createElement", "setTimeout", "Global", "isSsr", "get", "key", "set", "value", "keys", "Object", "length", "for<PERSON>ach", "k"], "sources": ["C:/CMS/webapp/frontend/node_modules/recharts/es6/util/Global.js"], "sourcesContent": ["var parseIsSsrByDefault = function parseIsSsrByDefault() {\n  return !(typeof window !== 'undefined' && window.document && window.document.createElement && window.setTimeout);\n};\nexport var Global = {\n  isSsr: parseIsSsrByDefault(),\n  get: function get(key) {\n    return Global[key];\n  },\n  set: function set(key, value) {\n    if (typeof key === 'string') {\n      Global[key] = value;\n    } else {\n      var keys = Object.keys(key);\n      if (keys && keys.length) {\n        keys.forEach(function (k) {\n          Global[k] = key[k];\n        });\n      }\n    }\n  }\n};"], "mappings": "AAAA,IAAIA,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;EACvD,OAAO,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,aAAa,IAAIF,MAAM,CAACG,UAAU,CAAC;AAClH,CAAC;AACD,OAAO,IAAIC,MAAM,GAAG;EAClBC,KAAK,EAAEN,mBAAmB,CAAC,CAAC;EAC5BO,GAAG,EAAE,SAASA,GAAGA,CAACC,GAAG,EAAE;IACrB,OAAOH,MAAM,CAACG,GAAG,CAAC;EACpB,CAAC;EACDC,GAAG,EAAE,SAASA,GAAGA,CAACD,GAAG,EAAEE,KAAK,EAAE;IAC5B,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;MAC3BH,MAAM,CAACG,GAAG,CAAC,GAAGE,KAAK;IACrB,CAAC,MAAM;MACL,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACH,GAAG,CAAC;MAC3B,IAAIG,IAAI,IAAIA,IAAI,CAACE,MAAM,EAAE;QACvBF,IAAI,CAACG,OAAO,CAAC,UAAUC,CAAC,EAAE;UACxBV,MAAM,CAACU,CAAC,CAAC,GAAGP,GAAG,CAACO,CAAC,CAAC;QACpB,CAAC,CAAC;MACJ;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}