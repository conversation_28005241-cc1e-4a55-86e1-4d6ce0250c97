{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ConfigurazioneDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, FormControl, RadioGroup, FormControlLabel, Radio, Box, CircularProgress } from '@mui/material';\n\n/**\n * Dialog per la configurazione della numerazione delle bobine.\n * Mostrato solo per il primo inserimento di una bobina in un cantiere.\n *\n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Indica se il dialog è aperto\n * @param {Function} props.onClose - Funzione chiamata alla chiusura del dialog\n * @param {Function} props.onConfirm - Funzione chiamata alla conferma della configurazione\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ConfigurazioneDialog = ({\n  open,\n  onClose,\n  onConfirm\n}) => {\n  _s();\n  const [configValue, setConfigValue] = useState('s');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Reset dello stato quando il dialog viene aperto\n  useEffect(() => {\n    if (open) {\n      setIsSubmitting(false);\n      setConfigValue('s'); // Reset al valore di default\n      console.log('ConfigurazioneDialog: Dialog aperto');\n    }\n  }, [open]);\n  const handleConfirm = () => {\n    console.log('ConfigurazioneDialog: Confermando con valore:', configValue);\n    setIsSubmitting(true);\n    // Previene click multipli\n    setTimeout(() => {\n      onConfirm(configValue);\n      setIsSubmitting(false);\n    }, 300);\n  };\n  const handleClose = () => {\n    if (isSubmitting) return; // Previene chiusura durante l'invio\n    console.log('ConfigurazioneDialog: Chiudendo dialog');\n    onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: \"Configurazione Numerazione Bobine\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa \\xE8 la prima bobina per questo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Scegli come vuoi gestire la numerazione delle bobine:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        component: \"fieldset\",\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(RadioGroup, {\n          value: configValue,\n          onChange: e => setConfigValue(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"s\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 24\n            }, this),\n            label: \"Usa numeri progressivi (es. 1, 2, 3, ...)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"n\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 24\n            }, this),\n            label: \"Inserisci manualmente l'ID della bobina (es. A123, SPEC01, ecc.)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Nota: Questa configurazione sar\\xE0 utilizzata per tutte le bobine di questo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        disabled: isSubmitting,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleConfirm,\n        variant: \"contained\",\n        color: \"primary\",\n        disabled: isSubmitting,\n        children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20,\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), \"Elaborazione...\"]\n        }, void 0, true) : 'Conferma'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(ConfigurazioneDialog, \"6+tREvTHKQxGnNAOAlXMK3BoDmo=\");\n_c = ConfigurazioneDialog;\nexport default ConfigurazioneDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfigurazioneDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "FormControl", "RadioGroup", "FormControlLabel", "Radio", "Box", "CircularProgress", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ConfigurazioneDialog", "open", "onClose", "onConfirm", "_s", "config<PERSON><PERSON><PERSON>", "setConfigValue", "isSubmitting", "setIsSubmitting", "console", "log", "handleConfirm", "setTimeout", "handleClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "variant", "gutterBottom", "component", "mt", "value", "onChange", "e", "target", "control", "label", "color", "onClick", "disabled", "size", "mr", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ConfigurazioneDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  FormControl,\n  RadioGroup,\n  FormControlLabel,\n  Radio,\n  Box,\n  CircularProgress\n} from '@mui/material';\n\n/**\n * Dialog per la configurazione della numerazione delle bobine.\n * Mostrato solo per il primo inserimento di una bobina in un cantiere.\n *\n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Indica se il dialog è aperto\n * @param {Function} props.onClose - Funzione chiamata alla chiusura del dialog\n * @param {Function} props.onConfirm - Funzione chiamata alla conferma della configurazione\n */\nconst ConfigurazioneDialog = ({ open, onClose, onConfirm }) => {\n  const [configValue, setConfigValue] = useState('s');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Reset dello stato quando il dialog viene aperto\n  useEffect(() => {\n    if (open) {\n      setIsSubmitting(false);\n      setConfigValue('s'); // Reset al valore di default\n      console.log('ConfigurazioneDialog: Dialog aperto');\n    }\n  }, [open]);\n\n  const handleConfirm = () => {\n    console.log('ConfigurazioneDialog: Confermando con valore:', configValue);\n    setIsSubmitting(true);\n    // Previene click multipli\n    setTimeout(() => {\n      onConfirm(configValue);\n      setIsSubmitting(false);\n    }, 300);\n  };\n\n  const handleClose = () => {\n    if (isSubmitting) return; // Previene chiusura durante l'invio\n    console.log('ConfigurazioneDialog: Chiudendo dialog');\n    onClose();\n  };\n\n  return (\n    <Dialog open={open} onClose={handleClose} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>Configurazione Numerazione Bobine</DialogTitle>\n      <DialogContent>\n        <Box sx={{ mb: 2 }}>\n          <Typography variant=\"body1\" gutterBottom>\n            Questa è la prima bobina per questo cantiere.\n          </Typography>\n          <Typography variant=\"body1\" gutterBottom>\n            Scegli come vuoi gestire la numerazione delle bobine:\n          </Typography>\n        </Box>\n        <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n          <RadioGroup\n            value={configValue}\n            onChange={(e) => setConfigValue(e.target.value)}\n          >\n            <FormControlLabel\n              value=\"s\"\n              control={<Radio />}\n              label=\"Usa numeri progressivi (es. 1, 2, 3, ...)\"\n            />\n            <FormControlLabel\n              value=\"n\"\n              control={<Radio />}\n              label=\"Inserisci manualmente l'ID della bobina (es. A123, SPEC01, ecc.)\"\n            />\n          </RadioGroup>\n        </FormControl>\n        <Box sx={{ mt: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Nota: Questa configurazione sarà utilizzata per tutte le bobine di questo cantiere.\n          </Typography>\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleClose} disabled={isSubmitting}>Annulla</Button>\n        <Button\n          onClick={handleConfirm}\n          variant=\"contained\"\n          color=\"primary\"\n          disabled={isSubmitting}\n        >\n          {isSubmitting ? (\n            <>\n              <CircularProgress size={20} sx={{ mr: 1 }} />\n              Elaborazione...\n            </>\n          ) : (\n            'Conferma'\n          )}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ConfigurazioneDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,GAAG,EACHC,gBAAgB,QACX,eAAe;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,GAAG,CAAC;EACnD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIkB,IAAI,EAAE;MACRO,eAAe,CAAC,KAAK,CAAC;MACtBF,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;MACrBG,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IACpD;EACF,CAAC,EAAE,CAACT,IAAI,CAAC,CAAC;EAEV,MAAMU,aAAa,GAAGA,CAAA,KAAM;IAC1BF,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEL,WAAW,CAAC;IACzEG,eAAe,CAAC,IAAI,CAAC;IACrB;IACAI,UAAU,CAAC,MAAM;MACfT,SAAS,CAACE,WAAW,CAAC;MACtBG,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIN,YAAY,EAAE,OAAO,CAAC;IAC1BE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrDR,OAAO,CAAC,CAAC;EACX,CAAC;EAED,oBACEL,OAAA,CAACb,MAAM;IAACiB,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEW,WAAY;IAACC,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC/DnB,OAAA,CAACZ,WAAW;MAAA+B,QAAA,EAAC;IAAiC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAC5DvB,OAAA,CAACX,aAAa;MAAA8B,QAAA,gBACZnB,OAAA,CAACH,GAAG;QAAC2B,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACjBnB,OAAA,CAACR,UAAU;UAACkC,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAR,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvB,OAAA,CAACR,UAAU;UAACkC,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAR,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNvB,OAAA,CAACP,WAAW;QAACmC,SAAS,EAAC,UAAU;QAACJ,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,eAC9CnB,OAAA,CAACN,UAAU;UACToC,KAAK,EAAEtB,WAAY;UACnBuB,QAAQ,EAAGC,CAAC,IAAKvB,cAAc,CAACuB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAX,QAAA,gBAEhDnB,OAAA,CAACL,gBAAgB;YACfmC,KAAK,EAAC,GAAG;YACTI,OAAO,eAAElC,OAAA,CAACJ,KAAK;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBY,KAAK,EAAC;UAA2C;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACFvB,OAAA,CAACL,gBAAgB;YACfmC,KAAK,EAAC,GAAG;YACTI,OAAO,eAAElC,OAAA,CAACJ,KAAK;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBY,KAAK,EAAC;UAAkE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdvB,OAAA,CAACH,GAAG;QAAC2B,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,eACjBnB,OAAA,CAACR,UAAU;UAACkC,OAAO,EAAC,OAAO;UAACU,KAAK,EAAC,gBAAgB;UAAAjB,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChBvB,OAAA,CAACV,aAAa;MAAA6B,QAAA,gBACZnB,OAAA,CAACT,MAAM;QAAC8C,OAAO,EAAErB,WAAY;QAACsB,QAAQ,EAAE5B,YAAa;QAAAS,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtEvB,OAAA,CAACT,MAAM;QACL8C,OAAO,EAAEvB,aAAc;QACvBY,OAAO,EAAC,WAAW;QACnBU,KAAK,EAAC,SAAS;QACfE,QAAQ,EAAE5B,YAAa;QAAAS,QAAA,EAEtBT,YAAY,gBACXV,OAAA,CAAAE,SAAA;UAAAiB,QAAA,gBACEnB,OAAA,CAACF,gBAAgB;YAACyC,IAAI,EAAE,EAAG;YAACf,EAAE,EAAE;cAAEgB,EAAE,EAAE;YAAE;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAE/C;QAAA,eAAE,CAAC,GAEH;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAChB,EAAA,CApFIJ,oBAAoB;AAAAsC,EAAA,GAApBtC,oBAAoB;AAsF1B,eAAeA,oBAAoB;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}