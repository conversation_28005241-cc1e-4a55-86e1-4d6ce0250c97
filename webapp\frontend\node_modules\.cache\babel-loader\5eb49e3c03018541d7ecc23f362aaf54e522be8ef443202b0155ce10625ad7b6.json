{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [isImpersonating, setIsImpersonating] = useState(localStorage.getItem('isImpersonating') === 'true');\n  const [impersonatedUser, setImpersonatedUser] = useState(JSON.parse(localStorage.getItem('impersonatedUser') || 'null'));\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const navigate = useNavigate();\n\n  // Carica il cantiere selezionato dal localStorage all'avvio\n  useEffect(() => {\n    const cantiereId = localStorage.getItem('selectedCantiereId');\n    const cantiereName = localStorage.getItem('selectedCantiereName');\n    if (cantiereId) {\n      setSelectedCantiere({\n        id_cantiere: parseInt(cantiereId, 10),\n        nome: cantiereName || `Cantiere ${cantiereId}`\n      });\n      console.log('Cantiere caricato dal localStorage:', {\n        id_cantiere: cantiereId,\n        nome: cantiereName\n      });\n    }\n  }, []);\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        console.log('Verificando autenticazione all\\'avvio...');\n        // Prima di tutto, imposta loading a true\n        setLoading(true);\n\n        // Pulisci eventuali token non validi o scaduti\n        const token = localStorage.getItem('token');\n        console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No');\n        if (token) {\n          try {\n            // Verifica la validità del token\n            console.log('Tentativo di verifica token...');\n            const userData = await authService.checkToken();\n            console.log('Token valido, dati utente:', userData);\n            setUser(userData);\n            setIsAuthenticated(true);\n\n            // Imposta lo stato di impersonificazione in base ai dati dell'utente\n            // Questo valore viene ora impostato dal backend nel token JWT e recuperato da authService.checkToken\n            const impersonatingState = userData.isImpersonated === true;\n            console.log('Stato di impersonificazione recuperato dai dati utente:', impersonatingState);\n            setIsImpersonating(impersonatingState);\n          } catch (tokenError) {\n            console.error('Errore durante la verifica del token:', tokenError);\n            // Se il token non è valido, rimuovilo\n            console.log('Rimozione token non valido dal localStorage');\n            localStorage.removeItem('token');\n            setUser(null);\n            setIsAuthenticated(false);\n            localStorage.removeItem('isImpersonating');\n            setIsImpersonating(false);\n\n            // Pulisci eventuali selezioni di cantiere precedenti\n            localStorage.removeItem('selectedCantiereId');\n            localStorage.removeItem('selectedCantiereName');\n            console.log('Rimossi dati cantiere precedenti dal localStorage');\n          }\n        } else {\n          console.log('Nessun token trovato, utente non autenticato');\n          setUser(null);\n          setIsAuthenticated(false);\n          localStorage.removeItem('isImpersonating');\n          setIsImpersonating(false);\n\n          // Pulisci eventuali selezioni di cantiere precedenti\n          localStorage.removeItem('selectedCantiereId');\n          localStorage.removeItem('selectedCantiereName');\n          console.log('Rimossi dati cantiere precedenti dal localStorage');\n        }\n      } catch (error) {\n        console.error('Errore generale durante la verifica dell\\'autenticazione:', error);\n        // In caso di errore generale, assicurati che l'utente non sia autenticato\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n\n        // Pulisci eventuali selezioni di cantiere precedenti\n        localStorage.removeItem('selectedCantiereId');\n        localStorage.removeItem('selectedCantiereName');\n        console.log('Rimossi dati cantiere precedenti dal localStorage');\n      } finally {\n        // Assicurati che loading sia impostato a false alla fine\n        console.log('Completata verifica autenticazione, loading:', false);\n        setTimeout(() => {\n          setLoading(false);\n        }, 500); // Aggiungi un piccolo ritardo per assicurarsi che lo stato sia aggiornato\n      }\n    };\n\n    // Esegui la verifica dell'autenticazione\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login come ${loginType}`, credentials);\n      const response = await authService.login(credentials, loginType);\n      console.log('Risposta login ricevuta:', response);\n      const {\n        access_token,\n        user_id,\n        username,\n        role,\n        cantiere_id,\n        cantiere_name\n      } = response;\n\n      // Pulisci eventuali selezioni di cantiere precedenti\n      // Questo risolve il problema del cantiere che rimane in memoria tra sessioni diverse\n      localStorage.removeItem('selectedCantiereId');\n      localStorage.removeItem('selectedCantiereName');\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\n\n      // Se è un login cantiere, salva l'ID e il nome del cantiere nel localStorage\n      if (loginType === 'cantiere' && cantiere_id) {\n        console.log('Login cantiere: salvando ID cantiere nel localStorage:', cantiere_id);\n        localStorage.setItem('selectedCantiereId', cantiere_id.toString());\n        localStorage.setItem('selectedCantiereName', cantiere_name || `Cantiere ${cantiere_id}`);\n      }\n\n      // Salva il token nel localStorage\n      console.log('Salvataggio token nel localStorage');\n      localStorage.setItem('token', access_token);\n\n      // Imposta i dati dell'utente\n      const userData = {\n        id: user_id,\n        username,\n        role\n      };\n      console.log('Impostazione dati utente:', userData);\n      setUser(userData);\n      setIsAuthenticated(true);\n\n      // Se è un utente cantiere, salva l'ID e il nome del cantiere nel localStorage\n      if (role === 'cantieri_user' && cantiere_id) {\n        console.log('Utente cantiere, salvataggio dati cantiere:', {\n          cantiere_id,\n          cantiere_name\n        });\n        localStorage.setItem('selectedCantiereId', cantiere_id.toString());\n        localStorage.setItem('selectedCantiereName', cantiere_name || `Cantiere ${cantiere_id}`);\n      }\n\n      // Resetta lo stato di impersonificazione a false quando si effettua un login normale\n      if (isImpersonating) {\n        console.log('Reset dello stato di impersonificazione durante login normale');\n        localStorage.removeItem('isImpersonating');\n        setIsImpersonating(false);\n      }\n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    // Se l'utente corrente è un amministratore che ha effettuato \"login as user\",\n    // reindirizza alla dashboard amministratore invece che alla pagina di login\n    if (isImpersonating) {\n      console.log('Logout da utente impersonato, ritorno al menu amministratore');\n\n      // Rimuovi i dati dell'utente impersonato\n      localStorage.removeItem('impersonatedUser');\n      localStorage.removeItem('isImpersonating');\n      setImpersonatedUser(null);\n      setIsImpersonating(false);\n\n      // Reindirizza alla dashboard amministratore\n      navigate('/dashboard/admin');\n    } else {\n      // Logout normale\n      console.log('Logout normale, ritorno alla pagina di login');\n      localStorage.removeItem('token');\n      localStorage.removeItem('isImpersonating');\n      localStorage.removeItem('impersonatedUser');\n      localStorage.removeItem('selectedCantiereId');\n      localStorage.removeItem('selectedCantiereName');\n      setUser(null);\n      setImpersonatedUser(null);\n      setIsAuthenticated(false);\n      navigate('/login');\n    }\n  };\n\n  // Funzione per impostare il token\n  const setToken = token => {\n    localStorage.setItem('token', token);\n  };\n\n  // Funzione per impersonare un utente\n  const impersonateUser = async userId => {\n    try {\n      // Chiama l'endpoint di impersonificazione\n      const response = await authService.impersonateUser(userId);\n\n      // Salva il token nel localStorage\n      localStorage.setItem('token', response.access_token);\n\n      // Salva i dati dell'utente impersonato\n      const impersonatedUserData = {\n        id: response.impersonated_id,\n        username: response.impersonated_username,\n        role: response.impersonated_role\n      };\n\n      // Salva i dati dell'utente impersonato nel localStorage\n      localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\n      setImpersonatedUser(impersonatedUserData);\n\n      // Imposta lo stato di impersonificazione a true\n      setIsImpersonating(true);\n      localStorage.setItem('isImpersonating', 'true');\n\n      // L'utente rimane l'amministratore, ma ora ha informazioni sull'utente impersonato\n      // Non modifichiamo lo stato dell'utente perché rimane l'amministratore\n\n      console.log('Impersonificazione attivata, logout riporterà al menu amministratore');\n      console.log('Utente impersonato:', impersonatedUserData.username, 'Ruolo:', impersonatedUserData.role);\n      return {\n        ...user,\n        // Mantiene i dati dell'amministratore\n        isImpersonated: true,\n        impersonatedUser: impersonatedUserData // Aggiunge i dati dell'utente impersonato\n      };\n    } catch (error) {\n      console.error('Errore durante l\\'impersonificazione:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per selezionare un cantiere\n  const selectCantiere = cantiere => {\n    if (cantiere && cantiere.id_cantiere) {\n      // Salva nel localStorage\n      localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString());\n      localStorage.setItem('selectedCantiereName', cantiere.nome || `Cantiere ${cantiere.id_cantiere}`);\n\n      // Aggiorna lo stato\n      setSelectedCantiere(cantiere);\n      console.log('Cantiere selezionato:', cantiere);\n    }\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    setUser,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    setToken,\n    impersonateUser,\n    isImpersonating,\n    impersonatedUser,\n    selectedCantiere,\n    selectCantiere\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"ZLm2PrL5RCg2a9CsJQMMKEA7to8=\", false, function () {\n  return [useNavigate];\n});\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useNavigate", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "isImpersonating", "setIsImpersonating", "localStorage", "getItem", "impersonated<PERSON><PERSON>", "setImpersonatedUser", "JSON", "parse", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "navigate", "cantiereId", "cantiereName", "id_cantiere", "parseInt", "nome", "console", "log", "checkAuth", "token", "userData", "checkToken", "impersonating<PERSON><PERSON>", "isImpersonated", "tokenError", "error", "removeItem", "setTimeout", "login", "credentials", "loginType", "response", "access_token", "user_id", "username", "role", "cantiere_id", "cantiere_name", "setItem", "toString", "id", "logout", "setToken", "impersonate<PERSON><PERSON>", "userId", "impersonated<PERSON><PERSON><PERSON><PERSON>", "impersonated_id", "impersonated_username", "impersonated_role", "stringify", "selectCantiere", "cantiere", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nconst AuthContext = createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [isImpersonating, setIsImpersonating] = useState(localStorage.getItem('isImpersonating') === 'true');\n  const [impersonatedUser, setImpersonatedUser] = useState(JSON.parse(localStorage.getItem('impersonatedUser') || 'null'));\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const navigate = useNavigate();\n\n  // Carica il cantiere selezionato dal localStorage all'avvio\n  useEffect(() => {\n    const cantiereId = localStorage.getItem('selectedCantiereId');\n    const cantiereName = localStorage.getItem('selectedCantiereName');\n\n    if (cantiereId) {\n      setSelectedCantiere({\n        id_cantiere: parseInt(cantiereId, 10),\n        nome: cantiereName || `Cantiere ${cantiereId}`\n      });\n      console.log('Cantiere caricato dal localStorage:', { id_cantiere: cantiereId, nome: cantiereName });\n    }\n  }, []);\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        console.log('Verificando autenticazione all\\'avvio...');\n        // Prima di tutto, imposta loading a true\n        setLoading(true);\n\n        // Pulisci eventuali token non validi o scaduti\n        const token = localStorage.getItem('token');\n        console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No');\n\n        if (token) {\n          try {\n            // Verifica la validità del token\n            console.log('Tentativo di verifica token...');\n            const userData = await authService.checkToken();\n            console.log('Token valido, dati utente:', userData);\n            setUser(userData);\n            setIsAuthenticated(true);\n\n            // Imposta lo stato di impersonificazione in base ai dati dell'utente\n            // Questo valore viene ora impostato dal backend nel token JWT e recuperato da authService.checkToken\n            const impersonatingState = userData.isImpersonated === true;\n            console.log('Stato di impersonificazione recuperato dai dati utente:', impersonatingState);\n            setIsImpersonating(impersonatingState);\n          } catch (tokenError) {\n            console.error('Errore durante la verifica del token:', tokenError);\n            // Se il token non è valido, rimuovilo\n            console.log('Rimozione token non valido dal localStorage');\n            localStorage.removeItem('token');\n            setUser(null);\n            setIsAuthenticated(false);\n            localStorage.removeItem('isImpersonating');\n            setIsImpersonating(false);\n\n            // Pulisci eventuali selezioni di cantiere precedenti\n            localStorage.removeItem('selectedCantiereId');\n            localStorage.removeItem('selectedCantiereName');\n            console.log('Rimossi dati cantiere precedenti dal localStorage');\n          }\n        } else {\n          console.log('Nessun token trovato, utente non autenticato');\n          setUser(null);\n          setIsAuthenticated(false);\n          localStorage.removeItem('isImpersonating');\n          setIsImpersonating(false);\n\n          // Pulisci eventuali selezioni di cantiere precedenti\n          localStorage.removeItem('selectedCantiereId');\n          localStorage.removeItem('selectedCantiereName');\n          console.log('Rimossi dati cantiere precedenti dal localStorage');\n        }\n      } catch (error) {\n        console.error('Errore generale durante la verifica dell\\'autenticazione:', error);\n        // In caso di errore generale, assicurati che l'utente non sia autenticato\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n\n        // Pulisci eventuali selezioni di cantiere precedenti\n        localStorage.removeItem('selectedCantiereId');\n        localStorage.removeItem('selectedCantiereName');\n        console.log('Rimossi dati cantiere precedenti dal localStorage');\n      } finally {\n        // Assicurati che loading sia impostato a false alla fine\n        console.log('Completata verifica autenticazione, loading:', false);\n        setTimeout(() => {\n          setLoading(false);\n        }, 500); // Aggiungi un piccolo ritardo per assicurarsi che lo stato sia aggiornato\n      }\n    };\n\n    // Esegui la verifica dell'autenticazione\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login come ${loginType}`, credentials);\n      const response = await authService.login(credentials, loginType);\n      console.log('Risposta login ricevuta:', response);\n\n      const { access_token, user_id, username, role, cantiere_id, cantiere_name } = response;\n\n      // Pulisci eventuali selezioni di cantiere precedenti\n      // Questo risolve il problema del cantiere che rimane in memoria tra sessioni diverse\n      localStorage.removeItem('selectedCantiereId');\n      localStorage.removeItem('selectedCantiereName');\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\n\n      // Se è un login cantiere, salva l'ID e il nome del cantiere nel localStorage\n      if (loginType === 'cantiere' && cantiere_id) {\n        console.log('Login cantiere: salvando ID cantiere nel localStorage:', cantiere_id);\n        localStorage.setItem('selectedCantiereId', cantiere_id.toString());\n        localStorage.setItem('selectedCantiereName', cantiere_name || `Cantiere ${cantiere_id}`);\n      }\n\n      // Salva il token nel localStorage\n      console.log('Salvataggio token nel localStorage');\n      localStorage.setItem('token', access_token);\n\n      // Imposta i dati dell'utente\n      const userData = { id: user_id, username, role };\n      console.log('Impostazione dati utente:', userData);\n      setUser(userData);\n      setIsAuthenticated(true);\n\n      // Se è un utente cantiere, salva l'ID e il nome del cantiere nel localStorage\n      if (role === 'cantieri_user' && cantiere_id) {\n        console.log('Utente cantiere, salvataggio dati cantiere:', { cantiere_id, cantiere_name });\n        localStorage.setItem('selectedCantiereId', cantiere_id.toString());\n        localStorage.setItem('selectedCantiereName', cantiere_name || `Cantiere ${cantiere_id}`);\n      }\n\n      // Resetta lo stato di impersonificazione a false quando si effettua un login normale\n      if (isImpersonating) {\n        console.log('Reset dello stato di impersonificazione durante login normale');\n        localStorage.removeItem('isImpersonating');\n        setIsImpersonating(false);\n      }\n\n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    // Se l'utente corrente è un amministratore che ha effettuato \"login as user\",\n    // reindirizza alla dashboard amministratore invece che alla pagina di login\n    if (isImpersonating) {\n      console.log('Logout da utente impersonato, ritorno al menu amministratore');\n\n      // Rimuovi i dati dell'utente impersonato\n      localStorage.removeItem('impersonatedUser');\n      localStorage.removeItem('isImpersonating');\n      setImpersonatedUser(null);\n      setIsImpersonating(false);\n\n      // Reindirizza alla dashboard amministratore\n      navigate('/dashboard/admin');\n    } else {\n      // Logout normale\n      console.log('Logout normale, ritorno alla pagina di login');\n      localStorage.removeItem('token');\n      localStorage.removeItem('isImpersonating');\n      localStorage.removeItem('impersonatedUser');\n      localStorage.removeItem('selectedCantiereId');\n      localStorage.removeItem('selectedCantiereName');\n      setUser(null);\n      setImpersonatedUser(null);\n      setIsAuthenticated(false);\n      navigate('/login');\n    }\n  };\n\n  // Funzione per impostare il token\n  const setToken = (token) => {\n    localStorage.setItem('token', token);\n  };\n\n  // Funzione per impersonare un utente\n  const impersonateUser = async (userId) => {\n    try {\n      // Chiama l'endpoint di impersonificazione\n      const response = await authService.impersonateUser(userId);\n\n      // Salva il token nel localStorage\n      localStorage.setItem('token', response.access_token);\n\n      // Salva i dati dell'utente impersonato\n      const impersonatedUserData = {\n        id: response.impersonated_id,\n        username: response.impersonated_username,\n        role: response.impersonated_role\n      };\n\n      // Salva i dati dell'utente impersonato nel localStorage\n      localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\n      setImpersonatedUser(impersonatedUserData);\n\n      // Imposta lo stato di impersonificazione a true\n      setIsImpersonating(true);\n      localStorage.setItem('isImpersonating', 'true');\n\n      // L'utente rimane l'amministratore, ma ora ha informazioni sull'utente impersonato\n      // Non modifichiamo lo stato dell'utente perché rimane l'amministratore\n\n      console.log('Impersonificazione attivata, logout riporterà al menu amministratore');\n      console.log('Utente impersonato:', impersonatedUserData.username, 'Ruolo:', impersonatedUserData.role);\n\n      return {\n        ...user,  // Mantiene i dati dell'amministratore\n        isImpersonated: true,\n        impersonatedUser: impersonatedUserData  // Aggiunge i dati dell'utente impersonato\n      };\n    } catch (error) {\n      console.error('Errore durante l\\'impersonificazione:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per selezionare un cantiere\n  const selectCantiere = (cantiere) => {\n    if (cantiere && cantiere.id_cantiere) {\n      // Salva nel localStorage\n      localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString());\n      localStorage.setItem('selectedCantiereName', cantiere.nome || `Cantiere ${cantiere.id_cantiere}`);\n\n      // Aggiorna lo stato\n      setSelectedCantiere(cantiere);\n      console.log('Cantiere selezionato:', cantiere);\n    }\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    setUser,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    setToken,\n    impersonateUser,\n    isImpersonating,\n    impersonatedUser,\n    selectedCantiere,\n    selectCantiere\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGR,aAAa,CAAC,IAAI,CAAC;;AAEvC;AACA,OAAO,MAAMS,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMR,UAAU,CAACM,WAAW,CAAC;AAAA;AAACE,EAAA,CAAxCD,OAAO;AAEpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAACqB,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,KAAK,MAAM,CAAC;EAC1G,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAACyB,IAAI,CAACC,KAAK,CAACL,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,MAAM,CAAC,CAAC;EACxH,MAAM,CAACK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM6B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;;EAE9B;EACAD,SAAS,CAAC,MAAM;IACd,MAAM4B,UAAU,GAAGT,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;IAC7D,MAAMS,YAAY,GAAGV,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IAEjE,IAAIQ,UAAU,EAAE;MACdF,mBAAmB,CAAC;QAClBI,WAAW,EAAEC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;QACrCI,IAAI,EAAEH,YAAY,IAAI,YAAYD,UAAU;MAC9C,CAAC,CAAC;MACFK,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;QAAEJ,WAAW,EAAEF,UAAU;QAAEI,IAAI,EAAEH;MAAa,CAAC,CAAC;IACrG;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7B,SAAS,CAAC,MAAM;IACd,MAAMmC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD;QACAlB,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMoB,KAAK,GAAGjB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3Ca,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEE,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;QAEnE,IAAIA,KAAK,EAAE;UACT,IAAI;YACF;YACAH,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7C,MAAMG,QAAQ,GAAG,MAAMnC,WAAW,CAACoC,UAAU,CAAC,CAAC;YAC/CL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEG,QAAQ,CAAC;YACnDzB,OAAO,CAACyB,QAAQ,CAAC;YACjBvB,kBAAkB,CAAC,IAAI,CAAC;;YAExB;YACA;YACA,MAAMyB,kBAAkB,GAAGF,QAAQ,CAACG,cAAc,KAAK,IAAI;YAC3DP,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAEK,kBAAkB,CAAC;YAC1FrB,kBAAkB,CAACqB,kBAAkB,CAAC;UACxC,CAAC,CAAC,OAAOE,UAAU,EAAE;YACnBR,OAAO,CAACS,KAAK,CAAC,uCAAuC,EAAED,UAAU,CAAC;YAClE;YACAR,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;YAC1Df,YAAY,CAACwB,UAAU,CAAC,OAAO,CAAC;YAChC/B,OAAO,CAAC,IAAI,CAAC;YACbE,kBAAkB,CAAC,KAAK,CAAC;YACzBK,YAAY,CAACwB,UAAU,CAAC,iBAAiB,CAAC;YAC1CzB,kBAAkB,CAAC,KAAK,CAAC;;YAEzB;YACAC,YAAY,CAACwB,UAAU,CAAC,oBAAoB,CAAC;YAC7CxB,YAAY,CAACwB,UAAU,CAAC,sBAAsB,CAAC;YAC/CV,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAClE;QACF,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3DtB,OAAO,CAAC,IAAI,CAAC;UACbE,kBAAkB,CAAC,KAAK,CAAC;UACzBK,YAAY,CAACwB,UAAU,CAAC,iBAAiB,CAAC;UAC1CzB,kBAAkB,CAAC,KAAK,CAAC;;UAEzB;UACAC,YAAY,CAACwB,UAAU,CAAC,oBAAoB,CAAC;UAC7CxB,YAAY,CAACwB,UAAU,CAAC,sBAAsB,CAAC;UAC/CV,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAClE;MACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;QACjF;QACAvB,YAAY,CAACwB,UAAU,CAAC,OAAO,CAAC;QAChC/B,OAAO,CAAC,IAAI,CAAC;QACbE,kBAAkB,CAAC,KAAK,CAAC;;QAEzB;QACAK,YAAY,CAACwB,UAAU,CAAC,oBAAoB,CAAC;QAC7CxB,YAAY,CAACwB,UAAU,CAAC,sBAAsB,CAAC;QAC/CV,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAClE,CAAC,SAAS;QACR;QACAD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE,KAAK,CAAC;QAClEU,UAAU,CAAC,MAAM;UACf5B,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACX;IACF,CAAC;;IAED;IACAmB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,KAAK,GAAG,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IAC9C,IAAI;MACFd,OAAO,CAACC,GAAG,CAAC,2BAA2Ba,SAAS,EAAE,EAAED,WAAW,CAAC;MAChE,MAAME,QAAQ,GAAG,MAAM9C,WAAW,CAAC2C,KAAK,CAACC,WAAW,EAAEC,SAAS,CAAC;MAChEd,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEc,QAAQ,CAAC;MAEjD,MAAM;QAAEC,YAAY;QAAEC,OAAO;QAAEC,QAAQ;QAAEC,IAAI;QAAEC,WAAW;QAAEC;MAAc,CAAC,GAAGN,QAAQ;;MAEtF;MACA;MACA7B,YAAY,CAACwB,UAAU,CAAC,oBAAoB,CAAC;MAC7CxB,YAAY,CAACwB,UAAU,CAAC,sBAAsB,CAAC;MAC/CV,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;MAEhE;MACA,IAAIa,SAAS,KAAK,UAAU,IAAIM,WAAW,EAAE;QAC3CpB,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEmB,WAAW,CAAC;QAClFlC,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAEF,WAAW,CAACG,QAAQ,CAAC,CAAC,CAAC;QAClErC,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAED,aAAa,IAAI,YAAYD,WAAW,EAAE,CAAC;MAC1F;;MAEA;MACApB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDf,YAAY,CAACoC,OAAO,CAAC,OAAO,EAAEN,YAAY,CAAC;;MAE3C;MACA,MAAMZ,QAAQ,GAAG;QAAEoB,EAAE,EAAEP,OAAO;QAAEC,QAAQ;QAAEC;MAAK,CAAC;MAChDnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,QAAQ,CAAC;MAClDzB,OAAO,CAACyB,QAAQ,CAAC;MACjBvB,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,IAAIsC,IAAI,KAAK,eAAe,IAAIC,WAAW,EAAE;QAC3CpB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE;UAAEmB,WAAW;UAAEC;QAAc,CAAC,CAAC;QAC1FnC,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAEF,WAAW,CAACG,QAAQ,CAAC,CAAC,CAAC;QAClErC,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAED,aAAa,IAAI,YAAYD,WAAW,EAAE,CAAC;MAC1F;;MAEA;MACA,IAAIpC,eAAe,EAAE;QACnBgB,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;QAC5Ef,YAAY,CAACwB,UAAU,CAAC,iBAAiB,CAAC;QAC1CzB,kBAAkB,CAAC,KAAK,CAAC;MAC3B;MAEA,OAAOmB,QAAQ;IACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMgB,MAAM,GAAGA,CAAA,KAAM;IACnB;IACA;IACA,IAAIzC,eAAe,EAAE;MACnBgB,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;;MAE3E;MACAf,YAAY,CAACwB,UAAU,CAAC,kBAAkB,CAAC;MAC3CxB,YAAY,CAACwB,UAAU,CAAC,iBAAiB,CAAC;MAC1CrB,mBAAmB,CAAC,IAAI,CAAC;MACzBJ,kBAAkB,CAAC,KAAK,CAAC;;MAEzB;MACAS,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACL;MACAM,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3Df,YAAY,CAACwB,UAAU,CAAC,OAAO,CAAC;MAChCxB,YAAY,CAACwB,UAAU,CAAC,iBAAiB,CAAC;MAC1CxB,YAAY,CAACwB,UAAU,CAAC,kBAAkB,CAAC;MAC3CxB,YAAY,CAACwB,UAAU,CAAC,oBAAoB,CAAC;MAC7CxB,YAAY,CAACwB,UAAU,CAAC,sBAAsB,CAAC;MAC/C/B,OAAO,CAAC,IAAI,CAAC;MACbU,mBAAmB,CAAC,IAAI,CAAC;MACzBR,kBAAkB,CAAC,KAAK,CAAC;MACzBa,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMgC,QAAQ,GAAIvB,KAAK,IAAK;IAC1BjB,YAAY,CAACoC,OAAO,CAAC,OAAO,EAAEnB,KAAK,CAAC;EACtC,CAAC;;EAED;EACA,MAAMwB,eAAe,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAMb,QAAQ,GAAG,MAAM9C,WAAW,CAAC0D,eAAe,CAACC,MAAM,CAAC;;MAE1D;MACA1C,YAAY,CAACoC,OAAO,CAAC,OAAO,EAAEP,QAAQ,CAACC,YAAY,CAAC;;MAEpD;MACA,MAAMa,oBAAoB,GAAG;QAC3BL,EAAE,EAAET,QAAQ,CAACe,eAAe;QAC5BZ,QAAQ,EAAEH,QAAQ,CAACgB,qBAAqB;QACxCZ,IAAI,EAAEJ,QAAQ,CAACiB;MACjB,CAAC;;MAED;MACA9C,YAAY,CAACoC,OAAO,CAAC,kBAAkB,EAAEhC,IAAI,CAAC2C,SAAS,CAACJ,oBAAoB,CAAC,CAAC;MAC9ExC,mBAAmB,CAACwC,oBAAoB,CAAC;;MAEzC;MACA5C,kBAAkB,CAAC,IAAI,CAAC;MACxBC,YAAY,CAACoC,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;;MAE/C;MACA;;MAEAtB,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;MACnFD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4B,oBAAoB,CAACX,QAAQ,EAAE,QAAQ,EAAEW,oBAAoB,CAACV,IAAI,CAAC;MAEtG,OAAO;QACL,GAAGzC,IAAI;QAAG;QACV6B,cAAc,EAAE,IAAI;QACpBnB,gBAAgB,EAAEyC,oBAAoB,CAAE;MAC1C,CAAC;IACH,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMyB,cAAc,GAAIC,QAAQ,IAAK;IACnC,IAAIA,QAAQ,IAAIA,QAAQ,CAACtC,WAAW,EAAE;MACpC;MACAX,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAEa,QAAQ,CAACtC,WAAW,CAAC0B,QAAQ,CAAC,CAAC,CAAC;MAC3ErC,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAEa,QAAQ,CAACpC,IAAI,IAAI,YAAYoC,QAAQ,CAACtC,WAAW,EAAE,CAAC;;MAEjG;MACAJ,mBAAmB,CAAC0C,QAAQ,CAAC;MAC7BnC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEkC,QAAQ,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMC,KAAK,GAAG;IACZ1D,IAAI;IACJC,OAAO;IACPC,eAAe;IACfE,OAAO;IACP8B,KAAK;IACLa,MAAM;IACNC,QAAQ;IACRC,eAAe;IACf3C,eAAe;IACfI,gBAAgB;IAChBI,gBAAgB;IAChB0C;EACF,CAAC;EAED,oBAAO/D,OAAA,CAACC,WAAW,CAACiE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA5D,QAAA,EAAEA;EAAQ;IAAA8D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAAChE,GAAA,CAnQWF,YAAY;EAAA,QAONP,WAAW;AAAA;AAAA0E,EAAA,GAPjBnE,YAAY;AAAA,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}