{"ast": null, "code": "import { InternMap } from \"internmap\";\nimport identity from \"./identity.js\";\nexport default function group(values) {\n  for (var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    keys[_key - 1] = arguments[_key];\n  }\n  return nest(values, identity, identity, keys);\n}\nexport function groups(values) {\n  for (var _len2 = arguments.length, keys = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    keys[_key2 - 1] = arguments[_key2];\n  }\n  return nest(values, Array.from, identity, keys);\n}\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(_ref => {\n      let [key, value] = _ref;\n      return [...g, key, value];\n    }));\n  }\n  return groups;\n}\nexport function flatGroup(values) {\n  for (var _len3 = arguments.length, keys = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    keys[_key3 - 1] = arguments[_key3];\n  }\n  return flatten(groups(values, ...keys), keys);\n}\nexport function flatRollup(values, reduce) {\n  for (var _len4 = arguments.length, keys = new Array(_len4 > 2 ? _len4 - 2 : 0), _key4 = 2; _key4 < _len4; _key4++) {\n    keys[_key4 - 2] = arguments[_key4];\n  }\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\nexport function rollup(values, reduce) {\n  for (var _len5 = arguments.length, keys = new Array(_len5 > 2 ? _len5 - 2 : 0), _key5 = 2; _key5 < _len5; _key5++) {\n    keys[_key5 - 2] = arguments[_key5];\n  }\n  return nest(values, identity, reduce, keys);\n}\nexport function rollups(values, reduce) {\n  for (var _len6 = arguments.length, keys = new Array(_len6 > 2 ? _len6 - 2 : 0), _key6 = 2; _key6 < _len6; _key6++) {\n    keys[_key6 - 2] = arguments[_key6];\n  }\n  return nest(values, Array.from, reduce, keys);\n}\nexport function index(values) {\n  for (var _len7 = arguments.length, keys = new Array(_len7 > 1 ? _len7 - 1 : 0), _key7 = 1; _key7 < _len7; _key7++) {\n    keys[_key7 - 1] = arguments[_key7];\n  }\n  return nest(values, identity, unique, keys);\n}\nexport function indexes(values) {\n  for (var _len8 = arguments.length, keys = new Array(_len8 > 1 ? _len8 - 1 : 0), _key8 = 1; _key8 < _len8; _key8++) {\n    keys[_key8 - 1] = arguments[_key8];\n  }\n  return nest(values, Array.from, unique, keys);\n}\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\nfunction nest(values, map, reduce, keys) {\n  return function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  }(values, 0);\n}", "map": {"version": 3, "names": ["InternMap", "identity", "group", "values", "_len", "arguments", "length", "keys", "Array", "_key", "nest", "groups", "_len2", "_key2", "from", "flatten", "i", "n", "flatMap", "g", "pop", "map", "_ref", "key", "value", "flatGroup", "_len3", "_key3", "flatRollup", "reduce", "_len4", "_key4", "rollups", "rollup", "_len5", "_key5", "_len6", "_key6", "index", "_len7", "_key7", "unique", "indexes", "_len8", "_key8", "Error", "regroup", "keyof", "get", "push", "set"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-array/src/group.js"], "sourcesContent": ["import {InternMap} from \"internmap\";\nimport identity from \"./identity.js\";\n\nexport default function group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\n\nexport function groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\n\nexport function flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nexport function flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nexport function rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\n\nexport function rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nexport function index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\n\nexport function indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AACnC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,KAAKA,CAACC,MAAM,EAAW;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC3C,OAAOC,IAAI,CAACP,MAAM,EAAEF,QAAQ,EAAEA,QAAQ,EAAEM,IAAI,CAAC;AAC/C;AAEA,OAAO,SAASI,MAAMA,CAACR,MAAM,EAAW;EAAA,SAAAS,KAAA,GAAAP,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAI,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJN,IAAI,CAAAM,KAAA,QAAAR,SAAA,CAAAQ,KAAA;EAAA;EACpC,OAAOH,IAAI,CAACP,MAAM,EAAEK,KAAK,CAACM,IAAI,EAAEb,QAAQ,EAAEM,IAAI,CAAC;AACjD;AAEA,SAASQ,OAAOA,CAACJ,MAAM,EAAEJ,IAAI,EAAE;EAC7B,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGV,IAAI,CAACD,MAAM,EAAEU,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC3CL,MAAM,GAAGA,MAAM,CAACO,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,CAACC,IAAA;MAAA,IAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAAF,IAAA;MAAA,OAAK,CAAC,GAAGH,CAAC,EAAEI,GAAG,EAAEC,KAAK,CAAC;IAAA,EAAC,CAAC;EACjF;EACA,OAAOb,MAAM;AACf;AAEA,OAAO,SAASc,SAASA,CAACtB,MAAM,EAAW;EAAA,SAAAuB,KAAA,GAAArB,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAkB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJpB,IAAI,CAAAoB,KAAA,QAAAtB,SAAA,CAAAsB,KAAA;EAAA;EACvC,OAAOZ,OAAO,CAACJ,MAAM,CAACR,MAAM,EAAE,GAAGI,IAAI,CAAC,EAAEA,IAAI,CAAC;AAC/C;AAEA,OAAO,SAASqB,UAAUA,CAACzB,MAAM,EAAE0B,MAAM,EAAW;EAAA,SAAAC,KAAA,GAAAzB,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAsB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJxB,IAAI,CAAAwB,KAAA,QAAA1B,SAAA,CAAA0B,KAAA;EAAA;EAChD,OAAOhB,OAAO,CAACiB,OAAO,CAAC7B,MAAM,EAAE0B,MAAM,EAAE,GAAGtB,IAAI,CAAC,EAAEA,IAAI,CAAC;AACxD;AAEA,OAAO,SAAS0B,MAAMA,CAAC9B,MAAM,EAAE0B,MAAM,EAAW;EAAA,SAAAK,KAAA,GAAA7B,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAA0B,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJ5B,IAAI,CAAA4B,KAAA,QAAA9B,SAAA,CAAA8B,KAAA;EAAA;EAC5C,OAAOzB,IAAI,CAACP,MAAM,EAAEF,QAAQ,EAAE4B,MAAM,EAAEtB,IAAI,CAAC;AAC7C;AAEA,OAAO,SAASyB,OAAOA,CAAC7B,MAAM,EAAE0B,MAAM,EAAW;EAAA,SAAAO,KAAA,GAAA/B,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAA4B,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJ9B,IAAI,CAAA8B,KAAA,QAAAhC,SAAA,CAAAgC,KAAA;EAAA;EAC7C,OAAO3B,IAAI,CAACP,MAAM,EAAEK,KAAK,CAACM,IAAI,EAAEe,MAAM,EAAEtB,IAAI,CAAC;AAC/C;AAEA,OAAO,SAAS+B,KAAKA,CAACnC,MAAM,EAAW;EAAA,SAAAoC,KAAA,GAAAlC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAA+B,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJjC,IAAI,CAAAiC,KAAA,QAAAnC,SAAA,CAAAmC,KAAA;EAAA;EACnC,OAAO9B,IAAI,CAACP,MAAM,EAAEF,QAAQ,EAAEwC,MAAM,EAAElC,IAAI,CAAC;AAC7C;AAEA,OAAO,SAASmC,OAAOA,CAACvC,MAAM,EAAW;EAAA,SAAAwC,KAAA,GAAAtC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAmC,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJrC,IAAI,CAAAqC,KAAA,QAAAvC,SAAA,CAAAuC,KAAA;EAAA;EACrC,OAAOlC,IAAI,CAACP,MAAM,EAAEK,KAAK,CAACM,IAAI,EAAE2B,MAAM,EAAElC,IAAI,CAAC;AAC/C;AAEA,SAASkC,MAAMA,CAACtC,MAAM,EAAE;EACtB,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,MAAM,IAAIuC,KAAK,CAAC,eAAe,CAAC;EACzD,OAAO1C,MAAM,CAAC,CAAC,CAAC;AAClB;AAEA,SAASO,IAAIA,CAACP,MAAM,EAAEkB,GAAG,EAAEQ,MAAM,EAAEtB,IAAI,EAAE;EACvC,OAAQ,SAASuC,OAAOA,CAAC3C,MAAM,EAAEa,CAAC,EAAE;IAClC,IAAIA,CAAC,IAAIT,IAAI,CAACD,MAAM,EAAE,OAAOuB,MAAM,CAAC1B,MAAM,CAAC;IAC3C,MAAMQ,MAAM,GAAG,IAAIX,SAAS,CAAC,CAAC;IAC9B,MAAM+C,KAAK,GAAGxC,IAAI,CAACS,CAAC,EAAE,CAAC;IACvB,IAAIsB,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,MAAMd,KAAK,IAAIrB,MAAM,EAAE;MAC1B,MAAMoB,GAAG,GAAGwB,KAAK,CAACvB,KAAK,EAAE,EAAEc,KAAK,EAAEnC,MAAM,CAAC;MACzC,MAAMD,KAAK,GAAGS,MAAM,CAACqC,GAAG,CAACzB,GAAG,CAAC;MAC7B,IAAIrB,KAAK,EAAEA,KAAK,CAAC+C,IAAI,CAACzB,KAAK,CAAC,CAAC,KACxBb,MAAM,CAACuC,GAAG,CAAC3B,GAAG,EAAE,CAACC,KAAK,CAAC,CAAC;IAC/B;IACA,KAAK,MAAM,CAACD,GAAG,EAAEpB,MAAM,CAAC,IAAIQ,MAAM,EAAE;MAClCA,MAAM,CAACuC,GAAG,CAAC3B,GAAG,EAAEuB,OAAO,CAAC3C,MAAM,EAAEa,CAAC,CAAC,CAAC;IACrC;IACA,OAAOK,GAAG,CAACV,MAAM,CAAC;EACpB,CAAC,CAAER,MAAM,EAAE,CAAC,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}