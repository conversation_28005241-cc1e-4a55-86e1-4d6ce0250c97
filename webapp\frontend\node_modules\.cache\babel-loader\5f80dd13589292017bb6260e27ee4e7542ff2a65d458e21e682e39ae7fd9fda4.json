{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Stepper, Step, StepLabel, Grid, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemSecondaryAction, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, ArrowBack as ArrowBackIcon, ArrowForward as ArrowForwardIcon, Cancel as CancelIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Associa Bobina', 'Inserisci Metri', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 1) {\n      // Carica le bobine quando si passa al passo \"Associa Bobina\"\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n      // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n      setCavi(caviData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione);\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || cavoData.metratura_reale && cavoData.metratura_reale > 0) {\n        // Mostra il dialogo per cavi già posati\n        setAlreadyLaidCavo(cavoData);\n        setShowAlreadyLaidDialog(true);\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n      onError('Cavo non trovato o errore nella ricerca: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo (Associa Bobina)\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo (Associa Bobina)\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n\n        // Chiedi conferma all'utente\n        if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m).\\n\\nVuoi continuare comunque?`)) {\n          isValid = false;\n        }\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n\n          // Chiedi conferma all'utente\n          if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n            isValid = false;\n          }\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      let idBobina = formData.id_bobina || null;\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n      }\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      let forceOver = false;\n      if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio\n      const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n      if (!window.confirm(confirmMessage)) {\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 27\n                  }, this), isCableSpare(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 29\n                  }, this) : isCableInstalled(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent triggering the ListItem click\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n        cavo: selectedCavo,\n        compact: true,\n        title: \"Dettagli del cavo selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni importanti:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Metri teorici del cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 15\n            }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Stato attuale del cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 15\n            }, this), \" \", selectedCavo.stato_installazione || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            return bobina ? /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri residui bobina:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 19\n              }, this), \" \", bobina.metri_residui || 0, \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 17\n            }, this) : null;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Metri posati\",\n          variant: \"outlined\",\n          name: \"metri_posati\",\n          type: \"number\",\n          value: formData.metri_posati,\n          onChange: handleFormChange,\n          error: !!formErrors.metri_posati,\n          helperText: formErrors.metri_posati || formWarnings.metri_posati,\n          FormHelperTextProps: {\n            sx: {\n              color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n            }\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione opzioni speciali\n      if (numeroBobina.toLowerCase() === 'v') {\n        // Opzione 'v' per bobina vuota\n        setFormData({\n          ...formData,\n          id_bobina: 'BOBINA_VUOTA'\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina.toLowerCase() === 'q') {\n        // Opzione 'q' per annullare\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo && (bobinaEsistente.tipologia !== selectedCavo.tipologia || String(bobinaEsistente.n_conduttori) !== String(selectedCavo.n_conduttori) || String(bobinaEsistente.sezione) !== String(selectedCavo.sezione))) {\n            // Mostra il dialogo per bobine incompatibili\n            setIncompatibleReel(bobinaEsistente);\n            setShowIncompatibleReelDialog(true);\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina (opzionale)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Puoi associare una bobina al cavo selezionato. Questo \\xE8 opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Inserisci numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Inserisci solo il numero (Y) o 'v' per bobina vuota\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By), 'v' per bobina vuota o 'q' per annullare\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mt: 1\n                },\n                children: [\"ID Bobina completo: \", formData.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : formData.id_bobina || '-']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            sx: {\n              mt: 3,\n              fontWeight: 'bold'\n            },\n            children: \"Bobine disponibili compatibili con il cavo selezionato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 15\n          }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              overflowX: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse',\n                marginBottom: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    borderBottom: '2px solid #ddd',\n                    backgroundColor: '#f5f5f5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Cond.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'right'\n                    },\n                    children: \"Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: bobine.map(bobina => {\n                  const isCompatible = selectedCavo && bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione;\n                  const hasSufficient = bobina.metri_residui >= parseFloat(formData.metri_posati || 0);\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    style: {\n                      borderBottom: '1px solid #ddd',\n                      backgroundColor: isCompatible ? hasSufficient ? '#e8f5e9' : '#fff8e1' : 'transparent',\n                      cursor: 'pointer'\n                    },\n                    onClick: () => {\n                      if (hasSufficient) {\n                        setFormData({\n                          ...formData,\n                          id_bobina: bobina.id_bobina\n                        });\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 901,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.tipologia || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 902,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.n_conduttori || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 903,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.sezione || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 904,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px',\n                        textAlign: 'right'\n                      },\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 905,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: bobina.stato_bobina,\n                        color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 907,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 906,\n                      columnNumber: 29\n                    }, this)]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2,\n              mb: 3\n            },\n            children: \"Non ci sono bobine disponibili compatibili con il cavo selezionato.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Opzioni speciali:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 932,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => {\n                    setFormData({\n                      ...formData,\n                      id_bobina: 'BOBINA_VUOTA'\n                    });\n                  },\n                  children: \"Bobina Vuota (v)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  color: \"secondary\",\n                  onClick: () => {\n                    setFormData({\n                      ...formData,\n                      id_bobina: ''\n                    });\n                  },\n                  children: \"Nessuna Bobina (q)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"bobina-select-label\",\n              children: \"Seleziona Bobina dalla lista\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"bobina-select-label\",\n              id: \"bobina-select\",\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              label: \"Seleziona Bobina dalla lista\",\n              onChange: handleFormChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"Nessuna bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"BOBINA VUOTA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 19\n              }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                disabled: bobina.metri_residui < parseFloat(formData.metri_posati),\n                children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A', \" - Residui: \", bobina.metri_residui || 0, \" m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n              children: \"Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 993,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 15\n          }, this), formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              p: 2,\n              bgcolor: 'background.paper',\n              borderRadius: 1,\n              border: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Dettagli bobina selezionata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 19\n            }, this), (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              if (bobina) {\n                return /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Numero:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1011,\n                        columnNumber: 31\n                      }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Tipologia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1014,\n                        columnNumber: 31\n                      }, this), \" \", bobina.tipologia || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1013,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Conduttori:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1017,\n                        columnNumber: 31\n                      }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1016,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1022,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1021,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1025,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1024,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Stato:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1028,\n                        columnNumber: 31\n                      }, this), \" \", bobina.stato_bobina || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1027,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 25\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"error\",\n                children: \"Bobina non trovata nel database\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 23\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 836,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 821,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1083,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1088,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1093,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1107,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1115,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1114,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1119,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1100,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1128,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1127,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1087,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1082,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: activeStep,\n      sx: {\n        mb: 4\n      },\n      children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1231,\n          columnNumber: 13\n        }, this)\n      }, label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1230,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2,\n        mb: 4\n      },\n      children: getStepContent(activeStep)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1236,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"secondary\",\n        onClick: activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1245,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: activeStep === 0 ? 'Annulla' : 'Indietro'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: activeStep === steps.length - 1 ? handleSubmit : handleNext,\n        endIcon: activeStep === steps.length - 1 ? /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1255,\n          columnNumber: 54\n        }, this) : /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1255,\n          columnNumber: 69\n        }, this),\n        disabled: loading || activeStep === 0 && !selectedCavo,\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1259,\n          columnNumber: 13\n        }, this) : activeStep === steps.length - 1 ? 'Salva' : 'Avanti'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1271,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1280,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1279,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1282,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1285,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1278,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: selectedCavo,\n      bobina: incompatibleReel,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1332,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1334,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1319,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1227,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"6FkN6PzcU3fEI2WPvKMUCdC6WqU=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Search", "SearchIcon", "Save", "SaveIcon", "ArrowBack", "ArrowBackIcon", "ArrowForward", "ArrowForwardIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "activeStep", "setActiveStep", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "steps", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "error", "console", "message", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "tipologia", "n_conduttori", "sezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "length", "log", "sort", "a", "b", "metri_residui", "handleSearchCavoById", "trim", "cavoData", "getCavoById", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "cavo", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "find", "prev", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "idBobina", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "edge", "stopPropagation", "renderStep2", "compact", "title", "bgcolor", "borderRadius", "fontWeight", "type", "helperText", "FormHelperTextProps", "mt", "renderStep3", "getBobinaNumber", "includes", "split", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "toLowerCase", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "String", "paragraph", "md", "onBlur", "overflowX", "style", "width", "borderCollapse", "marginBottom", "borderBottom", "backgroundColor", "padding", "textAlign", "isCompatible", "hasSufficient", "cursor", "id", "labelId", "border", "metri_totali", "renderStep4", "bobinaInfo", "getStepContent", "step", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoToMatchReel", "handleSelectAnotherReel", "endIcon", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gap", "mr", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Stepper,\n  Step,\n  StepLabel,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  ArrowBack as ArrowBackIcon,\n  ArrowForward as ArrowForwardIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Associa Bobina', 'Inserisci Metri', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 1) {\n      // Carica le bobine quando si passa al passo \"Associa Bobina\"\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n      // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n      setCavi(caviData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina =>\n            bobina.tipologia === selectedCavo.tipologia &&\n            bobina.n_conduttori === selectedCavo.n_conduttori &&\n            bobina.sezione === selectedCavo.sezione\n          );\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || (cavoData.metratura_reale && cavoData.metratura_reale > 0)) {\n        // Mostra il dialogo per cavi già posati\n        setAlreadyLaidCavo(cavoData);\n        setShowAlreadyLaidDialog(true);\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n      onError('Cavo non trovato o errore nella ricerca: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo (Associa Bobina)\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo (Associa Bobina)\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n\n        // Chiedi conferma all'utente\n        if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m).\\n\\nVuoi continuare comunque?`)) {\n          isValid = false;\n        }\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n\n          // Chiedi conferma all'utente\n          if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n            isValid = false;\n          }\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      let idBobina = formData.id_bobina || null;\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n      }\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      let forceOver = false;\n      if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio\n      const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n      if (!window.confirm(confirmMessage)) {\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isCableSpare(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : isCableInstalled(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={(e) => {\n                        e.stopPropagation(); // Prevent triggering the ListItem click\n                        setSelectedCavo(cavo);\n                        setShowCavoDetailsDialog(true);\n                      }}>\n                        <InfoIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <CavoDetailsView\n          cavo={selectedCavo}\n          compact={true}\n          title=\"Dettagli del cavo selezionato\"\n        />\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina */}\n          <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni importanti:\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Metri teorici del cavo:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Stato attuale del cavo:</strong> {selectedCavo.stato_installazione || 'N/A'}\n            </Typography>\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              return bobina ? (\n                <Typography variant=\"body2\">\n                  <strong>Metri residui bobina:</strong> {bobina.metri_residui || 0} m\n                </Typography>\n              ) : null;\n            })()}\n          </Box>\n\n          <TextField\n            fullWidth\n            label=\"Metri posati\"\n            variant=\"outlined\"\n            name=\"metri_posati\"\n            type=\"number\"\n            value={formData.metri_posati}\n            onChange={handleFormChange}\n            error={!!formErrors.metri_posati}\n            helperText={formErrors.metri_posati || formWarnings.metri_posati}\n            FormHelperTextProps={{\n              sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n            }}\n            sx={{ mb: 2 }}\n          />\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione opzioni speciali\n      if (numeroBobina.toLowerCase() === 'v') {\n        // Opzione 'v' per bobina vuota\n        setFormData({\n          ...formData,\n          id_bobina: 'BOBINA_VUOTA'\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina.toLowerCase() === 'q') {\n        // Opzione 'q' per annullare\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo && (\n              bobinaEsistente.tipologia !== selectedCavo.tipologia ||\n              String(bobinaEsistente.n_conduttori) !== String(selectedCavo.n_conduttori) ||\n              String(bobinaEsistente.sezione) !== String(selectedCavo.sezione)\n          )) {\n            // Mostra il dialogo per bobine incompatibili\n            setIncompatibleReel(bobinaEsistente);\n            setShowIncompatibleReelDialog(true);\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina (opzionale)\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Puoi associare una bobina al cavo selezionato. Questo è opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {/* Input diretto del numero della bobina */}\n              <Grid container spacing={2} sx={{ mb: 3 }}>\n                <Grid item xs={12} md={6}>\n                  <TextField\n                    fullWidth\n                    label=\"Inserisci numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Inserisci solo il numero (Y) o 'v' per bobina vuota\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By), 'v' per bobina vuota o 'q' per annullare\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                  />\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                    ID Bobina completo: {formData.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : formData.id_bobina || '-'}\n                  </Typography>\n                </Grid>\n              </Grid>\n\n              {/* Tabella delle bobine disponibili */}\n              <Typography variant=\"subtitle1\" gutterBottom sx={{ mt: 3, fontWeight: 'bold' }}>\n                Bobine disponibili compatibili con il cavo selezionato\n              </Typography>\n\n              {bobine.length > 0 ? (\n                <Box sx={{ overflowX: 'auto' }}>\n                  <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '20px' }}>\n                    <thead>\n                      <tr style={{ borderBottom: '2px solid #ddd', backgroundColor: '#f5f5f5' }}>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>ID</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Tipologia</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Cond.</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Sezione</th>\n                        <th style={{ padding: '8px', textAlign: 'right' }}>Residui</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Stato</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {bobine.map((bobina) => {\n                        const isCompatible = selectedCavo &&\n                          bobina.tipologia === selectedCavo.tipologia &&\n                          bobina.n_conduttori === selectedCavo.n_conduttori &&\n                          bobina.sezione === selectedCavo.sezione;\n\n                        const hasSufficient = bobina.metri_residui >= parseFloat(formData.metri_posati || 0);\n\n                        return (\n                          <tr\n                            key={bobina.id_bobina}\n                            style={{\n                              borderBottom: '1px solid #ddd',\n                              backgroundColor: isCompatible ? (hasSufficient ? '#e8f5e9' : '#fff8e1') : 'transparent',\n                              cursor: 'pointer'\n                            }}\n                            onClick={() => {\n                              if (hasSufficient) {\n                                setFormData({\n                                  ...formData,\n                                  id_bobina: bobina.id_bobina\n                                });\n                              }\n                            }}\n                          >\n                            <td style={{ padding: '8px' }}>{getBobinaNumber(bobina.id_bobina)}</td>\n                            <td style={{ padding: '8px' }}>{bobina.tipologia || 'N/A'}</td>\n                            <td style={{ padding: '8px' }}>{bobina.n_conduttori || 'N/A'}</td>\n                            <td style={{ padding: '8px' }}>{bobina.sezione || 'N/A'}</td>\n                            <td style={{ padding: '8px', textAlign: 'right' }}>{bobina.metri_residui || 0} m</td>\n                            <td style={{ padding: '8px' }}>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina}\n                                color={\n                                  bobina.stato_bobina === 'Disponibile' ? 'success' :\n                                  bobina.stato_bobina === 'In uso' ? 'primary' :\n                                  bobina.stato_bobina === 'Over' ? 'error' :\n                                  bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                                }\n                              />\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </Box>\n              ) : (\n                <Alert severity=\"info\" sx={{ mt: 2, mb: 3 }}>\n                  Non ci sono bobine disponibili compatibili con il cavo selezionato.\n                </Alert>\n              )}\n\n              {/* Opzioni speciali */}\n              <Box sx={{ mt: 3, mb: 3 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Opzioni speciali:\n                </Typography>\n                <Grid container spacing={2}>\n                  <Grid item>\n                    <Button\n                      variant=\"outlined\"\n                      onClick={() => {\n                        setFormData({\n                          ...formData,\n                          id_bobina: 'BOBINA_VUOTA'\n                        });\n                      }}\n                    >\n                      Bobina Vuota (v)\n                    </Button>\n                  </Grid>\n                  <Grid item>\n                    <Button\n                      variant=\"outlined\"\n                      color=\"secondary\"\n                      onClick={() => {\n                        setFormData({\n                          ...formData,\n                          id_bobina: ''\n                        });\n                      }}\n                    >\n                      Nessuna Bobina (q)\n                    </Button>\n                  </Grid>\n                </Grid>\n              </Box>\n\n              {/* Selezione dalla lista (manteniamo anche questa per compatibilità) */}\n              <FormControl fullWidth>\n                <InputLabel id=\"bobina-select-label\">Seleziona Bobina dalla lista</InputLabel>\n                <Select\n                  labelId=\"bobina-select-label\"\n                  id=\"bobina-select\"\n                  name=\"id_bobina\"\n                  value={formData.id_bobina}\n                  label=\"Seleziona Bobina dalla lista\"\n                  onChange={handleFormChange}\n                >\n                  <MenuItem value=\"\">\n                    <em>Nessuna bobina</em>\n                  </MenuItem>\n                  <MenuItem value=\"BOBINA_VUOTA\">\n                    <em>BOBINA VUOTA</em>\n                  </MenuItem>\n                  {bobine.map((bobina) => (\n                    <MenuItem\n                      key={bobina.id_bobina}\n                      value={bobina.id_bobina}\n                      disabled={bobina.metri_residui < parseFloat(formData.metri_posati)}\n                    >\n                      {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'} - Residui: {bobina.metri_residui || 0} m\n                    </MenuItem>\n                  ))}\n                </Select>\n                <FormHelperText>\n                  Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\n                </FormHelperText>\n              </FormControl>\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {formData.id_bobina && (\n                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Dettagli bobina selezionata\n                  </Typography>\n                  {(() => {\n                    const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                    if (bobina) {\n                      return (\n                        <Grid container spacing={2}>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                            </Typography>\n                          </Grid>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                            </Typography>\n                          </Grid>\n                        </Grid>\n                      );\n                    }\n                    return (\n                      <Typography variant=\"body2\" color=\"error\">\n                        Bobina non trovata nel database\n                      </Typography>\n                    );\n                  })()}\n                </Box>\n              )}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n        {steps.map((label) => (\n          <Step key={label}>\n            <StepLabel>{label}</StepLabel>\n          </Step>\n        ))}\n      </Stepper>\n\n      <Box sx={{ mt: 2, mb: 4 }}>\n        {getStepContent(activeStep)}\n      </Box>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n        <Button\n          variant=\"outlined\"\n          color=\"secondary\"\n          onClick={activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack}\n          startIcon={<ArrowBackIcon />}\n          disabled={loading}\n        >\n          {activeStep === 0 ? 'Annulla' : 'Indietro'}\n        </Button>\n\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}\n          endIcon={activeStep === steps.length - 1 ? <SaveIcon /> : <ArrowForwardIcon />}\n          disabled={loading || (activeStep === 0 && !selectedCavo)}\n        >\n          {loading ? (\n            <CircularProgress size={24} />\n          ) : activeStep === steps.length - 1 ? (\n            'Salva'\n          ) : (\n            'Avanti'\n          )}\n        </Button>\n      </Box>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={selectedCavo}\n        bobina={incompatibleReel}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC8E,IAAI,EAAEC,OAAO,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgF,MAAM,EAAEC,SAAS,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACsF,QAAQ,EAAEC,WAAW,CAAC,GAAGvF,QAAQ,CAAC;IACvCwF,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC6F,YAAY,EAAEC,eAAe,CAAC,GAAG9F,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAAC+F,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpG,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACqG,eAAe,EAAEC,kBAAkB,CAAC,GAAGtG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAMyG,KAAK,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,UAAU,CAAC;;EAEjF;EACAxG,SAAS,CAAC,MAAM;IACdyG,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACzC,UAAU,CAAC,CAAC;;EAEhB;EACAhE,SAAS,CAAC,MAAM;IACd,IAAIqE,UAAU,KAAK,CAAC,EAAE;MACpB;MACAqC,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACrC,UAAU,EAAEL,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAMyC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF/B,cAAc,CAAC,IAAI,CAAC;MACpB;MACA,MAAMiC,QAAQ,GAAG,MAAM9D,WAAW,CAAC+D,OAAO,CAAC5C,UAAU,CAAC;;MAEtD;MACA;MACAc,OAAO,CAAC6B,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD3C,OAAO,CAAC,mCAAmC,IAAI2C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACRrC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF9B,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMoC,UAAU,GAAG,MAAMvD,gBAAgB,CAACwD,SAAS,CAACjD,UAAU,CAAC;;MAE/D;MACA,IAAIkD,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC/C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;;MAED;MACA,IAAIpC,YAAY,EAAE;QAChB;QACA,IAAIA,YAAY,CAACqC,SAAS,IAAIrC,YAAY,CAACsC,YAAY,IAAItC,YAAY,CAACuC,OAAO,EAAE;UAC/E,MAAMC,iBAAiB,GAAGP,kBAAkB,CAACC,MAAM,CAACC,MAAM,IACxDA,MAAM,CAACE,SAAS,KAAKrC,YAAY,CAACqC,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAKtC,YAAY,CAACsC,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAKvC,YAAY,CAACuC,OAClC,CAAC;;UAED;UACA,IAAIC,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAE;YAChCR,kBAAkB,GAAGO,iBAAiB;UACxC,CAAC,MAAM;YACLX,OAAO,CAACa,GAAG,CAAC,wEAAwE,CAAC;UACvF;QACF;;QAEA;QACAT,kBAAkB,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MACtE;MAEA/C,SAAS,CAACkC,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D3C,OAAO,CAAC,uCAAuC,IAAI2C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRnC,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMoD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAC7C,WAAW,CAAC8C,IAAI,CAAC,CAAC,EAAE;MACvB/D,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFQ,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMwD,QAAQ,GAAG,MAAMrF,WAAW,CAACsF,WAAW,CAACnE,UAAU,EAAEmB,WAAW,CAAC8C,IAAI,CAAC,CAAC,CAAC;;MAE9E;MACA,IAAIC,QAAQ,CAACE,mBAAmB,KAAK,YAAY,IAAKF,QAAQ,CAACG,eAAe,IAAIH,QAAQ,CAACG,eAAe,GAAG,CAAE,EAAE;QAC/G;QACAhC,kBAAkB,CAAC6B,QAAQ,CAAC;QAC5B/B,wBAAwB,CAAC,IAAI,CAAC;QAC9BzB,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;;MAEA;MACA,IAAIwD,QAAQ,CAACI,sBAAsB,KAAK,CAAC,EAAE;QACzC;MAAA;;MAGF;MACAC,gBAAgB,CAACL,QAAQ,CAAC;IAC5B,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD3C,OAAO,CAAC,2CAA2C,IAAI2C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAChG,CAAC,SAAS;MACRrC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM6D,gBAAgB,GAAIC,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACJ,mBAAmB,KAAK,YAAY,IAAKI,IAAI,CAACH,eAAe,IAAIG,IAAI,CAACH,eAAe,GAAG,CAAE,EAAE;MACnG;MACAhC,kBAAkB,CAACmC,IAAI,CAAC;MACxBrC,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIqC,IAAI,CAACF,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAWF,IAAI,CAACjD,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACAoD,eAAe,CAACH,IAAI,CAACjD,OAAO,CAAC,CAACqD,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGL,IAAI;YAAEF,sBAAsB,EAAE;UAAE,CAAC;UAC1DpD,eAAe,CAAC2D,WAAW,CAAC;UAC5BvD,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAEsD,WAAW,CAACtD,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAlB,aAAa,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAACwE,KAAK,CAACjC,KAAK,IAAI;UAChBC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE3C,OAAO,CAAC,kDAAkD,IAAI2C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACA7B,eAAe,CAACsD,IAAI,CAAC;MACrBlD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEiD,IAAI,CAACjD,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAlB,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMqE,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAMlG,WAAW,CAAC8F,eAAe,CAAC3E,UAAU,EAAE+E,MAAM,CAAC;MACrD9E,SAAS,CAAC,QAAQ8E,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE3C,OAAO,CAAC,kDAAkD,IAAI2C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMF,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMmC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC9D,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC6D,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAE,aAAa,CAACH,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAItC,KAAK,GAAG,IAAI;IAChB,IAAIyC,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAClB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCpB,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAI0C,KAAK,CAACC,UAAU,CAACL,KAAK,CAAC,CAAC,IAAIK,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDtC,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAM4C,WAAW,GAAGD,UAAU,CAACL,KAAK,CAAC;;MAErC;MACA,IAAIlE,YAAY,IAAIA,YAAY,CAACyE,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACvE,YAAY,CAACyE,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyCxE,YAAY,CAACyE,aAAa,IAAI;MACjH;;MAEA;MACA,IAAIrE,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAM2B,MAAM,GAAGrC,MAAM,CAAC4E,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAI2B,MAAM,IAAIqC,WAAW,GAAGD,UAAU,CAACpC,MAAM,CAACW,aAAa,CAAC,EAAE;UAC5DuB,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CrC,MAAM,CAACW,aAAa,oCAAoC;QAC/I;MACF;IACF;;IAEA;IACApC,aAAa,CAACiE,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACV,IAAI,GAAGrC;IACV,CAAC,CAAC,CAAC;;IAEH;IACAhB,eAAe,CAAC+D,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACV,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAACzC,KAAK;EACf,CAAC;;EAED;EACA,MAAMgD,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACA,IAAI,CAAC3E,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAACyC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjE8B,MAAM,CAACvE,YAAY,GAAG,uCAAuC;MAC7DsE,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIP,KAAK,CAACC,UAAU,CAACnE,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIgE,UAAU,CAACnE,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FuE,MAAM,CAACvE,YAAY,GAAG,sCAAsC;MAC5DsE,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM;MACL,MAAML,WAAW,GAAGD,UAAU,CAACnE,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIP,YAAY,IAAIA,YAAY,CAACyE,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACvE,YAAY,CAACyE,aAAa,CAAC,EAAE;QACtGM,QAAQ,CAACxE,YAAY,GAAG,mBAAmBiE,WAAW,yCAAyCxE,YAAY,CAACyE,aAAa,IAAI;;QAE7H;QACA,IAAI,CAACjB,MAAM,CAACC,OAAO,CAAC,+BAA+Be,WAAW,yCAAyCxE,YAAY,CAACyE,aAAa,kCAAkC,CAAC,EAAE;UACpKI,OAAO,GAAG,KAAK;QACjB;MACF;;MAEA;MACA,IAAIA,OAAO,IAAIzE,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAM2B,MAAM,GAAGrC,MAAM,CAAC4E,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAI2B,MAAM,IAAIqC,WAAW,GAAGD,UAAU,CAACpC,MAAM,CAACW,aAAa,CAAC,EAAE;UAC5DiC,QAAQ,CAACxE,YAAY,GAAG,mBAAmBiE,WAAW,6CAA6CrC,MAAM,CAACW,aAAa,oCAAoC;;UAE3J;UACA,IAAI,CAACU,MAAM,CAACC,OAAO,CAAC,+BAA+Be,WAAW,6CAA6CrC,MAAM,CAACW,aAAa,oEAAoE,CAAC,EAAE;YACpM+B,OAAO,GAAG,KAAK;UACjB;QACF;MACF;IACF;IAEAnE,aAAa,CAACoE,MAAM,CAAC;IACrBlE,eAAe,CAACmE,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI5F,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACwF,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIxF,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAqC,UAAU,CAAC,CAAC;IACd;IAEApC,aAAa,CAAE4F,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB7F,aAAa,CAAE4F,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB9F,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMwE,2BAA2B,GAAGA,CAACZ,WAAW,EAAEa,YAAY,KAAK;IACjE,OAAOpH,mBAAmB,CAACuG,WAAW,EAAEa,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF/F,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACqF,YAAY,CAAC,CAAC,EAAE;QACnBrF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMiF,WAAW,GAAGD,UAAU,CAACnE,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIgF,QAAQ,GAAGnF,QAAQ,CAACI,SAAS,IAAI,IAAI;MACzC,IAAI+E,QAAQ,KAAK,cAAc,EAAE;QAC/B1D,OAAO,CAACa,GAAG,CAAC,iCAAiC,CAAC;MAChD;;MAEA;MACA,MAAM8C,kBAAkB,GAAGJ,2BAA2B,CAACZ,WAAW,EAAExE,YAAY,CAACyE,aAAa,CAAC;;MAE/F;MACA,IAAIgB,SAAS,GAAG,KAAK;MACrB,IAAIF,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAC3C,MAAMpD,MAAM,GAAGrC,MAAM,CAAC4E,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,SAAS,KAAK+E,QAAQ,CAAC;QACzD,IAAIpD,MAAM,IAAIqC,WAAW,GAAGD,UAAU,CAACpC,MAAM,CAACW,aAAa,CAAC,EAAE;UAC5D2C,SAAS,GAAG,IAAI;UAChB5D,OAAO,CAACa,GAAG,CAAC,qCAAqC6C,QAAQ,iCAAiC,CAAC;QAC7F;MACF;;MAEA;MACA1D,OAAO,CAACa,GAAG,CAAC,aAAa,EAAE;QACzB3D,UAAU;QACV+E,MAAM,EAAE1D,QAAQ,CAACE,OAAO;QACxBkE,WAAW;QACXe,QAAQ;QACRE,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,MAAME,cAAc,GAAG,qCAAqCtF,QAAQ,CAACE,OAAO,QAAQkE,WAAW,WAAW;MAC1G,IAAI,CAAChB,MAAM,CAACC,OAAO,CAACiC,cAAc,CAAC,EAAE;QACnCnG,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAM3B,WAAW,CAAC+H,iBAAiB,CACjC5G,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChBkE,WAAW,EACXe,QAAQ,EACRE,SACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAID,QAAQ,KAAK,cAAc,EAAE;QAC/BK,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAIL,QAAQ,EAAE;QACnB,MAAMpD,MAAM,GAAGrC,MAAM,CAAC4E,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,SAAS,KAAK+E,QAAQ,CAAC;QACzD,IAAIpD,MAAM,EAAE;UACVyD,cAAc,IAAI,gCAAgCL,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAvG,SAAS,CAAC4G,cAAc,CAAC;;MAEzB;MACAT,WAAW,CAAC,CAAC;;MAEb;MACA3D,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE3C,OAAO,CAAC,oDAAoD,IAAI2C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACzG,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsG,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACElH,OAAA,CAAC3D,GAAG;MAAA8K,QAAA,gBACFnH,OAAA,CAACzD,UAAU;QAAC6K,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbzH,OAAA,CAAC1D,KAAK;QAACoL,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBnH,OAAA,CAACzD,UAAU;UAAC6K,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzH,OAAA,CAACnD,IAAI;UAACgL,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7CnH,OAAA,CAACnD,IAAI;YAACmL,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfnH,OAAA,CAACxD,SAAS;cACR0L,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClB7B,KAAK,EAAEhE,WAAY;cACnB6G,QAAQ,EAAG/C,CAAC,IAAK7D,cAAc,CAAC6D,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChD8C,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPzH,OAAA,CAACnD,IAAI;YAACmL,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfnH,OAAA,CAACvD,MAAM;cACLyL,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAEnE,oBAAqB;cAC9BoE,QAAQ,EAAE3H,WAAW,IAAI,CAACU,WAAW,CAAC8C,IAAI,CAAC,CAAE;cAC7CoE,SAAS,EAAE5H,WAAW,gBAAGb,OAAA,CAACxC,gBAAgB;gBAACkL,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGzH,OAAA,CAAC/B,UAAU;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRzH,OAAA,CAAC1D,KAAK;QAACoL,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBnH,OAAA,CAACzD,UAAU;UAAC6K,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ5G,WAAW,gBACVb,OAAA,CAAC3D,GAAG;UAACqL,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DnH,OAAA,CAACxC,gBAAgB;YAAA8J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJxG,IAAI,CAAC6C,MAAM,KAAK,CAAC,gBACnB9D,OAAA,CAACzC,KAAK;UAACuL,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERzH,OAAA,CAAC9C,IAAI;UAACwK,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChDlG,IAAI,CAACgI,GAAG,CAAErE,IAAI,iBACb5E,OAAA,CAAC9D,KAAK,CAAC+D,QAAQ;YAAAkH,QAAA,gBACbnH,OAAA,CAAC7C,QAAQ;cAAC+L,MAAM;cAACX,OAAO,EAAEA,CAAA,KAAM5D,gBAAgB,CAACC,IAAI,CAAE;cAAAuC,QAAA,gBACrDnH,OAAA,CAAC5C,YAAY;gBACX+L,OAAO,eACLnJ,OAAA,CAAC3D,GAAG;kBAACqL,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjDnH,OAAA,CAACzD,UAAU;oBAAC6K,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAEvC,IAAI,CAACjD;kBAAO;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DhI,YAAY,CAACmF,IAAI,CAAC,gBACjB5E,OAAA,CAACrC,IAAI;oBACH+K,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACA/H,gBAAgB,CAACkF,IAAI,CAAC,gBACxB5E,OAAA,CAACrC,IAAI;oBACH+K,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEFzH,OAAA,CAACrC,IAAI;oBACH+K,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAEvD,IAAI,CAACJ,mBAAoB;oBAChC8D,KAAK,EAAE3I,kBAAkB,CAACiF,IAAI,CAACJ,mBAAmB,CAAE;oBACpDkD,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD4B,SAAS,eACPrJ,OAAA,CAAAE,SAAA;kBAAAiH,QAAA,gBACEnH,OAAA,CAACzD,UAAU;oBAAC6K,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GACzCvC,IAAI,CAAClB,SAAS,IAAI,KAAK,EAAC,KAAG,EAACkB,IAAI,CAACjB,YAAY,IAAI,KAAK,EAAC,KAAG,EAACiB,IAAI,CAAChB,OAAO,IAAI,KAAK;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACbzH,OAAA;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNzH,OAAA,CAACzD,UAAU;oBAAC6K,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,MACvC,EAACvC,IAAI,CAAC2E,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAAC3E,IAAI,CAAC4E,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACbzH,OAAA;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNzH,OAAA,CAACzD,UAAU;oBAAC6K,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,iBAC5B,EAACvC,IAAI,CAACkB,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAAClB,IAAI,CAACH,eAAe,IAAI,GAAG;kBAAA;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFzH,OAAA,CAAC3C,uBAAuB;gBAAA8J,QAAA,eACtBnH,OAAA,CAACtC,UAAU;kBAAC+L,IAAI,EAAC,KAAK;kBAAClB,OAAO,EAAGlD,CAAC,IAAK;oBACrCA,CAAC,CAACqE,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrBpI,eAAe,CAACsD,IAAI,CAAC;oBACrBjC,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAwE,QAAA,eACAnH,OAAA,CAACjB,QAAQ;oBAAAuI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACXzH,OAAA,CAAC1C,OAAO;cAAAgK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAxDQ7C,IAAI,CAACjD,OAAO;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACtI,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACErB,OAAA,CAAC3D,GAAG;MAAA8K,QAAA,gBACFnH,OAAA,CAACzD,UAAU;QAAC6K,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbzH,OAAA,CAACb,eAAe;QACdyF,IAAI,EAAEvD,YAAa;QACnBuI,OAAO,EAAE,IAAK;QACdC,KAAK,EAAC;MAA+B;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEFzH,OAAA,CAAC1D,KAAK;QAACoL,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBnH,OAAA,CAACzD,UAAU;UAAC6K,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbzH,OAAA,CAAC3D,GAAG;UAACqL,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEmC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBAC5DnH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEsC,UAAU,EAAE;YAAO,CAAE;YAAA7C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,OAAO;YAAAD,QAAA,gBACzBnH,OAAA;cAAAmH,QAAA,EAAQ;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpG,YAAY,CAACyE,aAAa,IAAI,KAAK,EAAC,IAChF;UAAA;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,OAAO;YAAAD,QAAA,gBACzBnH,OAAA;cAAAmH,QAAA,EAAQ;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpG,YAAY,CAACmD,mBAAmB,IAAI,KAAK;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,EACZhG,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,CAAC,MAAM;YACrE,MAAM2B,MAAM,GAAGrC,MAAM,CAAC4E,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,OAAO2B,MAAM,gBACXxD,OAAA,CAACzD,UAAU;cAAC6K,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBnH,OAAA;gBAAAmH,QAAA,EAAQ;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjE,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IACpE;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,GACX,IAAI;UACV,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENzH,OAAA,CAACxD,SAAS;UACR0L,SAAS;UACTC,KAAK,EAAC,cAAc;UACpBf,OAAO,EAAC,UAAU;UAClB9B,IAAI,EAAC,cAAc;UACnB2E,IAAI,EAAC,QAAQ;UACb1E,KAAK,EAAE9D,QAAQ,CAACG,YAAa;UAC7BwG,QAAQ,EAAEhD,gBAAiB;UAC3BnC,KAAK,EAAE,CAAC,CAACnB,UAAU,CAACF,YAAa;UACjCsI,UAAU,EAAEpI,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;UACjEuI,mBAAmB,EAAE;YACnBzC,EAAE,EAAE;cAAEY,KAAK,EAAEtG,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;YAAa;UACrG,CAAE;UACF8F,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EAEDzF,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpD5B,OAAA,CAACzC,KAAK;UAACuL,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACrCnF,YAAY,CAACJ;QAAY;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAEDzH,OAAA,CAACzC,KAAK;UAACuL,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM4C,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,eAAe,GAAI1D,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC2D,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAO3D,QAAQ,CAAC4D,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAO5D,QAAQ;IACjB,CAAC;;IAED;IACA,MAAM6D,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAItK,UAAU,KAAKsK,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAInH,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAAC/B,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOgE,UAAU,CAACpC,MAAM,CAACW,aAAa,CAAC,IAAIyB,UAAU,CAACnE,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAMgJ,uBAAuB,GAAIvF,CAAC,IAAK;MACrC,MAAMqF,YAAY,GAAGrF,CAAC,CAACG,MAAM,CAACD,KAAK,CAAClB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIqG,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC;QACAnJ,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbgJ,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC;QACAnJ,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbgJ,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,EAAE;QAChB;QACA,MAAMK,gBAAgB,GAAGN,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMM,eAAe,GAAG7J,MAAM,CAAC4E,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,SAAS,KAAKkJ,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAACvH,YAAY,KAAK,MAAM,IAAIuH,eAAe,CAACvH,YAAY,KAAK,WAAW,EAAE;YAC3F1B,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbgJ,eAAe,EAAE,aAAaJ,YAAY,eAAeM,eAAe,CAACvH,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAIpC,YAAY,KACZ2J,eAAe,CAACtH,SAAS,KAAKrC,YAAY,CAACqC,SAAS,IACpDuH,MAAM,CAACD,eAAe,CAACrH,YAAY,CAAC,KAAKsH,MAAM,CAAC5J,YAAY,CAACsC,YAAY,CAAC,IAC1EsH,MAAM,CAACD,eAAe,CAACpH,OAAO,CAAC,KAAKqH,MAAM,CAAC5J,YAAY,CAACuC,OAAO,CAAC,CACnE,EAAE;YACD;YACAvB,mBAAmB,CAAC2I,eAAe,CAAC;YACpC7I,6BAA6B,CAAC,IAAI,CAAC;YACnC;UACF;;UAEA;UACA,IAAIwI,mBAAmB,CAACK,eAAe,CAAC,EAAE;YACxC;YACAtJ,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAEkJ;YACb,CAAC,CAAC;YACFhJ,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbgJ,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA/I,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbgJ,eAAe,EAAE,aAAaJ,YAAY,sCAAsCM,eAAe,CAAC7G,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACApC,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbgJ,eAAe,EAAE,UAAUJ,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAhJ,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbgJ,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE9K,OAAA,CAAC3D,GAAG;MAAA8K,QAAA,gBACFnH,OAAA,CAACzD,UAAU;QAAC6K,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbzH,OAAA,CAAC1D,KAAK;QAACoL,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBnH,OAAA,CAACzD,UAAU;UAAC6K,OAAO,EAAC,OAAO;UAAC8D,SAAS;UAAA/D,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ1G,aAAa,gBACZf,OAAA,CAAC3D,GAAG;UAACqL,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DnH,OAAA,CAACxC,gBAAgB;YAAA8J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENzH,OAAA,CAAC3D,GAAG;UAAA8K,QAAA,gBAEFnH,OAAA,CAACnD,IAAI;YAACgL,SAAS;YAACC,OAAO,EAAE,CAAE;YAACJ,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxCnH,OAAA,CAACnD,IAAI;cAACmL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkD,EAAE,EAAE,CAAE;cAAAhE,QAAA,eACvBnH,OAAA,CAACxD,SAAS;gBACR0L,SAAS;gBACTC,KAAK,EAAC,yBAAyB;gBAC/Bf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,qDAAqD;gBACjE6B,UAAU,EAAEpI,UAAU,CAACgJ,eAAe,IAAI,4GAA6G;gBACvJ7H,KAAK,EAAE,CAAC,CAACnB,UAAU,CAACgJ,eAAgB;gBACpCM,MAAM,EAAER;cAAwB;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPzH,OAAA,CAACnD,IAAI;cAACmL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkD,EAAE,EAAE,CAAE;cAAAhE,QAAA,eACvBnH,OAAA,CAACzD,UAAU;gBAAC6K,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAE0C,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,GAAC,sBACrB,EAAC1F,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGJ,QAAQ,CAACI,SAAS,IAAI,GAAG;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPzH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAE0C,EAAE,EAAE,CAAC;cAAEJ,UAAU,EAAE;YAAO,CAAE;YAAA7C,QAAA,EAAC;UAEhF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZtG,MAAM,CAAC2C,MAAM,GAAG,CAAC,gBAChB9D,OAAA,CAAC3D,GAAG;YAACqL,EAAE,EAAE;cAAE2D,SAAS,EAAE;YAAO,CAAE;YAAAlE,QAAA,eAC7BnH,OAAA;cAAOsL,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAtE,QAAA,gBAChFnH,OAAA;gBAAAmH,QAAA,eACEnH,OAAA;kBAAIsL,KAAK,EAAE;oBAAEI,YAAY,EAAE,gBAAgB;oBAAEC,eAAe,EAAE;kBAAU,CAAE;kBAAAxE,QAAA,gBACxEnH,OAAA;oBAAIsL,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAA1E,QAAA,EAAC;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzDzH,OAAA;oBAAIsL,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAA1E,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChEzH,OAAA;oBAAIsL,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAA1E,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DzH,OAAA;oBAAIsL,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAA1E,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DzH,OAAA;oBAAIsL,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAQ,CAAE;oBAAA1E,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/DzH,OAAA;oBAAIsL,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAA1E,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRzH,OAAA;gBAAAmH,QAAA,EACGhG,MAAM,CAAC8H,GAAG,CAAEzF,MAAM,IAAK;kBACtB,MAAMsI,YAAY,GAAGzK,YAAY,IAC/BmC,MAAM,CAACE,SAAS,KAAKrC,YAAY,CAACqC,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAKtC,YAAY,CAACsC,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAKvC,YAAY,CAACuC,OAAO;kBAEzC,MAAMmI,aAAa,GAAGvI,MAAM,CAACW,aAAa,IAAIyB,UAAU,CAACnE,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC;kBAEpF,oBACE5B,OAAA;oBAEEsL,KAAK,EAAE;sBACLI,YAAY,EAAE,gBAAgB;sBAC9BC,eAAe,EAAEG,YAAY,GAAIC,aAAa,GAAG,SAAS,GAAG,SAAS,GAAI,aAAa;sBACvFC,MAAM,EAAE;oBACV,CAAE;oBACFzD,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIwD,aAAa,EAAE;wBACjBrK,WAAW,CAAC;0BACV,GAAGD,QAAQ;0BACXI,SAAS,EAAE2B,MAAM,CAAC3B;wBACpB,CAAC,CAAC;sBACJ;oBACF,CAAE;oBAAAsF,QAAA,gBAEFnH,OAAA;sBAAIsL,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAzE,QAAA,EAAEmD,eAAe,CAAC9G,MAAM,CAAC3B,SAAS;oBAAC;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvEzH,OAAA;sBAAIsL,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAzE,QAAA,EAAE3D,MAAM,CAACE,SAAS,IAAI;oBAAK;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/DzH,OAAA;sBAAIsL,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAzE,QAAA,EAAE3D,MAAM,CAACG,YAAY,IAAI;oBAAK;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClEzH,OAAA;sBAAIsL,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAzE,QAAA,EAAE3D,MAAM,CAACI,OAAO,IAAI;oBAAK;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7DzH,OAAA;sBAAIsL,KAAK,EAAE;wBAAEM,OAAO,EAAE,KAAK;wBAAEC,SAAS,EAAE;sBAAQ,CAAE;sBAAA1E,QAAA,GAAE3D,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrFzH,OAAA;sBAAIsL,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAzE,QAAA,eAC5BnH,OAAA,CAACrC,IAAI;wBACH+K,IAAI,EAAC,OAAO;wBACZP,KAAK,EAAE3E,MAAM,CAACC,YAAa;wBAC3B6E,KAAK,EACH9E,MAAM,CAACC,YAAY,KAAK,aAAa,GAAG,SAAS,GACjDD,MAAM,CAACC,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5CD,MAAM,CAACC,YAAY,KAAK,MAAM,GAAG,OAAO,GACxCD,MAAM,CAACC,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;sBACnD;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA,GA/BAjE,MAAM,CAAC3B,SAAS;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgCnB,CAAC;gBAET,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,gBAENzH,OAAA,CAACzC,KAAK;YAACuL,QAAQ,EAAC,MAAM;YAACpB,EAAE,EAAE;cAAE0C,EAAE,EAAE,CAAC;cAAExC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,eAGDzH,OAAA,CAAC3D,GAAG;YAACqL,EAAE,EAAE;cAAE0C,EAAE,EAAE,CAAC;cAAExC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxBnH,OAAA,CAACzD,UAAU;cAAC6K,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzH,OAAA,CAACnD,IAAI;cAACgL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAX,QAAA,gBACzBnH,OAAA,CAACnD,IAAI;gBAACmL,IAAI;gBAAAb,QAAA,eACRnH,OAAA,CAACvD,MAAM;kBACL2K,OAAO,EAAC,UAAU;kBAClBmB,OAAO,EAAEA,CAAA,KAAM;oBACb7G,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXI,SAAS,EAAE;oBACb,CAAC,CAAC;kBACJ,CAAE;kBAAAsF,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACPzH,OAAA,CAACnD,IAAI;gBAACmL,IAAI;gBAAAb,QAAA,eACRnH,OAAA,CAACvD,MAAM;kBACL2K,OAAO,EAAC,UAAU;kBAClBkB,KAAK,EAAC,WAAW;kBACjBC,OAAO,EAAEA,CAAA,KAAM;oBACb7G,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXI,SAAS,EAAE;oBACb,CAAC,CAAC;kBACJ,CAAE;kBAAAsF,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNzH,OAAA,CAAClD,WAAW;YAACoL,SAAS;YAAAf,QAAA,gBACpBnH,OAAA,CAACjD,UAAU;cAACkP,EAAE,EAAC,qBAAqB;cAAA9E,QAAA,EAAC;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9EzH,OAAA,CAAChD,MAAM;cACLkP,OAAO,EAAC,qBAAqB;cAC7BD,EAAE,EAAC,eAAe;cAClB3G,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAE9D,QAAQ,CAACI,SAAU;cAC1BsG,KAAK,EAAC,8BAA8B;cACpCC,QAAQ,EAAEhD,gBAAiB;cAAA+B,QAAA,gBAE3BnH,OAAA,CAAC/C,QAAQ;gBAACsI,KAAK,EAAC,EAAE;gBAAA4B,QAAA,eAChBnH,OAAA;kBAAAmH,QAAA,EAAI;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACXzH,OAAA,CAAC/C,QAAQ;gBAACsI,KAAK,EAAC,cAAc;gBAAA4B,QAAA,eAC5BnH,OAAA;kBAAAmH,QAAA,EAAI;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,EACVtG,MAAM,CAAC8H,GAAG,CAAEzF,MAAM,iBACjBxD,OAAA,CAAC/C,QAAQ;gBAEPsI,KAAK,EAAE/B,MAAM,CAAC3B,SAAU;gBACxB2G,QAAQ,EAAEhF,MAAM,CAACW,aAAa,GAAGyB,UAAU,CAACnE,QAAQ,CAACG,YAAY,CAAE;gBAAAuF,QAAA,GAElEmD,eAAe,CAAC9G,MAAM,CAAC3B,SAAS,CAAC,EAAC,KAAG,EAAC2B,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,cAAY,EAACF,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAC1G;cAAA,GALOX,MAAM,CAAC3B,SAAS;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTzH,OAAA,CAACvC,cAAc;cAAA0J,QAAA,EAAC;YAEhB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGbhG,QAAQ,CAACI,SAAS,iBACjB7B,OAAA,CAAC3D,GAAG;YAACqL,EAAE,EAAE;cAAE0C,EAAE,EAAE,CAAC;cAAEzC,CAAC,EAAE,CAAC;cAAEmC,OAAO,EAAE,kBAAkB;cAAEC,YAAY,EAAE,CAAC;cAAEoC,MAAM,EAAE;YAAoB,CAAE;YAAAhF,QAAA,gBAClGnH,OAAA,CAACzD,UAAU;cAAC6K,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ,CAAC,MAAM;cACN,MAAMjE,MAAM,GAAGrC,MAAM,CAAC4E,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;cACnE,IAAI2B,MAAM,EAAE;gBACV,oBACExD,OAAA,CAACnD,IAAI;kBAACgL,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzBnH,OAAA,CAACnD,IAAI;oBAACmL,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACkD,EAAE,EAAE,CAAE;oBAAAhE,QAAA,gBACvBnH,OAAA,CAACzD,UAAU;sBAAC6K,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBnH,OAAA;wBAAAmH,QAAA,EAAQ;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC6C,eAAe,CAAC9G,MAAM,CAAC3B,SAAS,CAAC;oBAAA;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACbzH,OAAA,CAACzD,UAAU;sBAAC6K,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBnH,OAAA;wBAAAmH,QAAA,EAAQ;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACjE,MAAM,CAACE,SAAS,IAAI,KAAK;oBAAA;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACbzH,OAAA,CAACzD,UAAU;sBAAC6K,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBnH,OAAA;wBAAAmH,QAAA,EAAQ;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACjE,MAAM,CAACG,YAAY,IAAI,KAAK,EAAC,KAAG,EAACH,MAAM,CAACI,OAAO,IAAI,KAAK;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACPzH,OAAA,CAACnD,IAAI;oBAACmL,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACkD,EAAE,EAAE,CAAE;oBAAAhE,QAAA,gBACvBnH,OAAA,CAACzD,UAAU;sBAAC6K,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBnH,OAAA;wBAAAmH,QAAA,EAAQ;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACjE,MAAM,CAAC4I,YAAY,IAAI,CAAC,EAAC,IAC3D;oBAAA;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzH,OAAA,CAACzD,UAAU;sBAAC6K,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBnH,OAAA;wBAAAmH,QAAA,EAAQ;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACjE,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAC7D;oBAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzH,OAAA,CAACzD,UAAU;sBAAC6K,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBnH,OAAA;wBAAAmH,QAAA,EAAQ;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACjE,MAAM,CAACC,YAAY,IAAI,KAAK;oBAAA;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEX;cACA,oBACEzH,OAAA,CAACzD,UAAU;gBAAC6K,OAAO,EAAC,OAAO;gBAACkB,KAAK,EAAC,OAAO;gBAAAnB,QAAA,EAAC;cAE1C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAEjB,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEAtG,MAAM,CAAC2C,MAAM,KAAK,CAAC,IAAI,CAAC/C,aAAa,iBACpCf,OAAA,CAACzC,KAAK;UAACuL,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM4E,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAM/B,eAAe,GAAI1D,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC2D,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAO3D,QAAQ,CAAC4D,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAO5D,QAAQ;IACjB,CAAC;;IAED;IACA,IAAI8D,YAAY,GAAG,SAAS;IAC5B,IAAI4B,UAAU,GAAG,IAAI;IAErB,IAAI7K,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC6I,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAIjJ,QAAQ,CAACI,SAAS,EAAE;MAC7B6I,YAAY,GAAGJ,eAAe,CAAC7I,QAAQ,CAACI,SAAS,CAAC;MAClD;MACAyK,UAAU,GAAGnL,MAAM,CAAC4E,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAMgF,kBAAkB,GAAGJ,2BAA2B,CAACb,UAAU,CAACnE,QAAQ,CAACG,YAAY,CAAC,EAAEP,YAAY,CAACyE,aAAa,CAAC;IAErH,oBACE9F,OAAA,CAAC3D,GAAG;MAAA8K,QAAA,gBACFnH,OAAA,CAACzD,UAAU;QAAC6K,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbzH,OAAA,CAAC1D,KAAK;QAACoL,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBnH,OAAA,CAACzD,UAAU;UAAC6K,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbzH,OAAA,CAACb,eAAe;UACdyF,IAAI,EAAEvD,YAAa;UACnBuI,OAAO,EAAE,IAAK;UACdC,KAAK,EAAC;QAAmB;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGFzH,OAAA,CAAC3D,GAAG;UAACqL,EAAE,EAAE;YAAE0C,EAAE,EAAE,CAAC;YAAEzC,CAAC,EAAE,CAAC;YAAEmC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBAC5DnH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEsC,UAAU,EAAE;YAAO,CAAE;YAAA7C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzH,OAAA,CAACnD,IAAI;YAACgL,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBnH,OAAA,CAACnD,IAAI;cAACmL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkD,EAAE,EAAE,CAAE;cAAAhE,QAAA,gBACvBnH,OAAA,CAACzD,UAAU;gBAAC6K,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBnH,OAAA;kBAAAmH,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChG,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzH,OAAA,CAACzD,UAAU;gBAAC6K,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBnH,OAAA;kBAAAmH,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACZ,kBAAkB;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPzH,OAAA,CAACnD,IAAI;cAACmL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkD,EAAE,EAAE,CAAE;cAAAhE,QAAA,gBACvBnH,OAAA,CAACzD,UAAU;gBAAC6K,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBnH,OAAA;kBAAAmH,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACiD,YAAY;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZ6E,UAAU,iBACTtM,OAAA,CAACzD,UAAU;gBAAC6K,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBnH,OAAA;kBAAAmH,QAAA,EAAQ;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC6E,UAAU,CAACnI,aAAa,EAAC,IACnE;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAEL6E,UAAU,IAAI1G,UAAU,CAACnE,QAAQ,CAACG,YAAY,CAAC,GAAGgE,UAAU,CAAC0G,UAAU,CAACnI,aAAa,CAAC,iBACrFnE,OAAA,CAACzC,KAAK;UAACuL,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,gBACtCnH,OAAA;YAAAmH,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAAChG,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC0K,UAAU,CAACnI,aAAa,EAAC,gDAE1I;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAEDzH,OAAA,CAACzC,KAAK;UAACuL,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,GAAC,8EAEpC,EAAC1F,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8E,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOtF,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOmD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOV,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO0C,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzClK,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMiK,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIlK,eAAe,EAAE;MACnBhC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIoC,eAAe,CAACb,OAAO,EAAE,CAAC;IACtF;IACA8K,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACA/L,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;;EAED;EACA,MAAMoL,iCAAiC,GAAGA,CAAA,KAAM;IAC9CzK,6BAA6B,CAAC,KAAK,CAAC;IACpCE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMwK,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI,CAACxL,YAAY,IAAI,CAACe,gBAAgB,EAAE;IAExC,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM3B,WAAW,CAAC6N,qBAAqB,CAAC1M,UAAU,EAAEiB,YAAY,CAACM,OAAO,EAAES,gBAAgB,CAAC;;MAE3F;MACA,MAAM6C,WAAW,GAAG,MAAMhG,WAAW,CAACsF,WAAW,CAACnE,UAAU,EAAEiB,YAAY,CAACM,OAAO,CAAC;MACnFL,eAAe,CAAC2D,WAAW,CAAC;;MAE5B;MACAvD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEO,gBAAgB,CAACP;MAC9B,CAAC,CAAC;MAEFxB,SAAS,CAAC,4BAA4BgB,YAAY,CAACM,OAAO,6CAA6CS,gBAAgB,CAACP,SAAS,EAAE,CAAC;MACpI+K,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAO3J,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvF3C,OAAO,CAAC,kEAAkE,IAAI2C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvH,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmM,uBAAuB,GAAGA,CAAA,KAAM;IACpCH,iCAAiC,CAAC,CAAC;IACnC;IACAlL,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACE7B,OAAA,CAAC3D,GAAG;IAAA8K,QAAA,gBACFnH,OAAA,CAACtD,OAAO;MAAC+D,UAAU,EAAEA,UAAW;MAACiH,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC5CvE,KAAK,CAACqG,GAAG,CAAEd,KAAK,iBACfnI,OAAA,CAACrD,IAAI;QAAAwK,QAAA,eACHnH,OAAA,CAACpD,SAAS;UAAAuK,QAAA,EAAEgB;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC,GADrBU,KAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEVzH,OAAA,CAAC3D,GAAG;MAACqL,EAAE,EAAE;QAAE0C,EAAE,EAAE,CAAC;QAAExC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EACvBoF,cAAc,CAAC9L,UAAU;IAAC;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAENzH,OAAA,CAAC3D,GAAG;MAACqL,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEwB,EAAE,EAAE;MAAE,CAAE;MAAAjD,QAAA,gBACnEnH,OAAA,CAACvD,MAAM;QACL2K,OAAO,EAAC,UAAU;QAClBkB,KAAK,EAAC,WAAW;QACjBC,OAAO,EAAE9H,UAAU,KAAK,CAAC,GAAG,MAAMD,QAAQ,CAAC,sBAAsB,CAAC,GAAG+F,UAAW;QAChFkC,SAAS,eAAEzI,OAAA,CAAC3B,aAAa;UAAAiJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Be,QAAQ,EAAE7H,OAAQ;QAAAwG,QAAA,EAEjB1G,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG;MAAU;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAETzH,OAAA,CAACvD,MAAM;QACL2K,OAAO,EAAC,WAAW;QACnBkB,KAAK,EAAC,SAAS;QACfC,OAAO,EAAE9H,UAAU,KAAKmC,KAAK,CAACkB,MAAM,GAAG,CAAC,GAAG6C,YAAY,GAAGN,UAAW;QACrE2G,OAAO,EAAEvM,UAAU,KAAKmC,KAAK,CAACkB,MAAM,GAAG,CAAC,gBAAG9D,OAAA,CAAC7B,QAAQ;UAAAmJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGzH,OAAA,CAACzB,gBAAgB;UAAA+I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/Ee,QAAQ,EAAE7H,OAAO,IAAKF,UAAU,KAAK,CAAC,IAAI,CAACY,YAAc;QAAA8F,QAAA,EAExDxG,OAAO,gBACNX,OAAA,CAACxC,gBAAgB;UAACkL,IAAI,EAAE;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC5BhH,UAAU,KAAKmC,KAAK,CAACkB,MAAM,GAAG,CAAC,GACjC,OAAO,GAEP;MACD;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNzH,OAAA,CAACpC,MAAM;MAACqP,IAAI,EAAE3K,qBAAsB;MAAC4K,OAAO,EAAET,4BAA6B;MAACU,QAAQ,EAAC,IAAI;MAACjF,SAAS;MAAAf,QAAA,gBACjGnH,OAAA,CAACnC,WAAW;QAAC6J,EAAE,EAAE;UAAEoC,OAAO,EAAE;QAAgB,CAAE;QAAA3C,QAAA,eAC5CnH,OAAA,CAAC3D,GAAG;UAACqL,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEqF,GAAG,EAAE;UAAE,CAAE;UAAAjG,QAAA,gBACzDnH,OAAA,CAACnB,WAAW;YAACyJ,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BzH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdzH,OAAA,CAAClC,aAAa;QAAAqJ,QAAA,EACX3E,eAAe,iBACdxC,OAAA,CAAC3D,GAAG;UAACqL,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,gBACjBnH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,OAAO;YAAC8D,SAAS;YAAA/D,QAAA,GAAC,UAC5B,eAAAnH,OAAA;cAAAmH,QAAA,EAAS3E,eAAe,CAACb;YAAO;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAACjF,eAAe,CAACiC,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,OAAO;YAAC8D,SAAS;YAAA/D,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,OAAO;YAACkC,SAAS,EAAC,IAAI;YAAAnC,QAAA,gBACxCnH,OAAA;cAAAmH,QAAA,EAAI;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CzH,OAAA;cAAAmH,QAAA,EAAI;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCzH,OAAA;cAAAmH,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBzH,OAAA,CAACjC,aAAa;QAAC2J,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBAC3DnH,OAAA,CAACvD,MAAM;UAAC8L,OAAO,EAAEkE,4BAA6B;UAACnE,KAAK,EAAC,WAAW;UAAAnB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzH,OAAA,CAAC3D,GAAG;UAAA8K,QAAA,gBACFnH,OAAA,CAACvD,MAAM;YAAC8L,OAAO,EAAEoE,wBAAyB;YAACrE,KAAK,EAAC,SAAS;YAACZ,EAAE,EAAE;cAAE2F,EAAE,EAAE;YAAE,CAAE;YAAAlG,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzH,OAAA,CAACvD,MAAM;YAAC8L,OAAO,EAAEmE,gBAAiB;YAACtF,OAAO,EAAC,WAAW;YAACkB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzH,OAAA,CAACd,sBAAsB;MACrB+N,IAAI,EAAE/K,0BAA2B;MACjCgL,OAAO,EAAEN,iCAAkC;MAC3ChI,IAAI,EAAEvD,YAAa;MACnBmC,MAAM,EAAEpB,gBAAiB;MACzBkL,YAAY,EAAET,2BAA4B;MAC1CU,mBAAmB,EAAER;IAAwB;MAAAzF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGFzH,OAAA,CAACpC,MAAM;MACLqP,IAAI,EAAEvK,qBAAsB;MAC5BwK,OAAO,EAAEA,CAAA,KAAMvK,wBAAwB,CAAC,KAAK,CAAE;MAC/CwK,QAAQ,EAAC,IAAI;MACbjF,SAAS;MAAAf,QAAA,gBAETnH,OAAA,CAACnC,WAAW;QAAAsJ,QAAA,eACVnH,OAAA,CAAC3D,GAAG;UAACqL,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEqF,GAAG,EAAE;UAAE,CAAE;UAAAjG,QAAA,gBACzDnH,OAAA,CAACjB,QAAQ;YAACuJ,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BzH,OAAA,CAACzD,UAAU;YAAC6K,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdzH,OAAA,CAAClC,aAAa;QAAAqJ,QAAA,eACZnH,OAAA,CAACb,eAAe;UAACyF,IAAI,EAAEvD;QAAa;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChBzH,OAAA,CAACjC,aAAa;QAAAoJ,QAAA,eACZnH,OAAA,CAACvD,MAAM;UAAC8L,OAAO,EAAEA,CAAA,KAAM5F,wBAAwB,CAAC,KAAK,CAAE;UAAC2F,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAAC;QAExE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAClH,EAAA,CA3vCIJ,kBAAkB;EAAA,QACLnB,WAAW;AAAA;AAAAwO,EAAA,GADxBrN,kBAAkB;AA6vCxB,eAAeA,kBAAkB;AAAC,IAAAqN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}