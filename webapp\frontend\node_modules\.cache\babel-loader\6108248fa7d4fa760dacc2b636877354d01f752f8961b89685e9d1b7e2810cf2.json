{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst certificazioneService = {\n  // Ottiene la lista delle certificazioni di un cantiere\n  getCertificazioni: async (cantiereId, filtroCavo = '') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      let url = `/certificazioni/${cantiereIdNum}`;\n      if (filtroCavo) {\n        url += `?cavo=${filtroCavo}`;\n      }\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazioni error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova certificazione\n  createCertificazione: async (cantiereId, certificazioneData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/certificazioni/${cantiereIdNum}`, certificazioneData);\n      return response.data;\n    } catch (error) {\n      console.error('Create certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di una certificazione\n  getCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/certificazioni/${cantiereIdNum}/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una certificazione\n  deleteCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/certificazioni/${cantiereIdNum}/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Genera PDF di una certificazione\n  generatePdf: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/certificazioni/${cantiereIdNum}/${idCertificazione}/pdf`);\n      return response.data;\n    } catch (error) {\n      console.error('Generate PDF error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la lista degli strumenti certificati\n  getStrumenti: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/strumenti/${cantiereIdNum}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get strumenti error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo strumento certificato\n  createStrumento: async (cantiereId, strumentoData) => {\n    try {\n      const response = await axiosInstance.post(`/strumenti/${cantiereId}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna uno strumento certificato\n  updateStrumento: async (cantiereId, idStrumento, strumentoData) => {\n    try {\n      const response = await axiosInstance.put(`/strumenti/${cantiereId}/${idStrumento}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina uno strumento certificato\n  deleteStrumento: async (cantiereId, idStrumento) => {\n    try {\n      const response = await axiosInstance.delete(`/strumenti/${cantiereId}/${idStrumento}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default certificazioneService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "certificazioneService", "getCertificazioni", "cantiereId", "filtroCavo", "cantiereIdNum", "parseInt", "isNaN", "Error", "url", "response", "get", "data", "console", "createCertificazione", "certificazioneData", "post", "getCertificazione", "idCertificazione", "deleteCertificazione", "delete", "generatePdf", "getStrumenti", "createStrumento", "strumentoData", "updateStrumento", "idStrumento", "put", "deleteStrumento"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/certificazioneService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst certificazioneService = {\n  // Ottiene la lista delle certificazioni di un cantiere\n  getCertificazioni: async (cantiereId, filtroCavo = '') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      let url = `/certificazioni/${cantiereIdNum}`;\n      if (filtroCavo) {\n        url += `?cavo=${filtroCavo}`;\n      }\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazioni error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea una nuova certificazione\n  createCertificazione: async (cantiereId, certificazioneData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/certificazioni/${cantiereIdNum}`, certificazioneData);\n      return response.data;\n    } catch (error) {\n      console.error('Create certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene i dettagli di una certificazione\n  getCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/certificazioni/${cantiereIdNum}/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina una certificazione\n  deleteCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/certificazioni/${cantiereIdNum}/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Genera PDF di una certificazione\n  generatePdf: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/certificazioni/${cantiereIdNum}/${idCertificazione}/pdf`);\n      return response.data;\n    } catch (error) {\n      console.error('Generate PDF error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la lista degli strumenti certificati\n  getStrumenti: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/strumenti/${cantiereIdNum}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get strumenti error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea un nuovo strumento certificato\n  createStrumento: async (cantiereId, strumentoData) => {\n    try {\n      const response = await axiosInstance.post(`/strumenti/${cantiereId}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna uno strumento certificato\n  updateStrumento: async (cantiereId, idStrumento, strumentoData) => {\n    try {\n      const response = await axiosInstance.put(`/strumenti/${cantiereId}/${idStrumento}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina uno strumento certificato\n  deleteStrumento: async (cantiereId, idStrumento) => {\n    try {\n      const response = await axiosInstance.delete(`/strumenti/${cantiereId}/${idStrumento}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default certificazioneService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,qBAAqB,GAAG;EAC5B;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,UAAU,EAAEC,UAAU,GAAG,EAAE,KAAK;IACxD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,IAAIM,GAAG,GAAG,mBAAmBJ,aAAa,EAAE;MAC5C,IAAID,UAAU,EAAE;QACdK,GAAG,IAAI,SAASL,UAAU,EAAE;MAC9B;MACA,MAAMM,QAAQ,GAAG,MAAMxB,aAAa,CAACyB,GAAG,CAACF,GAAG,CAAC;MAC7C,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF,CAAC;EAED;EACAgB,oBAAoB,EAAE,MAAAA,CAAOX,UAAU,EAAEY,kBAAkB,KAAK;IAC9D,IAAI;MACF;MACA,MAAMV,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMxB,aAAa,CAAC8B,IAAI,CAAC,mBAAmBX,aAAa,EAAE,EAAEU,kBAAkB,CAAC;MACjG,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF,CAAC;EAED;EACAmB,iBAAiB,EAAE,MAAAA,CAAOd,UAAU,EAAEe,gBAAgB,KAAK;IACzD,IAAI;MACF;MACA,MAAMb,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMxB,aAAa,CAACyB,GAAG,CAAC,mBAAmBN,aAAa,IAAIa,gBAAgB,EAAE,CAAC;MAChG,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF,CAAC;EAED;EACAqB,oBAAoB,EAAE,MAAAA,CAAOhB,UAAU,EAAEe,gBAAgB,KAAK;IAC5D,IAAI;MACF;MACA,MAAMb,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMxB,aAAa,CAACkC,MAAM,CAAC,mBAAmBf,aAAa,IAAIa,gBAAgB,EAAE,CAAC;MACnG,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF,CAAC;EAED;EACAuB,WAAW,EAAE,MAAAA,CAAOlB,UAAU,EAAEe,gBAAgB,KAAK;IACnD,IAAI;MACF;MACA,MAAMb,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMxB,aAAa,CAACyB,GAAG,CAAC,mBAAmBN,aAAa,IAAIa,gBAAgB,MAAM,CAAC;MACpG,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF,CAAC;EAED;EACAwB,YAAY,EAAE,MAAOnB,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMxB,aAAa,CAACyB,GAAG,CAAC,cAAcN,aAAa,EAAE,CAAC;MACvE,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF,CAAC;EAED;EACAyB,eAAe,EAAE,MAAAA,CAAOpB,UAAU,EAAEqB,aAAa,KAAK;IACpD,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMxB,aAAa,CAAC8B,IAAI,CAAC,cAAcb,UAAU,EAAE,EAAEqB,aAAa,CAAC;MACpF,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF,CAAC;EAED;EACA2B,eAAe,EAAE,MAAAA,CAAOtB,UAAU,EAAEuB,WAAW,EAAEF,aAAa,KAAK;IACjE,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMxB,aAAa,CAACyC,GAAG,CAAC,cAAcxB,UAAU,IAAIuB,WAAW,EAAE,EAAEF,aAAa,CAAC;MAClG,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF,CAAC;EAED;EACA8B,eAAe,EAAE,MAAAA,CAAOzB,UAAU,EAAEuB,WAAW,KAAK;IAClD,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMxB,aAAa,CAACkC,MAAM,CAAC,cAAcjB,UAAU,IAAIuB,WAAW,EAAE,CAAC;MACtF,OAAOhB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACY,QAAQ,GAAGZ,KAAK,CAACY,QAAQ,CAACE,IAAI,GAAGd,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}