{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\MetriPosatiSemplificatoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, TextField, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, CircularProgress, Alert, Chip, Divider, Grid, FormControl, InputLabel, Select, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, DialogContentText } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MetriPosatiSemplificatoForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      setBobine(bobineData);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    setSelectedCavo(cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = {\n      ...formErrors\n    };\n    const newWarnings = {\n      ...formWarnings\n    };\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Validazione completa\n    if (!validateForm()) {\n      return;\n    }\n\n    // Verifica compatibilità\n    if (!checkCompatibility()) {\n      // Mostra dialog per incompatibilità\n      const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n      setIncompatibleReelData({\n        cavo: selectedCavo,\n        bobina: selectedBobina\n      });\n      setShowIncompatibleReelDialog(true);\n      return;\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n      const {\n        cavo,\n        bobina\n      } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, {\n        id_bobina: bobina.id_bobina,\n        tipologia: bobina.tipologia,\n        sezione: bobina.sezione\n      });\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati), formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Filtra le bobine compatibili con il cavo selezionato\n  const getCompatibleBobine = () => {\n    if (!selectedCavo) return [];\n    return bobine.filter(bobina => bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione) && determineReelState(bobina) !== REEL_STATES.TERMINATA);\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this);\n    }\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          my: 2\n        },\n        children: \"Nessun cavo disponibile per questo cantiere.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#f5f5f5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"ID Cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ubicazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Metri Teorici\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.tipologia || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 67\n              }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [cavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: cavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(cavo.stato_installazione),\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: () => handleCavoSelect(cavo),\n                disabled: isCableInstalled(cavo),\n                children: \"Seleziona\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n    const compatibleBobine = getCompatibleBobine();\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          color: \"primary\",\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Formazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Metri posati\",\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleInputChange,\n            type: \"number\",\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'orange'\n              },\n              children: formWarnings.metri_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this),\n            disabled: saving,\n            InputProps: {\n              inputProps: {\n                min: 0,\n                step: 0.1\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            error: !!formErrors.id_bobina,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              onChange: handleInputChange,\n              disabled: saving || bobineLoading,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                sx: {\n                  fontWeight: 'bold',\n                  color: '#2e7d32',\n                  bgcolor: '#f1f8e9'\n                },\n                children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this), compatibleBobine.length === 0 ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: [\"Bobine compatibili (\", compatibleBobine.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this), compatibleBobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.metri_residui, \"m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this)), bobine.filter(bobina => !compatibleBobine.includes(bobina) && determineReelState(bobina) !== REEL_STATES.TERMINATA).length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  disabled: true,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    children: \"Bobine non compatibili\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 21\n                }, this), bobine.filter(bobina => !compatibleBobine.includes(bobina) && determineReelState(bobina) !== REEL_STATES.TERMINATA).map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: bobina.id_bobina,\n                  children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.metri_residui, \"m\"]\n                }, bobina.id_bobina, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              children: formErrors.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this), formWarnings.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'orange'\n              },\n              children: formWarnings.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              sx: {\n                mt: 1\n              },\n              children: \"Seleziona una bobina o usa BOBINA VUOTA se il cavo \\xE8 stato posato senza una bobina specifica.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          disabled: saving,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSave,\n          disabled: saving || Object.keys(formErrors).length > 0,\n          children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 23\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [!selectedCavo && renderCaviTable(), renderForm(), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: () => setShowIncompatibleReelDialog(false),\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoForCompatibility,\n      onSelectAnotherReel: () => {\n        setShowIncompatibleReelDialog(false);\n        setFormData(prev => ({\n          ...prev,\n          id_bobina: ''\n        }));\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Cavo gi\\xE0 posato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 663,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Il cavo \", alreadyLaidCavo === null || alreadyLaidCavo === void 0 ? void 0 : alreadyLaidCavo.id_cavo, \" \\xE8 gi\\xE0 stato posato.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"Puoi:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"ul\",\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 641,\n    columnNumber: 5\n  }, this);\n};\n_s(MetriPosatiSemplificatoForm, \"cfP9Z/492eSDo8ImuS/ObPalitY=\", false, function () {\n  return [useNavigate];\n});\n_c = MetriPosatiSemplificatoForm;\nexport default MetriPosatiSemplificatoForm;\nvar _c;\n$RefreshReg$(_c, \"MetriPosatiSemplificatoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "CircularProgress", "<PERSON><PERSON>", "Chip", "Divider", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "useNavigate", "caviService", "parcoCaviService", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "redirectToVisualizzaCavi", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MetriPosatiSemplificatoForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "saving", "setSaving", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "caviAttivi", "filter", "error", "console", "message", "bobine<PERSON><PERSON>", "getBobine", "handleCavoSelect", "handleInputChange", "e", "name", "value", "target", "prev", "validateField", "newErrors", "newWarnings", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "<PERSON><PERSON><PERSON><PERSON>", "find", "b", "metri_residui", "validateForm", "Object", "keys", "length", "checkCompatibility", "tipologiaCompatibile", "tipologia", "sezioneCompatibile", "String", "sezione", "handleSave", "log", "updateMetri<PERSON><PERSON><PERSON>", "handleUpdateCavoForCompatibility", "updateCavoForCompatibility", "handleCloseAlreadyLaidDialog", "handleSelectAnotherCable", "handleModifyReel", "getCompatibleBobine", "TERMINATA", "renderCaviTable", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "component", "mb", "size", "bgcolor", "map", "ubicazione_partenza", "ubicazione_arrivo", "label", "stato_installazione", "color", "variant", "onClick", "disabled", "renderForm", "compatibleBobine", "p", "gutterBottom", "borderRadius", "fontWeight", "container", "spacing", "item", "xs", "md", "ml", "fullWidth", "onChange", "type", "helperText", "style", "InputProps", "inputProps", "min", "step", "includes", "mt", "open", "onClose", "onUpdateCavo", "onSelectAnotherReel", "mr", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/MetriPosatiSemplificatoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  CircularProgress,\n  Alert,\n  Chip,\n  Divider,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  DialogContentText\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst MetriPosatiSemplificatoForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      setBobine(bobineData);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n\n    setSelectedCavo(cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = { ...formErrors };\n    const newWarnings = { ...formWarnings };\n\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Validazione completa\n    if (!validateForm()) {\n      return;\n    }\n\n    // Verifica compatibilità\n    if (!checkCompatibility()) {\n      // Mostra dialog per incompatibilità\n      const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n      setIncompatibleReelData({\n        cavo: selectedCavo,\n        bobina: selectedBobina\n      });\n      setShowIncompatibleReelDialog(true);\n      return;\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n\n      const { cavo, bobina } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(\n        cantiereId,\n        cavo.id_cavo,\n        {\n          id_bobina: bobina.id_bobina,\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione\n        }\n      );\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        parseFloat(formData.metri_posati),\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Filtra le bobine compatibili con il cavo selezionato\n  const getCompatibleBobine = () => {\n    if (!selectedCavo) return [];\n\n    return bobine.filter(bobina =>\n      bobina.tipologia === selectedCavo.tipologia &&\n      String(bobina.sezione) === String(selectedCavo.sezione) &&\n      determineReelState(bobina) !== REEL_STATES.TERMINATA\n    );\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          Nessun cavo disponibile per questo cantiere.\n        </Alert>\n      );\n    }\n\n    return (\n      <TableContainer component={Paper} sx={{ mb: 3 }}>\n        <Table size=\"small\">\n          <TableHead>\n            <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n              <TableCell>ID Cavo</TableCell>\n              <TableCell>Tipologia</TableCell>\n              <TableCell>Ubicazione</TableCell>\n              <TableCell>Metri Teorici</TableCell>\n              <TableCell>Stato</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {cavi.map((cavo) => (\n              <TableRow key={cavo.id_cavo}>\n                <TableCell>{cavo.id_cavo}</TableCell>\n                <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                <TableCell>\n                  <Chip\n                    label={cavo.stato_installazione || 'N/D'}\n                    size=\"small\"\n                    color={getCableStateColor(cavo.stato_installazione)}\n                    variant=\"outlined\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Button\n                    size=\"small\"\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={() => handleCavoSelect(cavo)}\n                    disabled={isCableInstalled(cavo)}\n                  >\n                    Seleziona\n                  </Button>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    );\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    const compatibleBobine = getCompatibleBobine();\n\n    return (\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserimento metri posati\n        </Typography>\n\n        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom fontWeight=\"bold\" color=\"primary\">\n            Cavo selezionato: {selectedCavo.id_cavo}\n          </Typography>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12}>\n              <Typography variant=\"body2\">\n                <strong>Stato:</strong>\n                <Chip\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  size=\"small\"\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  variant=\"outlined\"\n                  sx={{ ml: 1 }}\n                />\n              </Typography>\n            </Grid>\n          </Grid>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <TextField\n              fullWidth\n              label=\"Metri posati\"\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleInputChange}\n              type=\"number\"\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || (formWarnings.metri_posati && (\n                <span style={{ color: 'orange' }}>{formWarnings.metri_posati}</span>\n              ))}\n              disabled={saving}\n              InputProps={{\n                inputProps: { min: 0, step: 0.1 }\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <FormControl fullWidth error={!!formErrors.id_bobina}>\n              <InputLabel>Bobina</InputLabel>\n              <Select\n                name=\"id_bobina\"\n                value={formData.id_bobina}\n                onChange={handleInputChange}\n                disabled={saving || bobineLoading}\n              >\n                {/* Opzione BOBINA VUOTA sempre disponibile e in evidenza */}\n                <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                  BOBINA VUOTA (Cavo posato senza bobina)\n                </MenuItem>\n\n                {/* Separatore */}\n                <Divider />\n\n                {/* Messaggio se non ci sono bobine compatibili */}\n                {compatibleBobine.length === 0 ? (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\n                    </Typography>\n                  </MenuItem>\n                ) : (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\">\n                      Bobine compatibili ({compatibleBobine.length})\n                    </Typography>\n                  </MenuItem>\n                )}\n\n                {/* Bobine compatibili */}\n                {compatibleBobine.map((bobina) => (\n                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                    {bobina.id_bobina} - {bobina.tipologia} - {bobina.metri_residui}m\n                  </MenuItem>\n                ))}\n\n                {/* Bobine non compatibili */}\n                {bobine.filter(bobina => !compatibleBobine.includes(bobina) && determineReelState(bobina) !== REEL_STATES.TERMINATA).length > 0 && (\n                  <>\n                    <Divider />\n                    <MenuItem disabled>\n                      <Typography variant=\"caption\">\n                        Bobine non compatibili\n                      </Typography>\n                    </MenuItem>\n                    {bobine\n                      .filter(bobina => !compatibleBobine.includes(bobina) && determineReelState(bobina) !== REEL_STATES.TERMINATA)\n                      .map((bobina) => (\n                        <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                          {bobina.id_bobina} - {bobina.tipologia} - {bobina.metri_residui}m\n                        </MenuItem>\n                      ))}\n                  </>\n                )}\n              </Select>\n              {formErrors.id_bobina && (\n                <Typography variant=\"caption\" color=\"error\">\n                  {formErrors.id_bobina}\n                </Typography>\n              )}\n              {formWarnings.id_bobina && (\n                <Typography variant=\"caption\" sx={{ color: 'orange' }}>\n                  {formWarnings.id_bobina}\n                </Typography>\n              )}\n              {/* Messaggio informativo sotto il campo */}\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                Seleziona una bobina o usa BOBINA VUOTA se il cavo è stato posato senza una bobina specifica.\n              </Typography>\n            </FormControl>\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            onClick={() => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            }}\n            disabled={saving}\n          >\n            Annulla\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSave}\n            disabled={saving || Object.keys(formErrors).length > 0}\n          >\n            {saving ? <CircularProgress size={24} /> : 'Salva'}\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Tabella cavi */}\n      {!selectedCavo && renderCaviTable()}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {renderForm()}\n\n      {/* Dialog per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={() => setShowIncompatibleReelDialog(false)}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoForCompatibility}\n        onSelectAnotherReel={() => {\n          setShowIncompatibleReelDialog(false);\n          setFormData(prev => ({ ...prev, id_bobina: '' }));\n        }}\n      />\n\n      {/* Dialog per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog}>\n        <DialogTitle>Cavo già posato</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Il cavo {alreadyLaidCavo?.id_cavo} è già stato posato.\n          </DialogContentText>\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              Puoi:\n            </Typography>\n            <Typography component=\"ul\" variant=\"body2\">\n              <li>Modificare la bobina associata</li>\n              <li>Selezionare un altro cavo</li>\n              <li>Annullare l'operazione</li>\n            </Typography>\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default MetriPosatiSemplificatoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACZ,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,2BAA2B,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAAC0E,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC;IAAE8E,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACdmF,QAAQ,CAAC,CAAC;IACVC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACvC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFnB,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMqB,QAAQ,GAAG,MAAM1D,WAAW,CAAC2D,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAACX,IAAI,IAAI,CAAC3C,YAAY,CAAC2C,IAAI,CAAC,CAAC;MAE/D1B,OAAO,CAACoC,UAAU,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD1C,OAAO,CAAC,mCAAmC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAM0B,UAAU,GAAG,MAAMhE,gBAAgB,CAACiE,SAAS,CAAChD,UAAU,CAAC;MAC/DQ,SAAS,CAACuC,UAAU,CAAC;IACvB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1C,OAAO,CAAC,uCAAuC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM4B,gBAAgB,GAAIjB,IAAI,IAAK;IACjC;IACA,IAAI1C,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MAC1BK,kBAAkB,CAACL,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IAEAzB,eAAe,CAACsB,IAAI,CAAC;IACrBpB,WAAW,CAAC;MACVC,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFU,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMuB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC1C,WAAW,CAAC2C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACAG,aAAa,CAACJ,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAACJ,IAAI,EAAEC,KAAK,KAAK;IACrC,MAAMI,SAAS,GAAG;MAAE,GAAGjC;IAAW,CAAC;IACnC,MAAMkC,WAAW,GAAG;MAAE,GAAGhC;IAAa,CAAC;IAEvC,IAAI0B,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAAC3C,YAAY,GAAG,iCAAiC;MAC5D,CAAC,MAAM,IAAI6C,KAAK,CAACN,KAAK,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,GAAG,CAAC,EAAE;QAChDI,SAAS,CAAC3C,YAAY,GAAG,iDAAiD;MAC5E,CAAC,MAAM;QACL,OAAO2C,SAAS,CAAC3C,YAAY;;QAE7B;QACA,MAAM+C,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;QACrC,IAAI5C,YAAY,IAAIoD,WAAW,GAAGpD,YAAY,CAACqD,aAAa,EAAE;UAC5DJ,WAAW,CAAC5C,YAAY,GAAG,mBAAmB+C,WAAW,+BAA+BpD,YAAY,CAACqD,aAAa,GAAG;QACvH,CAAC,MAAM;UACL,OAAOJ,WAAW,CAAC5C,YAAY;QACjC;;QAEA;QACA,IAAIH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;UAC/D,MAAMgD,cAAc,GAAGxD,MAAM,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;UAC3E,IAAIgD,cAAc,IAAIF,WAAW,GAAGE,cAAc,CAACG,aAAa,EAAE;YAChER,WAAW,CAAC3C,SAAS,GAAG,mBAAmB8C,WAAW,4CAA4CE,cAAc,CAACG,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOR,WAAW,CAAC3C,SAAS;UAC9B;QACF;MACF;IACF;IAEA,IAAIqC,IAAI,KAAK,WAAW,EAAE;MACxB;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAAC1C,SAAS,GAAG,0BAA0B;MAClD,CAAC,MAAM;QACL,OAAO0C,SAAS,CAAC1C,SAAS;;QAE1B;QACA,IAAIsC,KAAK,KAAK,cAAc,IAAI1C,QAAQ,CAACG,YAAY,EAAE;UACrD,MAAM+C,WAAW,GAAGD,UAAU,CAACjD,QAAQ,CAACG,YAAY,CAAC;UACrD,MAAMiD,cAAc,GAAGxD,MAAM,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClD,SAAS,KAAKsC,KAAK,CAAC;UAC9D,IAAIU,cAAc,IAAIF,WAAW,GAAGE,cAAc,CAACG,aAAa,EAAE;YAChER,WAAW,CAAC3C,SAAS,GAAG,mBAAmB8C,WAAW,4CAA4CE,cAAc,CAACG,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOR,WAAW,CAAC3C,SAAS;UAC9B;QACF;MACF;IACF;IAEAU,aAAa,CAACgC,SAAS,CAAC;IACxB9B,eAAe,CAAC+B,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMV,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC9C,QAAQ,CAACG,YAAY,EAAE;MAC1B2C,SAAS,CAAC3C,YAAY,GAAG,iCAAiC;IAC5D,CAAC,MAAM,IAAI6C,KAAK,CAAChD,QAAQ,CAACG,YAAY,CAAC,IAAI8C,UAAU,CAACjD,QAAQ,CAACG,YAAY,CAAC,GAAG,CAAC,EAAE;MAChF2C,SAAS,CAAC3C,YAAY,GAAG,iDAAiD;IAC5E;;IAEA;IACA,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;MACvB0C,SAAS,CAAC1C,SAAS,GAAG,0BAA0B;IAClD;IAEAU,aAAa,CAACgC,SAAS,CAAC;IACxB,OAAOW,MAAM,CAACC,IAAI,CAACZ,SAAS,CAAC,CAACa,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI5D,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,MAAMgD,cAAc,GAAGxD,MAAM,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IAC3E,IAAI,CAACgD,cAAc,EAAE;MACnB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMS,oBAAoB,GAAG/D,YAAY,CAACgE,SAAS,KAAKV,cAAc,CAACU,SAAS;;IAEhF;IACA,MAAMC,kBAAkB,GAAGC,MAAM,CAAClE,YAAY,CAACmE,OAAO,CAAC,KAAKD,MAAM,CAACZ,cAAc,CAACa,OAAO,CAAC;IAE1F,OAAOJ,oBAAoB,IAAIE,kBAAkB;EACnD,CAAC;;EAED;EACA,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAI,CAACV,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACA,IAAI,CAACI,kBAAkB,CAAC,CAAC,EAAE;MACzB;MACA,MAAMR,cAAc,GAAGxD,MAAM,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;MAC3EgB,uBAAuB,CAAC;QACtBC,IAAI,EAAEvB,YAAY;QAClBwB,MAAM,EAAE8B;MACV,CAAC,CAAC;MACFlC,6BAA6B,CAAC,IAAI,CAAC;MACnC;IACF;;IAEA;IACA,IAAI;MACFN,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAMsC,WAAW,GAAGD,UAAU,CAACjD,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA+B,OAAO,CAACiC,GAAG,CAAC,6DAA6D,CAAC;MAC1EjC,OAAO,CAACiC,GAAG,CAAC,eAAe,EAAE9E,UAAU,CAAC;MACxC6C,OAAO,CAACiC,GAAG,CAAC,YAAY,EAAEnE,QAAQ,CAACE,OAAO,CAAC;MAC3CgC,OAAO,CAACiC,GAAG,CAAC,iBAAiB,EAAEjB,WAAW,CAAC;MAC3ChB,OAAO,CAACiC,GAAG,CAAC,cAAc,EAAEnE,QAAQ,CAACI,SAAS,CAAC;;MAE/C;MACA,MAAMjC,WAAW,CAACiG,iBAAiB,CACjC/E,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChBgD,WAAW,EACXlD,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,sCAAsC,CAAC;;MAEjD;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD1C,OAAO,CAAC,iCAAiC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMyD,gCAAgC,GAAG,MAAAA,CAAA,KAAY;IACnD,IAAI;MACFzD,SAAS,CAAC,IAAI,CAAC;MACfM,6BAA6B,CAAC,KAAK,CAAC;MAEpC,MAAM;QAAEG,IAAI;QAAEC;MAAO,CAAC,GAAGH,oBAAoB;;MAE7C;MACA,MAAMhD,WAAW,CAACmG,0BAA0B,CAC1CjF,UAAU,EACVgC,IAAI,CAACnB,OAAO,EACZ;QACEE,SAAS,EAAEkB,MAAM,CAAClB,SAAS;QAC3B0D,SAAS,EAAExC,MAAM,CAACwC,SAAS;QAC3BG,OAAO,EAAE3C,MAAM,CAAC2C;MAClB,CACF,CAAC;;MAED;MACA,MAAM9F,WAAW,CAACiG,iBAAiB,CACjC/E,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChB+C,UAAU,CAACjD,QAAQ,CAACG,YAAY,CAAC,EACjCH,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,wDAAwD,CAAC;;MAEnE;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE1C,OAAO,CAAC,4CAA4C,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACjG,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM2D,4BAA4B,GAAGA,CAAA,KAAM;IACzC/C,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM8C,wBAAwB,GAAGA,CAAA,KAAM;IACrCD,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhD,eAAe,EAAE;MACnBhC,QAAQ,CAAC,+CAA+CgC,eAAe,CAACvB,OAAO,EAAE,CAAC;IACpF;IACAqE,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC5E,YAAY,EAAE,OAAO,EAAE;IAE5B,OAAOF,MAAM,CAACoC,MAAM,CAACV,MAAM,IACzBA,MAAM,CAACwC,SAAS,KAAKhE,YAAY,CAACgE,SAAS,IAC3CE,MAAM,CAAC1C,MAAM,CAAC2C,OAAO,CAAC,KAAKD,MAAM,CAAClE,YAAY,CAACmE,OAAO,CAAC,IACvDzF,kBAAkB,CAAC8C,MAAM,CAAC,KAAKhD,WAAW,CAACqG,SAC7C,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIrE,WAAW,EAAE;MACf,oBACEtB,OAAA,CAACxC,GAAG;QAACoI,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5DhG,OAAA,CAAC7B,gBAAgB;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAI3F,IAAI,CAACiE,MAAM,KAAK,CAAC,EAAE;MACrB,oBACE1E,OAAA,CAAC5B,KAAK;QAACiI,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACEpG,OAAA,CAAChC,cAAc;MAACsI,SAAS,EAAE1I,KAAM;MAACgI,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,eAC9ChG,OAAA,CAACnC,KAAK;QAAC2I,IAAI,EAAC,OAAO;QAAAR,QAAA,gBACjBhG,OAAA,CAAC/B,SAAS;UAAA+H,QAAA,eACRhG,OAAA,CAAC9B,QAAQ;YAAC0H,EAAE,EAAE;cAAEa,OAAO,EAAE;YAAU,CAAE;YAAAT,QAAA,gBACnChG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9BpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjCpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpCpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZpG,OAAA,CAAClC,SAAS;UAAAkI,QAAA,EACPvF,IAAI,CAACiG,GAAG,CAAEtE,IAAI,iBACbpC,OAAA,CAAC9B,QAAQ;YAAA8H,QAAA,gBACPhG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,EAAE5D,IAAI,CAACnB;YAAO;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,EAAE5D,IAAI,CAACyC,SAAS,IAAI;YAAK;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChDpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,GAAC,MAAI,EAAC5D,IAAI,CAACuE,mBAAmB,IAAI,KAAK,eAAC3G,OAAA;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,OAAG,EAAChE,IAAI,CAACwE,iBAAiB,IAAI,KAAK;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvGpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,GAAE5D,IAAI,CAAC8B,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtDpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,eACRhG,OAAA,CAAC3B,IAAI;gBACHwI,KAAK,EAAEzE,IAAI,CAAC0E,mBAAmB,IAAI,KAAM;gBACzCN,IAAI,EAAC,OAAO;gBACZO,KAAK,EAAEpH,kBAAkB,CAACyC,IAAI,CAAC0E,mBAAmB,CAAE;gBACpDE,OAAO,EAAC;cAAU;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZpG,OAAA,CAACjC,SAAS;cAAAiI,QAAA,eACRhG,OAAA,CAACrC,MAAM;gBACL6I,IAAI,EAAC,OAAO;gBACZQ,OAAO,EAAC,WAAW;gBACnBD,KAAK,EAAC,SAAS;gBACfE,OAAO,EAAEA,CAAA,KAAM5D,gBAAgB,CAACjB,IAAI,CAAE;gBACtC8E,QAAQ,EAAExH,gBAAgB,CAAC0C,IAAI,CAAE;gBAAA4D,QAAA,EAClC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,GAvBChE,IAAI,CAACnB,OAAO;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBjB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAErB,CAAC;;EAED;EACA,MAAMe,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACtG,YAAY,EAAE,OAAO,IAAI;IAE9B,MAAMuG,gBAAgB,GAAG3B,mBAAmB,CAAC,CAAC;IAE9C,oBACEzF,OAAA,CAACpC,KAAK;MAACgI,EAAE,EAAE;QAAEyB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,gBAClBhG,OAAA,CAACvC,UAAU;QAACuJ,OAAO,EAAC,IAAI;QAACM,YAAY;QAAAtB,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbpG,OAAA,CAACxC,GAAG;QAACoI,EAAE,EAAE;UAAEW,EAAE,EAAE,CAAC;UAAEc,CAAC,EAAE,CAAC;UAAEZ,OAAO,EAAE,SAAS;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC5DhG,OAAA,CAACvC,UAAU;UAACuJ,OAAO,EAAC,WAAW;UAACM,YAAY;UAACE,UAAU,EAAC,MAAM;UAACT,KAAK,EAAC,SAAS;UAAAf,QAAA,GAAC,oBAC3D,EAACnF,YAAY,CAACI,OAAO;QAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACbpG,OAAA,CAACzB,IAAI;UAACkJ,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBhG,OAAA,CAACzB,IAAI;YAACoJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBhG,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBhG,OAAA;gBAAAgG,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvF,YAAY,CAACgE,SAAS,IAAI,KAAK;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPpG,OAAA,CAACzB,IAAI;YAACoJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBhG,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBhG,OAAA;gBAAAgG,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvF,YAAY,CAACmE,OAAO,IAAI,KAAK;YAAA;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPpG,OAAA,CAACzB,IAAI;YAACoJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBhG,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBhG,OAAA;gBAAAgG,QAAA,EAAQ;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvF,YAAY,CAACqD,aAAa,IAAI,KAAK,EAAC,IACvE;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPpG,OAAA,CAACzB,IAAI;YAACoJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBhG,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBhG,OAAA;gBAAAgG,QAAA,EAAQ;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvF,YAAY,CAAC8F,mBAAmB,IAAI,KAAK;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPpG,OAAA,CAACzB,IAAI;YAACoJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBhG,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBhG,OAAA;gBAAAgG,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvF,YAAY,CAAC+F,iBAAiB,IAAI,KAAK;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPpG,OAAA,CAACzB,IAAI;YAACoJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA5B,QAAA,eAChBhG,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBhG,OAAA;gBAAAgG,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvBpG,OAAA,CAAC3B,IAAI;gBACHwI,KAAK,EAAEhG,YAAY,CAACiG,mBAAmB,IAAI,KAAM;gBACjDN,IAAI,EAAC,OAAO;gBACZO,KAAK,EAAEpH,kBAAkB,CAACkB,YAAY,CAACiG,mBAAmB,CAAE;gBAC5DE,OAAO,EAAC,UAAU;gBAClBpB,EAAE,EAAE;kBAAEkC,EAAE,EAAE;gBAAE;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENpG,OAAA,CAAC1B,OAAO;QAACsH,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BpG,OAAA,CAACzB,IAAI;QAACkJ,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1B,QAAA,gBACzBhG,OAAA,CAACzB,IAAI;UAACoJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,eACvBhG,OAAA,CAACtC,SAAS;YACRqK,SAAS;YACTlB,KAAK,EAAC,cAAc;YACpBrD,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAE1C,QAAQ,CAACG,YAAa;YAC7B8G,QAAQ,EAAE1E,iBAAkB;YAC5B2E,IAAI,EAAC,QAAQ;YACbjF,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACV,YAAa;YACjCgH,UAAU,EAAEtG,UAAU,CAACV,YAAY,IAAKY,YAAY,CAACZ,YAAY,iBAC/DlB,OAAA;cAAMmI,KAAK,EAAE;gBAAEpB,KAAK,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAElE,YAAY,CAACZ;YAAY;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAClE;YACHc,QAAQ,EAAExF,MAAO;YACjB0G,UAAU,EAAE;cACVC,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,IAAI,EAAE;cAAI;YAClC;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPpG,OAAA,CAACzB,IAAI;UAACoJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,eACvBhG,OAAA,CAACxB,WAAW;YAACuJ,SAAS;YAAC/E,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACT,SAAU;YAAA6E,QAAA,gBACnDhG,OAAA,CAACvB,UAAU;cAAAuH,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BpG,OAAA,CAACtB,MAAM;cACL8E,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAE1C,QAAQ,CAACI,SAAU;cAC1B6G,QAAQ,EAAE1E,iBAAkB;cAC5B4D,QAAQ,EAAExF,MAAM,IAAIF,aAAc;cAAAwE,QAAA,gBAGlChG,OAAA,CAACrB,QAAQ;gBAAC8E,KAAK,EAAC,cAAc;gBAACmC,EAAE,EAAE;kBAAE4B,UAAU,EAAE,MAAM;kBAAET,KAAK,EAAE,SAAS;kBAAEN,OAAO,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGXpG,OAAA,CAAC1B,OAAO;gBAAA2H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAGVgB,gBAAgB,CAAC1C,MAAM,KAAK,CAAC,gBAC5B1E,OAAA,CAACrB,QAAQ;gBAACuI,QAAQ;gBAAAlB,QAAA,eAChBhG,OAAA,CAACvC,UAAU;kBAACuJ,OAAO,EAAC,SAAS;kBAACD,KAAK,EAAC,gBAAgB;kBAAAf,QAAA,EAAC;gBAErD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAEXpG,OAAA,CAACrB,QAAQ;gBAACuI,QAAQ;gBAAAlB,QAAA,eAChBhG,OAAA,CAACvC,UAAU;kBAACuJ,OAAO,EAAC,SAAS;kBAAAhB,QAAA,GAAC,sBACR,EAACoB,gBAAgB,CAAC1C,MAAM,EAAC,GAC/C;gBAAA;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACX,EAGAgB,gBAAgB,CAACV,GAAG,CAAErE,MAAM,iBAC3BrC,OAAA,CAACrB,QAAQ;gBAAwB8E,KAAK,EAAEpB,MAAM,CAAClB,SAAU;gBAAA6E,QAAA,GACtD3D,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACwC,SAAS,EAAC,KAAG,EAACxC,MAAM,CAACiC,aAAa,EAAC,GAClE;cAAA,GAFejC,MAAM,CAAClB,SAAS;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErB,CACX,CAAC,EAGDzF,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAI,CAAC+E,gBAAgB,CAACoB,QAAQ,CAACnG,MAAM,CAAC,IAAI9C,kBAAkB,CAAC8C,MAAM,CAAC,KAAKhD,WAAW,CAACqG,SAAS,CAAC,CAAChB,MAAM,GAAG,CAAC,iBAC7H1E,OAAA,CAAAE,SAAA;gBAAA8F,QAAA,gBACEhG,OAAA,CAAC1B,OAAO;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXpG,OAAA,CAACrB,QAAQ;kBAACuI,QAAQ;kBAAAlB,QAAA,eAChBhG,OAAA,CAACvC,UAAU;oBAACuJ,OAAO,EAAC,SAAS;oBAAAhB,QAAA,EAAC;kBAE9B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACVzF,MAAM,CACJoC,MAAM,CAACV,MAAM,IAAI,CAAC+E,gBAAgB,CAACoB,QAAQ,CAACnG,MAAM,CAAC,IAAI9C,kBAAkB,CAAC8C,MAAM,CAAC,KAAKhD,WAAW,CAACqG,SAAS,CAAC,CAC5GgB,GAAG,CAAErE,MAAM,iBACVrC,OAAA,CAACrB,QAAQ;kBAAwB8E,KAAK,EAAEpB,MAAM,CAAClB,SAAU;kBAAA6E,QAAA,GACtD3D,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACwC,SAAS,EAAC,KAAG,EAACxC,MAAM,CAACiC,aAAa,EAAC,GAClE;gBAAA,GAFejC,MAAM,CAAClB,SAAS;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErB,CACX,CAAC;cAAA,eACJ,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,EACRxE,UAAU,CAACT,SAAS,iBACnBnB,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,OAAO;cAAAf,QAAA,EACxCpE,UAAU,CAACT;YAAS;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb,EACAtE,YAAY,CAACX,SAAS,iBACrBnB,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,SAAS;cAACpB,EAAE,EAAE;gBAAEmB,KAAK,EAAE;cAAS,CAAE;cAAAf,QAAA,EACnDlE,YAAY,CAACX;YAAS;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACb,eAEDpG,OAAA,CAACvC,UAAU;cAACuJ,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,gBAAgB;cAACnB,EAAE,EAAE;gBAAE6C,EAAE,EAAE;cAAE,CAAE;cAAAzC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPpG,OAAA,CAACxC,GAAG;QAACoI,EAAE,EAAE;UAAE6C,EAAE,EAAE,CAAC;UAAE5C,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBACnEhG,OAAA,CAACrC,MAAM;UACLqJ,OAAO,EAAC,UAAU;UAClBD,KAAK,EAAC,WAAW;UACjBE,OAAO,EAAEA,CAAA,KAAM;YACbnG,eAAe,CAAC,IAAI,CAAC;YACrBE,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACF+F,QAAQ,EAAExF,MAAO;UAAAsE,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpG,OAAA,CAACrC,MAAM;UACLqJ,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACfE,OAAO,EAAEhC,UAAW;UACpBiC,QAAQ,EAAExF,MAAM,IAAI8C,MAAM,CAACC,IAAI,CAAC7C,UAAU,CAAC,CAAC8C,MAAM,GAAG,CAAE;UAAAsB,QAAA,EAEtDtE,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;YAACqI,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;EAED,oBACEpG,OAAA,CAACxC,GAAG;IAAAwI,QAAA,GAED,CAACnF,YAAY,IAAI8E,eAAe,CAAC,CAAC,EAGlCwB,UAAU,CAAC,CAAC,eAGbnH,OAAA,CAACF,sBAAsB;MACrB4I,IAAI,EAAE1G,0BAA2B;MACjC2G,OAAO,EAAEA,CAAA,KAAM1G,6BAA6B,CAAC,KAAK,CAAE;MACpDG,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCuG,YAAY,EAAExD,gCAAiC;MAC/CyD,mBAAmB,EAAEA,CAAA,KAAM;QACzB5G,6BAA6B,CAAC,KAAK,CAAC;QACpCjB,WAAW,CAAC2C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAExC,SAAS,EAAE;QAAG,CAAC,CAAC,CAAC;MACnD;IAAE;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFpG,OAAA,CAACpB,MAAM;MAAC8J,IAAI,EAAEpG,qBAAsB;MAACqG,OAAO,EAAErD,4BAA6B;MAAAU,QAAA,gBACzEhG,OAAA,CAACnB,WAAW;QAAAmH,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC1CpG,OAAA,CAAClB,aAAa;QAAAkH,QAAA,gBACZhG,OAAA,CAAChB,iBAAiB;UAAAgH,QAAA,GAAC,UACT,EAACxD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvB,OAAO,EAAC,4BACpC;QAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBpG,OAAA,CAACxC,GAAG;UAACoI,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBACjBhG,OAAA,CAACvC,UAAU;YAACuJ,OAAO,EAAC,OAAO;YAACM,YAAY;YAAAtB,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpG,OAAA,CAACvC,UAAU;YAAC6I,SAAS,EAAC,IAAI;YAACU,OAAO,EAAC,OAAO;YAAAhB,QAAA,gBACxChG,OAAA;cAAAgG,QAAA,EAAI;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCpG,OAAA;cAAAgG,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCpG,OAAA;cAAAgG,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBpG,OAAA,CAACjB,aAAa;QAAC6G,EAAE,EAAE;UAAEyB,CAAC,EAAE,CAAC;UAAEvB,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBAC3DhG,OAAA,CAACrC,MAAM;UAACsJ,OAAO,EAAE3B,4BAA6B;UAACyB,KAAK,EAAC,WAAW;UAAAf,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpG,OAAA,CAACxC,GAAG;UAAAwI,QAAA,gBACFhG,OAAA,CAACrC,MAAM;YAACsJ,OAAO,EAAE1B,wBAAyB;YAACwB,KAAK,EAAC,SAAS;YAACnB,EAAE,EAAE;cAAEkD,EAAE,EAAE;YAAE,CAAE;YAAA9C,QAAA,EAAC;UAE1E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpG,OAAA,CAACrC,MAAM;YAACsJ,OAAO,EAAEzB,gBAAiB;YAACwB,OAAO,EAAC,WAAW;YAACD,KAAK,EAAC,SAAS;YAAAf,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC7F,EAAA,CAhoBIJ,2BAA2B;EAAA,QACdlB,WAAW;AAAA;AAAA8J,EAAA,GADxB5I,2BAA2B;AAkoBjC,eAAeA,2BAA2B;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}