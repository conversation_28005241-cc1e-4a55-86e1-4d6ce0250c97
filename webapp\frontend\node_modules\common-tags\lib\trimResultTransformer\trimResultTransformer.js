'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
/**
 * TemplateTag transformer that trims whitespace on the end result of a tagged template
 * @param  {String} side = '' - The side of the string to trim. Can be 'start' or 'end' (alternatively 'left' or 'right')
 * @return {Object}           - a TemplateTag transformer
 */
var trimResultTransformer = function trimResultTransformer() {
  var side = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  return {
    onEndResult: function onEndResult(endResult) {
      if (side === '') {
        return endResult.trim();
      }

      side = side.toLowerCase();

      if (side === 'start' || side === 'left') {
        return endResult.replace(/^\s*/, '');
      }

      if (side === 'end' || side === 'right') {
        return endResult.replace(/\s*$/, '');
      }

      throw new Error('Side not supported: ' + side);
    }
  };
};

exports.default = trimResultTransformer;
module.exports = exports['default'];
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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