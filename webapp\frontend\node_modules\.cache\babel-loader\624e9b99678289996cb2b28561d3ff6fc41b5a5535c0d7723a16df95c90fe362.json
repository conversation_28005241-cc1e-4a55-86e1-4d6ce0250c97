{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\CertificazioneCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const certificazioneRef = useRef();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Con la nuova interfaccia unificata, non abbiamo più bisogno di gestire route specifiche\n  useEffect(() => {\n    console.log('CertificazioneCaviPage caricata per cantiere:', cantiereId);\n  }, [location.pathname, cantiereId]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce le notifiche\n  const handleSuccess = message => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n  const handleError = message => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 22\n        }, this),\n        onClick: handleBackToCantieri,\n        children: \"Torna ai Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CertificazioneCaviImproved, {\n      ref: certificazioneRef,\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(CertificazioneCaviPage, \"qChaAFd9ewnxuqu6/g1V8modph0=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = CertificazioneCaviPage;\nexport default CertificazioneCaviPage;\nvar _c;\n$RefreshReg$(_c, \"CertificazioneCaviPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "useNavigate", "useLocation", "useAuth", "AdminHomeButton", "CertificazioneCaviImproved", "jsxDEV", "_jsxDEV", "CertificazioneCaviPage", "_s", "isImpersonating", "navigate", "location", "certificazioneRef", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "console", "log", "pathname", "handleBackToCantieri", "handleSuccess", "message", "handleError", "error", "isNaN", "children", "severity", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "startIcon", "onClick", "display", "alignItems", "justifyContent", "mr", "window", "reload", "ml", "color", "title", "ref", "onSuccess", "onError", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/CertificazioneCaviPage.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\n\nconst CertificazioneCaviPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const certificazioneRef = useRef();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Con la nuova interfaccia unificata, non abbiamo più bisogno di gestire route specifiche\n  useEffect(() => {\n    console.log('CertificazioneCaviPage caricata per cantiere:', cantiereId);\n  }, [location.pathname, cantiereId]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n\n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n\n  const handleError = (message) => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          startIcon={<ArrowBackIcon />}\n          onClick={handleBackToCantieri}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n\n\n      <CertificazioneCaviImproved\n        ref={certificazioneRef}\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n    </Box>\n  );\n};\n\nexport default CertificazioneCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,0BAA0B,MAAM,kDAAkD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1F,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAgB,CAAC,GAAGP,OAAO,CAAC,CAAC;EACrC,MAAMQ,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,iBAAiB,GAAGzB,MAAM,CAAC,CAAC;;EAElC;EACA,MAAM0B,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYH,UAAU,EAAE;;EAE7F;EACA3B,SAAS,CAAC,MAAM;IACdgC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEN,UAAU,CAAC;EAC1E,CAAC,EAAE,CAACF,QAAQ,CAACS,QAAQ,EAAEP,UAAU,CAAC,CAAC;;EAEnC;EACA,MAAMQ,oBAAoB,GAAGA,CAAA,KAAM;IACjCX,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAID;EACA,MAAMY,aAAa,GAAIC,OAAO,IAAK;IACjC;IACAL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEI,OAAO,CAAC;EACnC,CAAC;EAED,MAAMC,WAAW,GAAID,OAAO,IAAK;IAC/B;IACAL,OAAO,CAACO,KAAK,CAAC,SAAS,EAAEF,OAAO,CAAC;EACnC,CAAC;EAED,IAAI,CAACV,UAAU,IAAIa,KAAK,CAACb,UAAU,CAAC,EAAE;IACpC,oBACEP,OAAA,CAAClB,GAAG;MAAAuC,QAAA,gBACFrB,OAAA,CAACb,KAAK;QAACmC,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR5B,OAAA,CAACf,MAAM;QACL4C,OAAO,EAAC,WAAW;QACnBC,SAAS,eAAE9B,OAAA,CAACX,aAAa;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BG,OAAO,EAAEhB,oBAAqB;QAAAM,QAAA,EAC/B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE5B,OAAA,CAAClB,GAAG;IAAAuC,QAAA,gBACFrB,OAAA,CAAClB,GAAG;MAACyC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEQ,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAb,QAAA,gBACzFrB,OAAA,CAAClB,GAAG;QAACyC,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAZ,QAAA,gBACjDrB,OAAA,CAACd,UAAU;UAAC6C,OAAO,EAAEhB,oBAAqB;UAACQ,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,eACvDrB,OAAA,CAACX,aAAa;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb5B,OAAA,CAACd,UAAU;UACT6C,OAAO,EAAEA,CAAA,KAAMK,MAAM,CAAC/B,QAAQ,CAACgC,MAAM,CAAC,CAAE;UACxCd,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAnB,QAAA,eAE1BrB,OAAA,CAACT,WAAW;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN5B,OAAA,CAACH,eAAe;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAIN5B,OAAA,CAACF,0BAA0B;MACzB2C,GAAG,EAAEnC,iBAAkB;MACvBC,UAAU,EAAEA,UAAW;MACvBmC,SAAS,EAAE1B,aAAc;MACzB2B,OAAO,EAAEzB;IAAY;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA/EID,sBAAsB;EAAA,QACEL,OAAO,EAClBF,WAAW,EACXC,WAAW;AAAA;AAAAiD,EAAA,GAHxB3C,sBAAsB;AAiF5B,eAAeA,sBAAsB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}