{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\CaviStatoChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, ComposedChart, Line } from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  'Installato': '#2e7d32',\n  'Da Installare': '#ed6c02',\n  'In Progresso': '#1976d2',\n  'Spare': '#9c27b0',\n  'Sospeso': '#d32f2f'\n};\nconst STATUS_COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\nconst CaviStatoChart = ({\n  data\n}) => {\n  if (!data || !data.cavi_per_stato) return null;\n\n  // Prepara dati per i grafici\n  const statoData = data.cavi_per_stato.map(stato => {\n    var _stato$stato;\n    return {\n      ...stato,\n      stato_short: ((_stato$stato = stato.stato) === null || _stato$stato === void 0 ? void 0 : _stato$stato.length) > 12 ? stato.stato.substring(0, 12) + '...' : stato.stato,\n      color: COLORS[stato.stato] || STATUS_COLORS.info,\n      efficienza: stato.metri_teorici > 0 ? stato.metri_reali / stato.metri_teorici * 100 : 0\n    };\n  });\n\n  // Calcola totali\n  const totali = statoData.reduce((acc, stato) => {\n    acc.cavi += stato.num_cavi || 0;\n    acc.metri_teorici += stato.metri_teorici || 0;\n    acc.metri_reali += stato.metri_reali || 0;\n    return acc;\n  }, {\n    cavi: 0,\n    metri_teorici: 0,\n    metri_reali: 0\n  });\n\n  // Dati per grafico a torta\n  const pieData = statoData.map(stato => ({\n    name: stato.stato,\n    value: stato.num_cavi,\n    color: stato.color\n  }));\n\n  // Dati per confronto metri\n  const metriData = statoData.map(stato => ({\n    stato: stato.stato_short,\n    stato_full: stato.stato,\n    metri_teorici: stato.metri_teorici || 0,\n    metri_reali: stato.metri_reali || 0,\n    differenza: (stato.metri_reali || 0) - (stato.metri_teorici || 0)\n  }));\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null;\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Analisi Cavi per Stato di Installazione\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Riepilogo Generale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 4,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  children: totali.cavi\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Cavi Totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 4,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"info.main\",\n                  children: [totali.metri_teorici.toFixed(0), \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Metri Teorici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 4,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"success.main\",\n                  children: [totali.metri_reali.toFixed(0), \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Metri Reali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 175\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Distribuzione Cavi per Stato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 140,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: pieData,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: renderCustomizedLabel,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                children: pieData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 175\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Numero Cavi per Stato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 140,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: statoData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"stato_short\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"num_cavi\",\n                fill: STATUS_COLORS.primary,\n                name: \"Numero Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 175\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Confronto Metri Teorici vs Reali per Stato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 140,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: metriData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"stato\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_teorici\",\n                fill: STATUS_COLORS.info,\n                name: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_reali\",\n                fill: STATUS_COLORS.success,\n                name: \"Metri Reali\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Dettaglio per Stato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: statoData.map((stato, index) => {\n              var _stato$metri_teorici, _stato$metri_reali, _stato$efficienza;\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 4,\n                lg: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 1,\n                    borderLeft: `4px solid ${stato.color}`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: stato.stato,\n                    style: {\n                      backgroundColor: stato.color,\n                      color: 'white'\n                    },\n                    sx: {\n                      mb: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: stato.num_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"cavi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Teorici: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [(_stato$metri_teorici = stato.metri_teorici) === null || _stato$metri_teorici === void 0 ? void 0 : _stato$metri_teorici.toFixed(0), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 32\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Reali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [(_stato$metri_reali = stato.metri_reali) === null || _stato$metri_reali === void 0 ? void 0 : _stato$metri_reali.toFixed(0), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 30\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Efficienza: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [(_stato$efficienza = stato.efficienza) === null || _stato$efficienza === void 0 ? void 0 : _stato$efficienza.toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 21\n                  }, this), stato.metri_reali > stato.metri_teorici && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `+${(stato.metri_reali - stato.metri_teorici).toFixed(0)}m`,\n                    color: \"warning\",\n                    size: \"small\",\n                    sx: {\n                      mt: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this), stato.metri_reali < stato.metri_teorici && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `-${(stato.metri_teorici - stato.metri_reali).toFixed(0)}m`,\n                    color: \"error\",\n                    size: \"small\",\n                    sx: {\n                      mt: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Analisi Efficienza Installazione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stati con Surplus di Metri:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), metriData.filter(stato => stato.differenza > 0).map((stato, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  p: 1,\n                  mb: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1,\n                  borderLeft: `4px solid ${STATUS_COLORS.success}`\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: stato.stato_full\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `+${stato.differenza.toFixed(0)}m`,\n                  color: \"success\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stati con Deficit di Metri:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this), metriData.filter(stato => stato.differenza < 0).map((stato, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  p: 1,\n                  mb: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1,\n                  borderLeft: `4px solid ${STATUS_COLORS.error}`\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: stato.stato_full\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${stato.differenza.toFixed(0)}m`,\n                  color: \"error\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_c = CaviStatoChart;\nexport default CaviStatoChart;\nvar _c;\n$RefreshReg$(_c, \"CaviStatoChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "ComposedChart", "Line", "Box", "Typography", "Grid", "Paper", "Chip", "jsxDEV", "_jsxDEV", "COLORS", "STATUS_COLORS", "primary", "secondary", "success", "warning", "info", "error", "Cavi<PERSON>tato<PERSON>hart", "data", "cavi_per_stato", "statoData", "map", "stato", "_stato$stato", "stato_short", "length", "substring", "color", "efficienza", "metri_te<PERSON>ci", "metri_reali", "totali", "reduce", "acc", "cavi", "num_cavi", "pieData", "name", "value", "metriData", "stato_full", "differenza", "CustomTooltip", "active", "payload", "label", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "index", "style", "toFixed", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "Math", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "gutterBottom", "container", "spacing", "item", "xs", "sm", "textAlign", "borderRadius", "md", "height", "align", "width", "labelLine", "dataKey", "content", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angle", "_stato$metri_teorici", "_stato$metri_reali", "_stato$efficienza", "lg", "borderLeft", "backgroundColor", "mb", "size", "filter", "display", "justifyContent", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/charts/CaviStatoChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CartesianGrid,\n  <PERSON><PERSON><PERSON>,\n  Legend,\n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON>,\n  Pie,\n  Cell,\n  ComposedChart,\n  Line\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  'Installato': '#2e7d32',\n  'Da Installare': '#ed6c02',\n  'In Progresso': '#1976d2',\n  'Spare': '#9c27b0',\n  'Sospeso': '#d32f2f'\n};\n\nconst STATUS_COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\n\nconst CaviStatoChart = ({ data }) => {\n  if (!data || !data.cavi_per_stato) return null;\n\n  // Prepara dati per i grafici\n  const statoData = data.cavi_per_stato.map(stato => ({\n    ...stato,\n    stato_short: stato.stato?.length > 12 ? stato.stato.substring(0, 12) + '...' : stato.stato,\n    color: COLORS[stato.stato] || STATUS_COLORS.info,\n    efficienza: stato.metri_teorici > 0 ? (stato.metri_reali / stato.metri_teorici * 100) : 0\n  }));\n\n  // Calcola totali\n  const totali = statoData.reduce((acc, stato) => {\n    acc.cavi += stato.num_cavi || 0;\n    acc.metri_teorici += stato.metri_teorici || 0;\n    acc.metri_reali += stato.metri_reali || 0;\n    return acc;\n  }, { cavi: 0, metri_teorici: 0, metri_reali: 0 });\n\n  // Dati per grafico a torta\n  const pieData = statoData.map(stato => ({\n    name: stato.stato,\n    value: stato.num_cavi,\n    color: stato.color\n  }));\n\n  // Dati per confronto metri\n  const metriData = statoData.map(stato => ({\n    stato: stato.stato_short,\n    stato_full: stato.stato,\n    metri_teorici: stato.metri_teorici || 0,\n    metri_reali: stato.metri_reali || 0,\n    differenza: (stato.metri_reali || 0) - (stato.metri_teorici || 0)\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text \n        x={x} \n        y={y} \n        fill=\"white\" \n        textAnchor={x > cx ? 'start' : 'end'} \n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Analisi Cavi per Stato di Installazione\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Statistiche Generali */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Riepilogo Generale\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={4}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"primary\">\n                    {totali.cavi}\n                  </Typography>\n                  <Typography variant=\"body2\">Cavi Totali</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"info.main\">\n                    {totali.metri_teorici.toFixed(0)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Metri Teorici</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"success.main\">\n                    {totali.metri_reali.toFixed(0)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Metri Reali</Typography>\n                </Box>\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a torta - Distribuzione Cavi */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 175 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Distribuzione Cavi per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={140}>\n              <PieChart>\n                <Pie\n                  data={pieData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={renderCustomizedLabel}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {pieData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Numero Cavi per Stato */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 175 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Numero Cavi per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={140}>\n              <BarChart data={statoData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"stato_short\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"num_cavi\" fill={STATUS_COLORS.primary} name=\"Numero Cavi\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico confronto metri */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2, height: 175 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Confronto Metri Teorici vs Reali per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={140}>\n              <BarChart data={metriData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"stato\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri_teorici\" fill={STATUS_COLORS.info} name=\"Metri Teorici\" />\n                <Bar dataKey=\"metri_reali\" fill={STATUS_COLORS.success} name=\"Metri Reali\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Dettaglio per Stato */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Dettaglio per Stato\n            </Typography>\n            <Grid container spacing={2}>\n              {statoData.map((stato, index) => (\n                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>\n                  <Box sx={{ \n                    textAlign: 'center', \n                    p: 2, \n                    border: '1px solid #e0e0e0', \n                    borderRadius: 1,\n                    borderLeft: `4px solid ${stato.color}`\n                  }}>\n                    <Chip \n                      label={stato.stato}\n                      style={{ backgroundColor: stato.color, color: 'white' }}\n                      sx={{ mb: 1 }}\n                    />\n                    <Typography variant=\"h6\">{stato.num_cavi}</Typography>\n                    <Typography variant=\"body2\">cavi</Typography>\n                    <Typography variant=\"body2\">\n                      Teorici: <strong>{stato.metri_teorici?.toFixed(0)}m</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Reali: <strong>{stato.metri_reali?.toFixed(0)}m</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Efficienza: <strong>{stato.efficienza?.toFixed(1)}%</strong>\n                    </Typography>\n                    {stato.metri_reali > stato.metri_teorici && (\n                      <Chip \n                        label={`+${(stato.metri_reali - stato.metri_teorici).toFixed(0)}m`}\n                        color=\"warning\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                    {stato.metri_reali < stato.metri_teorici && (\n                      <Chip \n                        label={`-${(stato.metri_teorici - stato.metri_reali).toFixed(0)}m`}\n                        color=\"error\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Analisi Efficienza */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Analisi Efficienza Installazione\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Stati con Surplus di Metri:</strong>\n                </Typography>\n                {metriData\n                  .filter(stato => stato.differenza > 0)\n                  .map((stato, index) => (\n                    <Box key={index} sx={{ \n                      display: 'flex', \n                      justifyContent: 'space-between', \n                      p: 1, \n                      mb: 1,\n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${STATUS_COLORS.success}`\n                    }}>\n                      <Typography variant=\"body2\">{stato.stato_full}</Typography>\n                      <Chip \n                        label={`+${stato.differenza.toFixed(0)}m`}\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  ))}\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Stati con Deficit di Metri:</strong>\n                </Typography>\n                {metriData\n                  .filter(stato => stato.differenza < 0)\n                  .map((stato, index) => (\n                    <Box key={index} sx={{ \n                      display: 'flex', \n                      justifyContent: 'space-between', \n                      p: 1, \n                      mb: 1,\n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${STATUS_COLORS.error}`\n                    }}>\n                      <Typography variant=\"body2\">{stato.stato_full}</Typography>\n                      <Chip \n                        label={`${stato.differenza.toFixed(0)}m`}\n                        color=\"error\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  ))}\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default CaviStatoChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,aAAa,EACbC,IAAI,QACC,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,MAAM,GAAG;EACb,YAAY,EAAE,SAAS;EACvB,eAAe,EAAE,SAAS;EAC1B,cAAc,EAAE,SAAS;EACzB,OAAO,EAAE,SAAS;EAClB,SAAS,EAAE;AACb,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EACnC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,cAAc,EAAE,OAAO,IAAI;;EAE9C;EACA,MAAMC,SAAS,GAAGF,IAAI,CAACC,cAAc,CAACE,GAAG,CAACC,KAAK;IAAA,IAAAC,YAAA;IAAA,OAAK;MAClD,GAAGD,KAAK;MACRE,WAAW,EAAE,EAAAD,YAAA,GAAAD,KAAK,CAACA,KAAK,cAAAC,YAAA,uBAAXA,YAAA,CAAaE,MAAM,IAAG,EAAE,GAAGH,KAAK,CAACA,KAAK,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGJ,KAAK,CAACA,KAAK;MAC1FK,KAAK,EAAElB,MAAM,CAACa,KAAK,CAACA,KAAK,CAAC,IAAIZ,aAAa,CAACK,IAAI;MAChDa,UAAU,EAAEN,KAAK,CAACO,aAAa,GAAG,CAAC,GAAIP,KAAK,CAACQ,WAAW,GAAGR,KAAK,CAACO,aAAa,GAAG,GAAG,GAAI;IAC1F,CAAC;EAAA,CAAC,CAAC;;EAEH;EACA,MAAME,MAAM,GAAGX,SAAS,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEX,KAAK,KAAK;IAC9CW,GAAG,CAACC,IAAI,IAAIZ,KAAK,CAACa,QAAQ,IAAI,CAAC;IAC/BF,GAAG,CAACJ,aAAa,IAAIP,KAAK,CAACO,aAAa,IAAI,CAAC;IAC7CI,GAAG,CAACH,WAAW,IAAIR,KAAK,CAACQ,WAAW,IAAI,CAAC;IACzC,OAAOG,GAAG;EACZ,CAAC,EAAE;IAAEC,IAAI,EAAE,CAAC;IAAEL,aAAa,EAAE,CAAC;IAAEC,WAAW,EAAE;EAAE,CAAC,CAAC;;EAEjD;EACA,MAAMM,OAAO,GAAGhB,SAAS,CAACC,GAAG,CAACC,KAAK,KAAK;IACtCe,IAAI,EAAEf,KAAK,CAACA,KAAK;IACjBgB,KAAK,EAAEhB,KAAK,CAACa,QAAQ;IACrBR,KAAK,EAAEL,KAAK,CAACK;EACf,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMY,SAAS,GAAGnB,SAAS,CAACC,GAAG,CAACC,KAAK,KAAK;IACxCA,KAAK,EAAEA,KAAK,CAACE,WAAW;IACxBgB,UAAU,EAAElB,KAAK,CAACA,KAAK;IACvBO,aAAa,EAAEP,KAAK,CAACO,aAAa,IAAI,CAAC;IACvCC,WAAW,EAAER,KAAK,CAACQ,WAAW,IAAI,CAAC;IACnCW,UAAU,EAAE,CAACnB,KAAK,CAACQ,WAAW,IAAI,CAAC,KAAKR,KAAK,CAACO,aAAa,IAAI,CAAC;EAClE,CAAC,CAAC,CAAC;EAEH,MAAMa,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACnB,MAAM,EAAE;MACvC,oBACEjB,OAAA,CAACH,KAAK;QAACyC,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5CzC,OAAA,CAACL,UAAU;UAAC+C,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGJ,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDV,OAAO,CAACvB,GAAG,CAAC,CAACkC,KAAK,EAAEC,KAAK,kBACxBhD,OAAA,CAACL,UAAU;UAAa+C,OAAO,EAAC,OAAO;UAACO,KAAK,EAAE;YAAE9B,KAAK,EAAE4B,KAAK,CAAC5B;UAAM,CAAE;UAAAsB,QAAA,EACnE,GAAGM,KAAK,CAAClB,IAAI,KAAK,OAAOkB,KAAK,CAACjB,KAAK,KAAK,QAAQ,GAAGiB,KAAK,CAACjB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,GAAGH,KAAK,CAACjB,KAAK;QAAE,GAD5EkB,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMK,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI;IAE/B,MAAMC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGN,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMO,CAAC,GAAGV,EAAE,GAAGS,MAAM,GAAGF,IAAI,CAACI,GAAG,CAAC,CAACT,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMM,CAAC,GAAGX,EAAE,GAAGQ,MAAM,GAAGF,IAAI,CAACM,GAAG,CAAC,CAACX,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACE1D,OAAA;MACE8D,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGV,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCgB,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA7B,QAAA,EAEhB,GAAG,CAACgB,OAAO,GAAG,GAAG,EAAEP,OAAO,CAAC,CAAC,CAAC;IAAG;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACE9C,OAAA,CAACN,GAAG;IAAC4C,EAAE,EAAE;MAAEiC,EAAE,EAAE;IAAE,CAAE;IAAA9B,QAAA,gBACjBzC,OAAA,CAACL,UAAU;MAAC+C,OAAO,EAAC,IAAI;MAAC8B,YAAY;MAAA/B,QAAA,EAAC;IAEtC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb9C,OAAA,CAACJ,IAAI;MAAC6E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjC,QAAA,gBAEzBzC,OAAA,CAACJ,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnC,QAAA,eAChBzC,OAAA,CAACH,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClBzC,OAAA,CAACL,UAAU;YAAC+C,OAAO,EAAC,WAAW;YAAC8B,YAAY;YAAA/B,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACJ,IAAI;YAAC6E,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAjC,QAAA,gBACzBzC,OAAA,CAACJ,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAApC,QAAA,eACvBzC,OAAA,CAACN,GAAG;gBAAC4C,EAAE,EAAE;kBAAEwC,SAAS,EAAE,QAAQ;kBAAEvC,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,mBAAmB;kBAAEuC,YAAY,EAAE;gBAAE,CAAE;gBAAAtC,QAAA,gBACnFzC,OAAA,CAACL,UAAU;kBAAC+C,OAAO,EAAC,IAAI;kBAACvB,KAAK,EAAC,SAAS;kBAAAsB,QAAA,EACrClB,MAAM,CAACG;gBAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACb9C,OAAA,CAACL,UAAU;kBAAC+C,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACP9C,OAAA,CAACJ,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAApC,QAAA,eACvBzC,OAAA,CAACN,GAAG;gBAAC4C,EAAE,EAAE;kBAAEwC,SAAS,EAAE,QAAQ;kBAAEvC,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,mBAAmB;kBAAEuC,YAAY,EAAE;gBAAE,CAAE;gBAAAtC,QAAA,gBACnFzC,OAAA,CAACL,UAAU;kBAAC+C,OAAO,EAAC,IAAI;kBAACvB,KAAK,EAAC,WAAW;kBAAAsB,QAAA,GACvClB,MAAM,CAACF,aAAa,CAAC6B,OAAO,CAAC,CAAC,CAAC,EAAC,GACnC;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9C,OAAA,CAACL,UAAU;kBAAC+C,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACP9C,OAAA,CAACJ,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAApC,QAAA,eACvBzC,OAAA,CAACN,GAAG;gBAAC4C,EAAE,EAAE;kBAAEwC,SAAS,EAAE,QAAQ;kBAAEvC,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,mBAAmB;kBAAEuC,YAAY,EAAE;gBAAE,CAAE;gBAAAtC,QAAA,gBACnFzC,OAAA,CAACL,UAAU;kBAAC+C,OAAO,EAAC,IAAI;kBAACvB,KAAK,EAAC,cAAc;kBAAAsB,QAAA,GAC1ClB,MAAM,CAACD,WAAW,CAAC4B,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9C,OAAA,CAACL,UAAU;kBAAC+C,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9C,OAAA,CAACJ,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACI,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACvBzC,OAAA,CAACH,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE0C,MAAM,EAAE;UAAI,CAAE;UAAAxC,QAAA,gBAC/BzC,OAAA,CAACL,UAAU;YAAC+C,OAAO,EAAC,WAAW;YAAC8B,YAAY;YAACU,KAAK,EAAC,QAAQ;YAAAzC,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACZ,mBAAmB;YAAC+F,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAAxC,QAAA,eAC5CzC,OAAA,CAACX,QAAQ;cAAAoD,QAAA,gBACPzC,OAAA,CAACV,GAAG;gBACFoB,IAAI,EAAEkB,OAAQ;gBACdwB,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACR+B,SAAS,EAAE,KAAM;gBACjB/C,KAAK,EAAEc,qBAAsB;gBAC7BK,WAAW,EAAE,EAAG;gBAChBU,IAAI,EAAC,SAAS;gBACdmB,OAAO,EAAC,OAAO;gBAAA5C,QAAA,EAEdb,OAAO,CAACf,GAAG,CAAC,CAACkC,KAAK,EAAEC,KAAK,kBACxBhD,OAAA,CAACT,IAAI;kBAAuB2E,IAAI,EAAEnB,KAAK,CAAC5B;gBAAM,GAAnC,QAAQ6B,KAAK,EAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9C,OAAA,CAACd,OAAO;gBAACoG,OAAO,eAAEtF,OAAA,CAACkC,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9C,OAAA,CAACb,MAAM;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9C,OAAA,CAACJ,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACI,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACvBzC,OAAA,CAACH,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE0C,MAAM,EAAE;UAAI,CAAE;UAAAxC,QAAA,gBAC/BzC,OAAA,CAACL,UAAU;YAAC+C,OAAO,EAAC,WAAW;YAAC8B,YAAY;YAACU,KAAK,EAAC,QAAQ;YAAAzC,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACZ,mBAAmB;YAAC+F,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAAxC,QAAA,eAC5CzC,OAAA,CAACnB,QAAQ;cAAC6B,IAAI,EAAEE,SAAU;cAAC2E,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAAlD,QAAA,gBAC7EzC,OAAA,CAACf,aAAa;gBAAC2G,eAAe,EAAC;cAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9C,OAAA,CAACjB,KAAK;gBAACsG,OAAO,EAAC,aAAa;gBAACQ,KAAK,EAAE,CAAC,EAAG;gBAAC1B,UAAU,EAAC,KAAK;gBAACc,MAAM,EAAE;cAAG;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxE9C,OAAA,CAAChB,KAAK;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT9C,OAAA,CAACd,OAAO;gBAACoG,OAAO,eAAEtF,OAAA,CAACkC,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9C,OAAA,CAACb,MAAM;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACV9C,OAAA,CAAClB,GAAG;gBAACuG,OAAO,EAAC,UAAU;gBAACnB,IAAI,EAAEhE,aAAa,CAACC,OAAQ;gBAAC0B,IAAI,EAAC;cAAa;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9C,OAAA,CAACJ,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnC,QAAA,eAChBzC,OAAA,CAACH,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE0C,MAAM,EAAE;UAAI,CAAE;UAAAxC,QAAA,gBAC/BzC,OAAA,CAACL,UAAU;YAAC+C,OAAO,EAAC,WAAW;YAAC8B,YAAY;YAACU,KAAK,EAAC,QAAQ;YAAAzC,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACZ,mBAAmB;YAAC+F,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAAxC,QAAA,eAC5CzC,OAAA,CAACnB,QAAQ;cAAC6B,IAAI,EAAEqB,SAAU;cAACwD,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAAlD,QAAA,gBAC7EzC,OAAA,CAACf,aAAa;gBAAC2G,eAAe,EAAC;cAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9C,OAAA,CAACjB,KAAK;gBAACsG,OAAO,EAAC,OAAO;gBAACQ,KAAK,EAAE,CAAC,EAAG;gBAAC1B,UAAU,EAAC,KAAK;gBAACc,MAAM,EAAE;cAAG;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClE9C,OAAA,CAAChB,KAAK;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT9C,OAAA,CAACd,OAAO;gBAACoG,OAAO,eAAEtF,OAAA,CAACkC,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9C,OAAA,CAACb,MAAM;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACV9C,OAAA,CAAClB,GAAG;gBAACuG,OAAO,EAAC,eAAe;gBAACnB,IAAI,EAAEhE,aAAa,CAACK,IAAK;gBAACsB,IAAI,EAAC;cAAe;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9E9C,OAAA,CAAClB,GAAG;gBAACuG,OAAO,EAAC,aAAa;gBAACnB,IAAI,EAAEhE,aAAa,CAACG,OAAQ;gBAACwB,IAAI,EAAC;cAAa;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9C,OAAA,CAACJ,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnC,QAAA,eAChBzC,OAAA,CAACH,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClBzC,OAAA,CAACL,UAAU;YAAC+C,OAAO,EAAC,WAAW;YAAC8B,YAAY;YAAA/B,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACJ,IAAI;YAAC6E,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAjC,QAAA,EACxB7B,SAAS,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEkC,KAAK;cAAA,IAAA8C,oBAAA,EAAAC,kBAAA,EAAAC,iBAAA;cAAA,oBAC1BhG,OAAA,CAACJ,IAAI;gBAAC+E,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAACG,EAAE,EAAE,CAAE;gBAACiB,EAAE,EAAE,CAAE;gBAAAxD,QAAA,eACrCzC,OAAA,CAACN,GAAG;kBAAC4C,EAAE,EAAE;oBACPwC,SAAS,EAAE,QAAQ;oBACnBvC,CAAC,EAAE,CAAC;oBACJC,MAAM,EAAE,mBAAmB;oBAC3BuC,YAAY,EAAE,CAAC;oBACfmB,UAAU,EAAE,aAAapF,KAAK,CAACK,KAAK;kBACtC,CAAE;kBAAAsB,QAAA,gBACAzC,OAAA,CAACF,IAAI;oBACHuC,KAAK,EAAEvB,KAAK,CAACA,KAAM;oBACnBmC,KAAK,EAAE;sBAAEkD,eAAe,EAAErF,KAAK,CAACK,KAAK;sBAAEA,KAAK,EAAE;oBAAQ,CAAE;oBACxDmB,EAAE,EAAE;sBAAE8D,EAAE,EAAE;oBAAE;kBAAE;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACF9C,OAAA,CAACL,UAAU;oBAAC+C,OAAO,EAAC,IAAI;oBAAAD,QAAA,EAAE3B,KAAK,CAACa;kBAAQ;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACtD9C,OAAA,CAACL,UAAU;oBAAC+C,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7C9C,OAAA,CAACL,UAAU;oBAAC+C,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,WACjB,eAAAzC,OAAA;sBAAAyC,QAAA,IAAAqD,oBAAA,GAAShF,KAAK,CAACO,aAAa,cAAAyE,oBAAA,uBAAnBA,oBAAA,CAAqB5C,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACb9C,OAAA,CAACL,UAAU;oBAAC+C,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,SACnB,eAAAzC,OAAA;sBAAAyC,QAAA,IAAAsD,kBAAA,GAASjF,KAAK,CAACQ,WAAW,cAAAyE,kBAAA,uBAAjBA,kBAAA,CAAmB7C,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACb9C,OAAA,CAACL,UAAU;oBAAC+C,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,cACd,eAAAzC,OAAA;sBAAAyC,QAAA,IAAAuD,iBAAA,GAASlF,KAAK,CAACM,UAAU,cAAA4E,iBAAA,uBAAhBA,iBAAA,CAAkB9C,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,EACZhC,KAAK,CAACQ,WAAW,GAAGR,KAAK,CAACO,aAAa,iBACtCrB,OAAA,CAACF,IAAI;oBACHuC,KAAK,EAAE,IAAI,CAACvB,KAAK,CAACQ,WAAW,GAAGR,KAAK,CAACO,aAAa,EAAE6B,OAAO,CAAC,CAAC,CAAC,GAAI;oBACnE/B,KAAK,EAAC,SAAS;oBACfkF,IAAI,EAAC,OAAO;oBACZ/D,EAAE,EAAE;sBAAEiC,EAAE,EAAE;oBAAE;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF,EACAhC,KAAK,CAACQ,WAAW,GAAGR,KAAK,CAACO,aAAa,iBACtCrB,OAAA,CAACF,IAAI;oBACHuC,KAAK,EAAE,IAAI,CAACvB,KAAK,CAACO,aAAa,GAAGP,KAAK,CAACQ,WAAW,EAAE4B,OAAO,CAAC,CAAC,CAAC,GAAI;oBACnE/B,KAAK,EAAC,OAAO;oBACbkF,IAAI,EAAC,OAAO;oBACZ/D,EAAE,EAAE;sBAAEiC,EAAE,EAAE;oBAAE;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC,GAxCqCE,KAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyC5C,CAAC;YAAA,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9C,OAAA,CAACJ,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnC,QAAA,eAChBzC,OAAA,CAACH,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClBzC,OAAA,CAACL,UAAU;YAAC+C,OAAO,EAAC,WAAW;YAAC8B,YAAY;YAAA/B,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACJ,IAAI;YAAC6E,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAjC,QAAA,gBACzBzC,OAAA,CAACJ,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACI,EAAE,EAAE,CAAE;cAAAvC,QAAA,gBACvBzC,OAAA,CAACL,UAAU;gBAAC+C,OAAO,EAAC,OAAO;gBAAC8B,YAAY;gBAAA/B,QAAA,eACtCzC,OAAA;kBAAAyC,QAAA,EAAQ;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACZf,SAAS,CACPuE,MAAM,CAACxF,KAAK,IAAIA,KAAK,CAACmB,UAAU,GAAG,CAAC,CAAC,CACrCpB,GAAG,CAAC,CAACC,KAAK,EAAEkC,KAAK,kBAChBhD,OAAA,CAACN,GAAG;gBAAa4C,EAAE,EAAE;kBACnBiE,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BjE,CAAC,EAAE,CAAC;kBACJ6D,EAAE,EAAE,CAAC;kBACL5D,MAAM,EAAE,mBAAmB;kBAC3BuC,YAAY,EAAE,CAAC;kBACfmB,UAAU,EAAE,aAAahG,aAAa,CAACG,OAAO;gBAChD,CAAE;gBAAAoC,QAAA,gBACAzC,OAAA,CAACL,UAAU;kBAAC+C,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAE3B,KAAK,CAACkB;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC3D9C,OAAA,CAACF,IAAI;kBACHuC,KAAK,EAAE,IAAIvB,KAAK,CAACmB,UAAU,CAACiB,OAAO,CAAC,CAAC,CAAC,GAAI;kBAC1C/B,KAAK,EAAC,SAAS;kBACfkF,IAAI,EAAC;gBAAO;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,GAdME,KAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACP9C,OAAA,CAACJ,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACI,EAAE,EAAE,CAAE;cAAAvC,QAAA,gBACvBzC,OAAA,CAACL,UAAU;gBAAC+C,OAAO,EAAC,OAAO;gBAAC8B,YAAY;gBAAA/B,QAAA,eACtCzC,OAAA;kBAAAyC,QAAA,EAAQ;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACZf,SAAS,CACPuE,MAAM,CAACxF,KAAK,IAAIA,KAAK,CAACmB,UAAU,GAAG,CAAC,CAAC,CACrCpB,GAAG,CAAC,CAACC,KAAK,EAAEkC,KAAK,kBAChBhD,OAAA,CAACN,GAAG;gBAAa4C,EAAE,EAAE;kBACnBiE,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BjE,CAAC,EAAE,CAAC;kBACJ6D,EAAE,EAAE,CAAC;kBACL5D,MAAM,EAAE,mBAAmB;kBAC3BuC,YAAY,EAAE,CAAC;kBACfmB,UAAU,EAAE,aAAahG,aAAa,CAACM,KAAK;gBAC9C,CAAE;gBAAAiC,QAAA,gBACAzC,OAAA,CAACL,UAAU;kBAAC+C,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAE3B,KAAK,CAACkB;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC3D9C,OAAA,CAACF,IAAI;kBACHuC,KAAK,EAAE,GAAGvB,KAAK,CAACmB,UAAU,CAACiB,OAAO,CAAC,CAAC,CAAC,GAAI;kBACzC/B,KAAK,EAAC,OAAO;kBACbkF,IAAI,EAAC;gBAAO;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,GAdME,KAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC2D,EAAA,GA9SIhG,cAAc;AAgTpB,eAAeA,cAAc;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}