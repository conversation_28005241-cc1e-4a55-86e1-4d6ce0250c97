{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ReportCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Alert, CircularProgress, Link, Divider } from '@mui/material';\nimport { Assessment as AssessmentIcon, Bar<PERSON>hart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, DateRange as DateRangeIcon } from '@mui/icons-material';\nimport reportService from '../../services/reportService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportCavi = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'pdf',\n    id_bobina: '',\n    data_inizio: '',\n    data_fine: ''\n  });\n  const [downloadLink, setDownloadLink] = useState('');\n  const [reportContent, setReportContent] = useState('');\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'reportAvanzamento') {\n      setDialogType('reportAvanzamento');\n      setOpenDialog(true);\n    } else if (option === 'billOfQuantities') {\n      setDialogType('billOfQuantities');\n      setOpenDialog(true);\n    } else if (option === 'reportUtilizzoBobine') {\n      setDialogType('reportUtilizzoBobine');\n      setOpenDialog(true);\n    } else if (option === 'reportPosaPeriodo') {\n      // Imposta le date di default (ultimo mese)\n      const today = new Date();\n      const lastMonth = new Date();\n      lastMonth.setMonth(today.getMonth() - 1);\n      setFormData({\n        ...formData,\n        data_inizio: lastMonth.toISOString().split('T')[0],\n        data_fine: today.toISOString().split('T')[0]\n      });\n      setDialogType('reportPosaPeriodo');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setFormData({\n      formato: 'pdf',\n      id_bobina: '',\n      data_inizio: '',\n      data_fine: ''\n    });\n    setDownloadLink('');\n    setReportContent('');\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la generazione del report di avanzamento\n  const handleGeneraReportAvanzamento = async () => {\n    try {\n      setLoading(true);\n      if (formData.formato === 'video') {\n        const response = await reportService.getProgressReport(cantiereId, 'video');\n        setReportContent(response.content);\n      } else {\n        const response = await reportService.getProgressReport(cantiereId, formData.formato);\n        setDownloadLink(response.file_url);\n      }\n      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');\n      onSuccess('Report di avanzamento generato con successo');\n    } catch (error) {\n      onError('Errore nella generazione del report di avanzamento: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del report di avanzamento:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la generazione della distinta materiali\n  const handleGeneraBillOfQuantities = async () => {\n    try {\n      setLoading(true);\n      if (formData.formato === 'video') {\n        const response = await reportService.getBillOfQuantities(cantiereId, 'video');\n        setReportContent(response.content);\n      } else {\n        const response = await reportService.getBillOfQuantities(cantiereId, formData.formato);\n        setDownloadLink(response.file_url);\n      }\n      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');\n      onSuccess('Distinta materiali generata con successo');\n    } catch (error) {\n      onError('Errore nella generazione della distinta materiali: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione della distinta materiali:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la generazione del report utilizzo bobine\n  const handleGeneraReportUtilizzoBobine = async () => {\n    try {\n      setLoading(true);\n      if (formData.id_bobina) {\n        // Report per bobina specifica\n        if (formData.formato === 'video') {\n          const response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, 'video');\n          setReportContent(response.content);\n        } else {\n          const response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, formData.formato);\n          setDownloadLink(response.file_url);\n        }\n      } else {\n        // Report completo bobine\n        if (formData.formato === 'video') {\n          const response = await reportService.getBobineReport(cantiereId, 'video');\n          setReportContent(response.content);\n        } else {\n          const response = await reportService.getBobineReport(cantiereId, formData.formato);\n          setDownloadLink(response.file_url);\n        }\n      }\n      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');\n      onSuccess('Report utilizzo bobine generato con successo');\n    } catch (error) {\n      onError('Errore nella generazione del report utilizzo bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del report utilizzo bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la generazione del report posa per periodo\n  const handleGeneraReportPosaPeriodo = async () => {\n    try {\n      if (!formData.data_inizio || !formData.data_fine) {\n        onError('Seleziona le date di inizio e fine periodo');\n        return;\n      }\n      setLoading(true);\n      if (formData.formato === 'video') {\n        const response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, 'video');\n        setReportContent(response.content);\n      } else {\n        const response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, formData.formato);\n        setDownloadLink(response.file_url);\n      }\n      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');\n      onSuccess('Report posa per periodo generato con successo');\n    } catch (error) {\n      onError('Errore nella generazione del report posa per periodo: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del report posa per periodo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'reportAvanzamento') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Report Avanzamento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Formato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"formato\",\n                value: formData.formato,\n                onChange: handleFormChange,\n                label: \"Formato\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"pdf\",\n                  children: \"PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"excel\",\n                  children: \"Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"video\",\n                  children: \"Visualizza a schermo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleGeneraReportAvanzamento,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 69\n            }, this),\n            children: \"Genera Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'billOfQuantities') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Bill of Quantities (Distinta Materiali)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Formato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"formato\",\n                value: formData.formato,\n                onChange: handleFormChange,\n                label: \"Formato\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"pdf\",\n                  children: \"PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"excel\",\n                  children: \"Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"video\",\n                  children: \"Visualizza a schermo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleGeneraBillOfQuantities,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 69\n            }, this),\n            children: \"Genera Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'reportUtilizzoBobine') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Report Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_bobina\",\n              label: \"ID Bobina (opzionale)\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              helperText: \"Lascia vuoto per un report completo di tutte le bobine\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Formato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"formato\",\n                value: formData.formato,\n                onChange: handleFormChange,\n                label: \"Formato\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"pdf\",\n                  children: \"PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"excel\",\n                  children: \"Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"video\",\n                  children: \"Visualizza a schermo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleGeneraReportUtilizzoBobine,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(PieChartIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 69\n            }, this),\n            children: \"Genera Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'reportPosaPeriodo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Report Posa per Periodo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              name: \"data_inizio\",\n              label: \"Data Inizio\",\n              type: \"date\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.data_inizio,\n              onChange: handleFormChange,\n              InputLabelProps: {\n                shrink: true\n              },\n              sx: {\n                mb: 2\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"data_fine\",\n              label: \"Data Fine\",\n              type: \"date\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.data_fine,\n              onChange: handleFormChange,\n              InputLabelProps: {\n                shrink: true\n              },\n              sx: {\n                mb: 2\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Formato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"formato\",\n                value: formData.formato,\n                onChange: handleFormChange,\n                label: \"Formato\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"pdf\",\n                  children: \"PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"excel\",\n                  children: \"Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"video\",\n                  children: \"Visualizza a schermo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleGeneraReportPosaPeriodo,\n            disabled: loading || !formData.data_inizio || !formData.data_fine,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 69\n            }, this),\n            children: \"Genera Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'downloadReport') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Report Generato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"success\",\n              sx: {\n                mb: 2\n              },\n              children: \"Il report \\xE8 stato generato con successo.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: \"Clicca sul link sottostante per scaricare il file:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              href: downloadLink,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              download: true,\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(DownloadIcon, {\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this), \"Scarica report\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'visualizzaReport') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Visualizzazione Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2,\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                maxHeight: '60vh',\n                overflow: 'auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                style: {\n                  whiteSpace: 'pre-wrap',\n                  fontFamily: 'monospace'\n                },\n                children: reportContent\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: \"Report\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"Opzioni disponibili:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOptionSelect('reportAvanzamento'),\n            sx: {\n              justifyContent: 'flex-start',\n              textAlign: 'left',\n              py: 1.5\n            },\n            children: \"1. Report Avanzamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOptionSelect('billOfQuantities'),\n            sx: {\n              justifyContent: 'flex-start',\n              textAlign: 'left',\n              py: 1.5\n            },\n            children: \"2. Bill of Quantities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(PieChartIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOptionSelect('reportUtilizzoBobine'),\n            sx: {\n              justifyContent: 'flex-start',\n              textAlign: 'left',\n              py: 1.5\n            },\n            children: \"3. Report Utilizzo Bobine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(DateRangeIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOptionSelect('reportPosaPeriodo'),\n            sx: {\n              justifyContent: 'flex-start',\n              textAlign: 'left',\n              py: 1.5\n            },\n            children: \"4. Report Posa per Periodo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 442,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCavi, \"chklRTxL1btB8KGRw1wVIY2qaDs=\");\n_c = ReportCavi;\nexport default ReportCavi;\nvar _c;\n$RefreshReg$(_c, \"ReportCavi\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "<PERSON><PERSON>", "CircularProgress", "Link", "Divider", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "DateRange", "DateRangeIcon", "reportService", "jsxDEV", "_jsxDEV", "ReportCavi", "cantiereId", "onSuccess", "onError", "_s", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "id_bobina", "data_inizio", "data_fine", "downloadLink", "setDownloadLink", "reportContent", "set<PERSON><PERSON>ort<PERSON><PERSON>nt", "handleOptionSelect", "option", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleCloseDialog", "handleFormChange", "e", "name", "value", "target", "handleGeneraReportAvanzamento", "response", "getProgressReport", "content", "file_url", "error", "message", "console", "handleGeneraBillOfQuantities", "getBillOfQuantities", "handleGeneraReportUtilizzoBobine", "getBobinaReport", "getBobineReport", "handleGeneraReportPosaPeriodo", "getPosaPerPeriodoReport", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mt", "variant", "mb", "onChange", "label", "onClick", "disabled", "startIcon", "size", "helperText", "type", "InputLabelProps", "shrink", "required", "severity", "gutterBottom", "href", "rel", "download", "display", "alignItems", "mr", "p", "maxHeight", "overflow", "style", "whiteSpace", "fontFamily", "container", "spacing", "item", "xs", "sm", "md", "justifyContent", "textAlign", "py", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ReportCavi.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Alert,\n  CircularProgress,\n  Link,\n  Divider\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  Bar<PERSON>hart as BarChartIcon,\n  PieChart as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  DateRange as DateRangeIcon\n} from '@mui/icons-material';\nimport reportService from '../../services/reportService';\n\nconst ReportCavi = ({ cantiereId, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'pdf',\n    id_bobina: '',\n    data_inizio: '',\n    data_fine: ''\n  });\n  const [downloadLink, setDownloadLink] = useState('');\n  const [reportContent, setReportContent] = useState('');\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n    \n    if (option === 'reportAvanzamento') {\n      setDialogType('reportAvanzamento');\n      setOpenDialog(true);\n    } else if (option === 'billOfQuantities') {\n      setDialogType('billOfQuantities');\n      setOpenDialog(true);\n    } else if (option === 'reportUtilizzoBobine') {\n      setDialogType('reportUtilizzoBobine');\n      setOpenDialog(true);\n    } else if (option === 'reportPosaPeriodo') {\n      // Imposta le date di default (ultimo mese)\n      const today = new Date();\n      const lastMonth = new Date();\n      lastMonth.setMonth(today.getMonth() - 1);\n      \n      setFormData({\n        ...formData,\n        data_inizio: lastMonth.toISOString().split('T')[0],\n        data_fine: today.toISOString().split('T')[0]\n      });\n      \n      setDialogType('reportPosaPeriodo');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setFormData({\n      formato: 'pdf',\n      id_bobina: '',\n      data_inizio: '',\n      data_fine: ''\n    });\n    setDownloadLink('');\n    setReportContent('');\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la generazione del report di avanzamento\n  const handleGeneraReportAvanzamento = async () => {\n    try {\n      setLoading(true);\n      \n      if (formData.formato === 'video') {\n        const response = await reportService.getProgressReport(cantiereId, 'video');\n        setReportContent(response.content);\n      } else {\n        const response = await reportService.getProgressReport(cantiereId, formData.formato);\n        setDownloadLink(response.file_url);\n      }\n      \n      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');\n      onSuccess('Report di avanzamento generato con successo');\n    } catch (error) {\n      onError('Errore nella generazione del report di avanzamento: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del report di avanzamento:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la generazione della distinta materiali\n  const handleGeneraBillOfQuantities = async () => {\n    try {\n      setLoading(true);\n      \n      if (formData.formato === 'video') {\n        const response = await reportService.getBillOfQuantities(cantiereId, 'video');\n        setReportContent(response.content);\n      } else {\n        const response = await reportService.getBillOfQuantities(cantiereId, formData.formato);\n        setDownloadLink(response.file_url);\n      }\n      \n      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');\n      onSuccess('Distinta materiali generata con successo');\n    } catch (error) {\n      onError('Errore nella generazione della distinta materiali: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione della distinta materiali:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la generazione del report utilizzo bobine\n  const handleGeneraReportUtilizzoBobine = async () => {\n    try {\n      setLoading(true);\n      \n      if (formData.id_bobina) {\n        // Report per bobina specifica\n        if (formData.formato === 'video') {\n          const response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, 'video');\n          setReportContent(response.content);\n        } else {\n          const response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, formData.formato);\n          setDownloadLink(response.file_url);\n        }\n      } else {\n        // Report completo bobine\n        if (formData.formato === 'video') {\n          const response = await reportService.getBobineReport(cantiereId, 'video');\n          setReportContent(response.content);\n        } else {\n          const response = await reportService.getBobineReport(cantiereId, formData.formato);\n          setDownloadLink(response.file_url);\n        }\n      }\n      \n      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');\n      onSuccess('Report utilizzo bobine generato con successo');\n    } catch (error) {\n      onError('Errore nella generazione del report utilizzo bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del report utilizzo bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la generazione del report posa per periodo\n  const handleGeneraReportPosaPeriodo = async () => {\n    try {\n      if (!formData.data_inizio || !formData.data_fine) {\n        onError('Seleziona le date di inizio e fine periodo');\n        return;\n      }\n      \n      setLoading(true);\n      \n      if (formData.formato === 'video') {\n        const response = await reportService.getPosaPerPeriodoReport(\n          cantiereId, \n          formData.data_inizio, \n          formData.data_fine, \n          'video'\n        );\n        setReportContent(response.content);\n      } else {\n        const response = await reportService.getPosaPerPeriodoReport(\n          cantiereId, \n          formData.data_inizio, \n          formData.data_fine, \n          formData.formato\n        );\n        setDownloadLink(response.file_url);\n      }\n      \n      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');\n      onSuccess('Report posa per periodo generato con successo');\n    } catch (error) {\n      onError('Errore nella generazione del report posa per periodo: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del report posa per periodo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'reportAvanzamento') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Report Avanzamento</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <FormControl fullWidth variant=\"outlined\" sx={{ mb: 2 }}>\n                <InputLabel>Formato</InputLabel>\n                <Select\n                  name=\"formato\"\n                  value={formData.formato}\n                  onChange={handleFormChange}\n                  label=\"Formato\"\n                >\n                  <MenuItem value=\"pdf\">PDF</MenuItem>\n                  <MenuItem value=\"excel\">Excel</MenuItem>\n                  <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                </Select>\n              </FormControl>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button \n              onClick={handleGeneraReportAvanzamento} \n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <AssessmentIcon />}\n            >\n              Genera Report\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'billOfQuantities') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Bill of Quantities (Distinta Materiali)</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <FormControl fullWidth variant=\"outlined\" sx={{ mb: 2 }}>\n                <InputLabel>Formato</InputLabel>\n                <Select\n                  name=\"formato\"\n                  value={formData.formato}\n                  onChange={handleFormChange}\n                  label=\"Formato\"\n                >\n                  <MenuItem value=\"pdf\">PDF</MenuItem>\n                  <MenuItem value=\"excel\">Excel</MenuItem>\n                  <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                </Select>\n              </FormControl>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button \n              onClick={handleGeneraBillOfQuantities} \n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <ListIcon />}\n            >\n              Genera Report\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'reportUtilizzoBobine') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Report Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <TextField\n                name=\"id_bobina\"\n                label=\"ID Bobina (opzionale)\"\n                fullWidth\n                variant=\"outlined\"\n                value={formData.id_bobina}\n                onChange={handleFormChange}\n                helperText=\"Lascia vuoto per un report completo di tutte le bobine\"\n                sx={{ mb: 2 }}\n              />\n              \n              <FormControl fullWidth variant=\"outlined\" sx={{ mb: 2 }}>\n                <InputLabel>Formato</InputLabel>\n                <Select\n                  name=\"formato\"\n                  value={formData.formato}\n                  onChange={handleFormChange}\n                  label=\"Formato\"\n                >\n                  <MenuItem value=\"pdf\">PDF</MenuItem>\n                  <MenuItem value=\"excel\">Excel</MenuItem>\n                  <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                </Select>\n              </FormControl>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button \n              onClick={handleGeneraReportUtilizzoBobine} \n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <PieChartIcon />}\n            >\n              Genera Report\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'reportPosaPeriodo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Report Posa per Periodo</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <TextField\n                name=\"data_inizio\"\n                label=\"Data Inizio\"\n                type=\"date\"\n                fullWidth\n                variant=\"outlined\"\n                value={formData.data_inizio}\n                onChange={handleFormChange}\n                InputLabelProps={{ shrink: true }}\n                sx={{ mb: 2 }}\n                required\n              />\n              \n              <TextField\n                name=\"data_fine\"\n                label=\"Data Fine\"\n                type=\"date\"\n                fullWidth\n                variant=\"outlined\"\n                value={formData.data_fine}\n                onChange={handleFormChange}\n                InputLabelProps={{ shrink: true }}\n                sx={{ mb: 2 }}\n                required\n              />\n              \n              <FormControl fullWidth variant=\"outlined\" sx={{ mb: 2 }}>\n                <InputLabel>Formato</InputLabel>\n                <Select\n                  name=\"formato\"\n                  value={formData.formato}\n                  onChange={handleFormChange}\n                  label=\"Formato\"\n                >\n                  <MenuItem value=\"pdf\">PDF</MenuItem>\n                  <MenuItem value=\"excel\">Excel</MenuItem>\n                  <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                </Select>\n              </FormControl>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button \n              onClick={handleGeneraReportPosaPeriodo} \n              disabled={loading || !formData.data_inizio || !formData.data_fine}\n              startIcon={loading ? <CircularProgress size={20} /> : <TimelineIcon />}\n            >\n              Genera Report\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'downloadReport') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Report Generato</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <Alert severity=\"success\" sx={{ mb: 2 }}>\n                Il report è stato generato con successo.\n              </Alert>\n              <Typography variant=\"body1\" gutterBottom>\n                Clicca sul link sottostante per scaricare il file:\n              </Typography>\n              <Link \n                href={downloadLink} \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                download\n                sx={{ display: 'flex', alignItems: 'center', mt: 1 }}\n              >\n                <DownloadIcon sx={{ mr: 1 }} />\n                Scarica report\n              </Link>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaReport') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Visualizzazione Report</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2, mb: 2 }}>\n              <Paper sx={{ p: 2, maxHeight: '60vh', overflow: 'auto' }}>\n                <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>\n                  {reportContent}\n                </pre>\n              </Paper>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n    \n    return null;\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h5\" gutterBottom>\n        Report\n      </Typography>\n      \n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Typography variant=\"subtitle1\" gutterBottom>\n          Opzioni disponibili:\n        </Typography>\n        \n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              startIcon={<AssessmentIcon />}\n              onClick={() => handleOptionSelect('reportAvanzamento')}\n              sx={{ justifyContent: 'flex-start', textAlign: 'left', py: 1.5 }}\n            >\n              1. Report Avanzamento\n            </Button>\n          </Grid>\n          \n          <Grid item xs={12} sm={6} md={3}>\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              startIcon={<ListIcon />}\n              onClick={() => handleOptionSelect('billOfQuantities')}\n              sx={{ justifyContent: 'flex-start', textAlign: 'left', py: 1.5 }}\n            >\n              2. Bill of Quantities\n            </Button>\n          </Grid>\n          \n          <Grid item xs={12} sm={6} md={3}>\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              startIcon={<PieChartIcon />}\n              onClick={() => handleOptionSelect('reportUtilizzoBobine')}\n              sx={{ justifyContent: 'flex-start', textAlign: 'left', py: 1.5 }}\n            >\n              3. Report Utilizzo Bobine\n            </Button>\n          </Grid>\n          \n          <Grid item xs={12} sm={6} md={3}>\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              startIcon={<DateRangeIcon />}\n              onClick={() => handleOptionSelect('reportPosaPeriodo')}\n              sx={{ justifyContent: 'flex-start', textAlign: 'left', py: 1.5 }}\n            >\n              4. Report Posa per Periodo\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n      \n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+C,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC;IACvCmD,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM2D,kBAAkB,GAAIC,MAAM,IAAK;IACrChB,iBAAiB,CAACgB,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,mBAAmB,EAAE;MAClCZ,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIc,MAAM,KAAK,kBAAkB,EAAE;MACxCZ,aAAa,CAAC,kBAAkB,CAAC;MACjCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIc,MAAM,KAAK,sBAAsB,EAAE;MAC5CZ,aAAa,CAAC,sBAAsB,CAAC;MACrCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIc,MAAM,KAAK,mBAAmB,EAAE;MACzC;MACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;MAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAExCf,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,WAAW,EAAEU,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDb,SAAS,EAAEO,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC;MAEFnB,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BtB,aAAa,CAAC,KAAK,CAAC;IACpBI,WAAW,CAAC;MACVC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,eAAe,CAAC,EAAE,CAAC;IACnBE,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCvB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACsB,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD,IAAI;MACFhC,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,QAAQ,CAACE,OAAO,KAAK,OAAO,EAAE;QAChC,MAAMwB,QAAQ,GAAG,MAAM1C,aAAa,CAAC2C,iBAAiB,CAACvC,UAAU,EAAE,OAAO,CAAC;QAC3EqB,gBAAgB,CAACiB,QAAQ,CAACE,OAAO,CAAC;MACpC,CAAC,MAAM;QACL,MAAMF,QAAQ,GAAG,MAAM1C,aAAa,CAAC2C,iBAAiB,CAACvC,UAAU,EAAEY,QAAQ,CAACE,OAAO,CAAC;QACpFK,eAAe,CAACmB,QAAQ,CAACG,QAAQ,CAAC;MACpC;MAEA9B,aAAa,CAACC,QAAQ,CAACE,OAAO,KAAK,OAAO,GAAG,kBAAkB,GAAG,gBAAgB,CAAC;MACnFb,SAAS,CAAC,6CAA6C,CAAC;IAC1D,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdxC,OAAO,CAAC,sDAAsD,IAAIwC,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACzGC,OAAO,CAACF,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;IAC7E,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwC,4BAA4B,GAAG,MAAAA,CAAA,KAAY;IAC/C,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,QAAQ,CAACE,OAAO,KAAK,OAAO,EAAE;QAChC,MAAMwB,QAAQ,GAAG,MAAM1C,aAAa,CAACkD,mBAAmB,CAAC9C,UAAU,EAAE,OAAO,CAAC;QAC7EqB,gBAAgB,CAACiB,QAAQ,CAACE,OAAO,CAAC;MACpC,CAAC,MAAM;QACL,MAAMF,QAAQ,GAAG,MAAM1C,aAAa,CAACkD,mBAAmB,CAAC9C,UAAU,EAAEY,QAAQ,CAACE,OAAO,CAAC;QACtFK,eAAe,CAACmB,QAAQ,CAACG,QAAQ,CAAC;MACpC;MAEA9B,aAAa,CAACC,QAAQ,CAACE,OAAO,KAAK,OAAO,GAAG,kBAAkB,GAAG,gBAAgB,CAAC;MACnFb,SAAS,CAAC,0CAA0C,CAAC;IACvD,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdxC,OAAO,CAAC,qDAAqD,IAAIwC,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACxGC,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;IAC5E,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0C,gCAAgC,GAAG,MAAAA,CAAA,KAAY;IACnD,IAAI;MACF1C,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,QAAQ,CAACG,SAAS,EAAE;QACtB;QACA,IAAIH,QAAQ,CAACE,OAAO,KAAK,OAAO,EAAE;UAChC,MAAMwB,QAAQ,GAAG,MAAM1C,aAAa,CAACoD,eAAe,CAAChD,UAAU,EAAEY,QAAQ,CAACG,SAAS,EAAE,OAAO,CAAC;UAC7FM,gBAAgB,CAACiB,QAAQ,CAACE,OAAO,CAAC;QACpC,CAAC,MAAM;UACL,MAAMF,QAAQ,GAAG,MAAM1C,aAAa,CAACoD,eAAe,CAAChD,UAAU,EAAEY,QAAQ,CAACG,SAAS,EAAEH,QAAQ,CAACE,OAAO,CAAC;UACtGK,eAAe,CAACmB,QAAQ,CAACG,QAAQ,CAAC;QACpC;MACF,CAAC,MAAM;QACL;QACA,IAAI7B,QAAQ,CAACE,OAAO,KAAK,OAAO,EAAE;UAChC,MAAMwB,QAAQ,GAAG,MAAM1C,aAAa,CAACqD,eAAe,CAACjD,UAAU,EAAE,OAAO,CAAC;UACzEqB,gBAAgB,CAACiB,QAAQ,CAACE,OAAO,CAAC;QACpC,CAAC,MAAM;UACL,MAAMF,QAAQ,GAAG,MAAM1C,aAAa,CAACqD,eAAe,CAACjD,UAAU,EAAEY,QAAQ,CAACE,OAAO,CAAC;UAClFK,eAAe,CAACmB,QAAQ,CAACG,QAAQ,CAAC;QACpC;MACF;MAEA9B,aAAa,CAACC,QAAQ,CAACE,OAAO,KAAK,OAAO,GAAG,kBAAkB,GAAG,gBAAgB,CAAC;MACnFb,SAAS,CAAC,8CAA8C,CAAC;IAC3D,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdxC,OAAO,CAAC,uDAAuD,IAAIwC,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAC1GC,OAAO,CAACF,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;IAC9E,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6C,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD,IAAI;MACF,IAAI,CAACtC,QAAQ,CAACI,WAAW,IAAI,CAACJ,QAAQ,CAACK,SAAS,EAAE;QAChDf,OAAO,CAAC,4CAA4C,CAAC;QACrD;MACF;MAEAG,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,QAAQ,CAACE,OAAO,KAAK,OAAO,EAAE;QAChC,MAAMwB,QAAQ,GAAG,MAAM1C,aAAa,CAACuD,uBAAuB,CAC1DnD,UAAU,EACVY,QAAQ,CAACI,WAAW,EACpBJ,QAAQ,CAACK,SAAS,EAClB,OACF,CAAC;QACDI,gBAAgB,CAACiB,QAAQ,CAACE,OAAO,CAAC;MACpC,CAAC,MAAM;QACL,MAAMF,QAAQ,GAAG,MAAM1C,aAAa,CAACuD,uBAAuB,CAC1DnD,UAAU,EACVY,QAAQ,CAACI,WAAW,EACpBJ,QAAQ,CAACK,SAAS,EAClBL,QAAQ,CAACE,OACX,CAAC;QACDK,eAAe,CAACmB,QAAQ,CAACG,QAAQ,CAAC;MACpC;MAEA9B,aAAa,CAACC,QAAQ,CAACE,OAAO,KAAK,OAAO,GAAG,kBAAkB,GAAG,gBAAgB,CAAC;MACnFb,SAAS,CAAC,+CAA+C,CAAC;IAC5D,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdxC,OAAO,CAAC,wDAAwD,IAAIwC,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAC3GC,OAAO,CAACF,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;IAC/E,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+C,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI1C,UAAU,KAAK,mBAAmB,EAAE;MACtC,oBACEZ,OAAA,CAAC7B,MAAM;QAACoF,IAAI,EAAE7C,UAAW;QAAC8C,OAAO,EAAEvB,iBAAkB;QAACwB,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3D,OAAA,CAAC5B,WAAW;UAAAuF,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC7C/D,OAAA,CAAC3B,aAAa;UAAAsF,QAAA,eACZ3D,OAAA,CAAClC,GAAG;YAACkG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,eACjB3D,OAAA,CAACzB,WAAW;cAACmF,SAAS;cAACQ,OAAO,EAAC,UAAU;cAACF,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,gBACtD3D,OAAA,CAACxB,UAAU;gBAAAmF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChC/D,OAAA,CAACvB,MAAM;gBACL2D,IAAI,EAAC,SAAS;gBACdC,KAAK,EAAEvB,QAAQ,CAACE,OAAQ;gBACxBoD,QAAQ,EAAElC,gBAAiB;gBAC3BmC,KAAK,EAAC,SAAS;gBAAAV,QAAA,gBAEf3D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,KAAK;kBAAAsB,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpC/D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,OAAO;kBAAAsB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxC/D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,OAAO;kBAAAsB,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB/D,OAAA,CAAC1B,aAAa;UAAAqF,QAAA,gBACZ3D,OAAA,CAAChC,MAAM;YAACsG,OAAO,EAAErC,iBAAkB;YAAA0B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD/D,OAAA,CAAChC,MAAM;YACLsG,OAAO,EAAE/B,6BAA8B;YACvCgC,QAAQ,EAAEjE,OAAQ;YAClBkE,SAAS,EAAElE,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAAC4F,IAAI,EAAE;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/D,OAAA,CAACf,cAAc;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAC1E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInD,UAAU,KAAK,kBAAkB,EAAE;MAC5C,oBACEZ,OAAA,CAAC7B,MAAM;QAACoF,IAAI,EAAE7C,UAAW;QAAC8C,OAAO,EAAEvB,iBAAkB;QAACwB,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3D,OAAA,CAAC5B,WAAW;UAAAuF,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClE/D,OAAA,CAAC3B,aAAa;UAAAsF,QAAA,eACZ3D,OAAA,CAAClC,GAAG;YAACkG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,eACjB3D,OAAA,CAACzB,WAAW;cAACmF,SAAS;cAACQ,OAAO,EAAC,UAAU;cAACF,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,gBACtD3D,OAAA,CAACxB,UAAU;gBAAAmF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChC/D,OAAA,CAACvB,MAAM;gBACL2D,IAAI,EAAC,SAAS;gBACdC,KAAK,EAAEvB,QAAQ,CAACE,OAAQ;gBACxBoD,QAAQ,EAAElC,gBAAiB;gBAC3BmC,KAAK,EAAC,SAAS;gBAAAV,QAAA,gBAEf3D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,KAAK;kBAAAsB,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpC/D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,OAAO;kBAAAsB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxC/D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,OAAO;kBAAAsB,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB/D,OAAA,CAAC1B,aAAa;UAAAqF,QAAA,gBACZ3D,OAAA,CAAChC,MAAM;YAACsG,OAAO,EAAErC,iBAAkB;YAAA0B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD/D,OAAA,CAAChC,MAAM;YACLsG,OAAO,EAAEvB,4BAA6B;YACtCwB,QAAQ,EAAEjE,OAAQ;YAClBkE,SAAS,EAAElE,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAAC4F,IAAI,EAAE;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/D,OAAA,CAACP,QAAQ;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInD,UAAU,KAAK,sBAAsB,EAAE;MAChD,oBACEZ,OAAA,CAAC7B,MAAM;QAACoF,IAAI,EAAE7C,UAAW;QAAC8C,OAAO,EAAEvB,iBAAkB;QAACwB,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3D,OAAA,CAAC5B,WAAW;UAAAuF,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjD/D,OAAA,CAAC3B,aAAa;UAAAsF,QAAA,eACZ3D,OAAA,CAAClC,GAAG;YAACkG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACjB3D,OAAA,CAACrB,SAAS;cACRyD,IAAI,EAAC,WAAW;cAChBiC,KAAK,EAAC,uBAAuB;cAC7BX,SAAS;cACTQ,OAAO,EAAC,UAAU;cAClB7B,KAAK,EAAEvB,QAAQ,CAACG,SAAU;cAC1BmD,QAAQ,EAAElC,gBAAiB;cAC3BwC,UAAU,EAAC,wDAAwD;cACnEV,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF/D,OAAA,CAACzB,WAAW;cAACmF,SAAS;cAACQ,OAAO,EAAC,UAAU;cAACF,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,gBACtD3D,OAAA,CAACxB,UAAU;gBAAAmF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChC/D,OAAA,CAACvB,MAAM;gBACL2D,IAAI,EAAC,SAAS;gBACdC,KAAK,EAAEvB,QAAQ,CAACE,OAAQ;gBACxBoD,QAAQ,EAAElC,gBAAiB;gBAC3BmC,KAAK,EAAC,SAAS;gBAAAV,QAAA,gBAEf3D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,KAAK;kBAAAsB,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpC/D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,OAAO;kBAAAsB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxC/D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,OAAO;kBAAAsB,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB/D,OAAA,CAAC1B,aAAa;UAAAqF,QAAA,gBACZ3D,OAAA,CAAChC,MAAM;YAACsG,OAAO,EAAErC,iBAAkB;YAAA0B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD/D,OAAA,CAAChC,MAAM;YACLsG,OAAO,EAAErB,gCAAiC;YAC1CsB,QAAQ,EAAEjE,OAAQ;YAClBkE,SAAS,EAAElE,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAAC4F,IAAI,EAAE;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/D,OAAA,CAACX,YAAY;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACxE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInD,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACEZ,OAAA,CAAC7B,MAAM;QAACoF,IAAI,EAAE7C,UAAW;QAAC8C,OAAO,EAAEvB,iBAAkB;QAACwB,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3D,OAAA,CAAC5B,WAAW;UAAAuF,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClD/D,OAAA,CAAC3B,aAAa;UAAAsF,QAAA,eACZ3D,OAAA,CAAClC,GAAG;YAACkG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACjB3D,OAAA,CAACrB,SAAS;cACRyD,IAAI,EAAC,aAAa;cAClBiC,KAAK,EAAC,aAAa;cACnBM,IAAI,EAAC,MAAM;cACXjB,SAAS;cACTQ,OAAO,EAAC,UAAU;cAClB7B,KAAK,EAAEvB,QAAQ,CAACI,WAAY;cAC5BkD,QAAQ,EAAElC,gBAAiB;cAC3B0C,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK,CAAE;cAClCb,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cACdW,QAAQ;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEF/D,OAAA,CAACrB,SAAS;cACRyD,IAAI,EAAC,WAAW;cAChBiC,KAAK,EAAC,WAAW;cACjBM,IAAI,EAAC,MAAM;cACXjB,SAAS;cACTQ,OAAO,EAAC,UAAU;cAClB7B,KAAK,EAAEvB,QAAQ,CAACK,SAAU;cAC1BiD,QAAQ,EAAElC,gBAAiB;cAC3B0C,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK,CAAE;cAClCb,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cACdW,QAAQ;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEF/D,OAAA,CAACzB,WAAW;cAACmF,SAAS;cAACQ,OAAO,EAAC,UAAU;cAACF,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,gBACtD3D,OAAA,CAACxB,UAAU;gBAAAmF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChC/D,OAAA,CAACvB,MAAM;gBACL2D,IAAI,EAAC,SAAS;gBACdC,KAAK,EAAEvB,QAAQ,CAACE,OAAQ;gBACxBoD,QAAQ,EAAElC,gBAAiB;gBAC3BmC,KAAK,EAAC,SAAS;gBAAAV,QAAA,gBAEf3D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,KAAK;kBAAAsB,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpC/D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,OAAO;kBAAAsB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxC/D,OAAA,CAACtB,QAAQ;kBAAC2D,KAAK,EAAC,OAAO;kBAAAsB,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB/D,OAAA,CAAC1B,aAAa;UAAAqF,QAAA,gBACZ3D,OAAA,CAAChC,MAAM;YAACsG,OAAO,EAAErC,iBAAkB;YAAA0B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD/D,OAAA,CAAChC,MAAM;YACLsG,OAAO,EAAElB,6BAA8B;YACvCmB,QAAQ,EAAEjE,OAAO,IAAI,CAACQ,QAAQ,CAACI,WAAW,IAAI,CAACJ,QAAQ,CAACK,SAAU;YAClEqD,SAAS,EAAElE,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAAC4F,IAAI,EAAE;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/D,OAAA,CAACT,YAAY;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACxE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInD,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACEZ,OAAA,CAAC7B,MAAM;QAACoF,IAAI,EAAE7C,UAAW;QAAC8C,OAAO,EAAEvB,iBAAkB;QAACwB,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3D,OAAA,CAAC5B,WAAW;UAAAuF,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1C/D,OAAA,CAAC3B,aAAa;UAAAsF,QAAA,eACZ3D,OAAA,CAAClC,GAAG;YAACkG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACjB3D,OAAA,CAACpB,KAAK;cAACmG,QAAQ,EAAC,SAAS;cAACf,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,EAAC;YAEzC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAACjC,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACc,YAAY;cAAArB,QAAA,EAAC;YAEzC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/D,OAAA,CAAClB,IAAI;cACHmG,IAAI,EAAE7D,YAAa;cACnBkB,MAAM,EAAC,QAAQ;cACf4C,GAAG,EAAC,qBAAqB;cACzBC,QAAQ;cACRnB,EAAE,EAAE;gBAAEoB,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEpB,EAAE,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAErD3D,OAAA,CAACL,YAAY;gBAACqE,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB/D,OAAA,CAAC1B,aAAa;UAAAqF,QAAA,eACZ3D,OAAA,CAAChC,MAAM;YAACsG,OAAO,EAAErC,iBAAkB;YAAA0B,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInD,UAAU,KAAK,kBAAkB,EAAE;MAC5C,oBACEZ,OAAA,CAAC7B,MAAM;QAACoF,IAAI,EAAE7C,UAAW;QAAC8C,OAAO,EAAEvB,iBAAkB;QAACwB,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3D,OAAA,CAAC5B,WAAW;UAAAuF,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjD/D,OAAA,CAAC3B,aAAa;UAAAsF,QAAA,eACZ3D,OAAA,CAAClC,GAAG;YAACkG,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,eACxB3D,OAAA,CAAC/B,KAAK;cAAC+F,EAAE,EAAE;gBAAEuB,CAAC,EAAE,CAAC;gBAAEC,SAAS,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA9B,QAAA,eACvD3D,OAAA;gBAAK0F,KAAK,EAAE;kBAAEC,UAAU,EAAE,UAAU;kBAAEC,UAAU,EAAE;gBAAY,CAAE;gBAAAjC,QAAA,EAC7DrC;cAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB/D,OAAA,CAAC1B,aAAa;UAAAqF,QAAA,eACZ3D,OAAA,CAAChC,MAAM;YAACsG,OAAO,EAAErC,iBAAkB;YAAA0B,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACE/D,OAAA,CAAClC,GAAG;IAAA6F,QAAA,gBACF3D,OAAA,CAACjC,UAAU;MAACmG,OAAO,EAAC,IAAI;MAACc,YAAY;MAAArB,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb/D,OAAA,CAAC/B,KAAK;MAAC+F,EAAE,EAAE;QAAEuB,CAAC,EAAE,CAAC;QAAEpB,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACzB3D,OAAA,CAACjC,UAAU;QAACmG,OAAO,EAAC,WAAW;QAACc,YAAY;QAAArB,QAAA,EAAC;MAE7C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/D,OAAA,CAAC9B,IAAI;QAAC2H,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAnC,QAAA,gBACzB3D,OAAA,CAAC9B,IAAI;UAAC6H,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAC9B3D,OAAA,CAAChC,MAAM;YACL0F,SAAS;YACTQ,OAAO,EAAC,UAAU;YAClBM,SAAS,eAAExE,OAAA,CAACf,cAAc;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BO,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAAC,mBAAmB,CAAE;YACvDwC,EAAE,EAAE;cAAEmC,cAAc,EAAE,YAAY;cAAEC,SAAS,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAI,CAAE;YAAA1C,QAAA,EAClE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEP/D,OAAA,CAAC9B,IAAI;UAAC6H,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAC9B3D,OAAA,CAAChC,MAAM;YACL0F,SAAS;YACTQ,OAAO,EAAC,UAAU;YAClBM,SAAS,eAAExE,OAAA,CAACP,QAAQ;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxBO,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAAC,kBAAkB,CAAE;YACtDwC,EAAE,EAAE;cAAEmC,cAAc,EAAE,YAAY;cAAEC,SAAS,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAI,CAAE;YAAA1C,QAAA,EAClE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEP/D,OAAA,CAAC9B,IAAI;UAAC6H,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAC9B3D,OAAA,CAAChC,MAAM;YACL0F,SAAS;YACTQ,OAAO,EAAC,UAAU;YAClBM,SAAS,eAAExE,OAAA,CAACX,YAAY;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BO,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAAC,sBAAsB,CAAE;YAC1DwC,EAAE,EAAE;cAAEmC,cAAc,EAAE,YAAY;cAAEC,SAAS,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAI,CAAE;YAAA1C,QAAA,EAClE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEP/D,OAAA,CAAC9B,IAAI;UAAC6H,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAC9B3D,OAAA,CAAChC,MAAM;YACL0F,SAAS;YACTQ,OAAO,EAAC,UAAU;YAClBM,SAAS,eAAExE,OAAA,CAACH,aAAa;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BO,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAAC,mBAAmB,CAAE;YACvDwC,EAAE,EAAE;cAAEmC,cAAc,EAAE,YAAY;cAAEC,SAAS,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAI,CAAE;YAAA1C,QAAA,EAClE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEPT,YAAY,CAAC,CAAC;EAAA;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAzdIJ,UAAU;AAAAqG,EAAA,GAAVrG,UAAU;AA2dhB,eAAeA,UAAU;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}