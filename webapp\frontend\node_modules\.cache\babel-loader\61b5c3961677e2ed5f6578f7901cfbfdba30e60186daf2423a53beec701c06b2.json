{"ast": null, "code": "import { constructFrom } from \"./constructFrom.mjs\";\nimport { getDaysInMonth } from \"./getDaysInMonth.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param month - The month index to set (0-11)\n *\n * @returns The new date with the month set\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\nexport function setMonth(date, month) {\n  const _date = toDate(date);\n  const year = _date.getFullYear();\n  const day = _date.getDate();\n  const dateWithDesiredMonth = constructFrom(date, 0);\n  dateWithDesiredMonth.setFullYear(year, month, 15);\n  dateWithDesiredMonth.setHours(0, 0, 0, 0);\n  const daysInMonth = getDaysInMonth(dateWithDesiredMonth);\n  // Set the last day of the new month\n  // if the original date was the last day of the longer month\n  _date.setMonth(month, Math.min(day, daysInMonth));\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMonth;", "map": {"version": 3, "names": ["constructFrom", "getDaysInMonth", "toDate", "setMonth", "date", "month", "_date", "year", "getFullYear", "day", "getDate", "date<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setFullYear", "setHours", "daysInMonth", "Math", "min"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/setMonth.mjs"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.mjs\";\nimport { getDaysInMonth } from \"./getDaysInMonth.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param month - The month index to set (0-11)\n *\n * @returns The new date with the month set\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\nexport function setMonth(date, month) {\n  const _date = toDate(date);\n  const year = _date.getFullYear();\n  const day = _date.getDate();\n\n  const dateWithDesiredMonth = constructFrom(date, 0);\n  dateWithDesiredMonth.setFullYear(year, month, 15);\n  dateWithDesiredMonth.setHours(0, 0, 0, 0);\n  const daysInMonth = getDaysInMonth(dateWithDesiredMonth);\n  // Set the last day of the new month\n  // if the original date was the last day of the longer month\n  _date.setMonth(month, Math.min(day, daysInMonth));\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMonth;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACpC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAMG,IAAI,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;EAChC,MAAMC,GAAG,GAAGH,KAAK,CAACI,OAAO,CAAC,CAAC;EAE3B,MAAMC,oBAAoB,GAAGX,aAAa,CAACI,IAAI,EAAE,CAAC,CAAC;EACnDO,oBAAoB,CAACC,WAAW,CAACL,IAAI,EAAEF,KAAK,EAAE,EAAE,CAAC;EACjDM,oBAAoB,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,MAAMC,WAAW,GAAGb,cAAc,CAACU,oBAAoB,CAAC;EACxD;EACA;EACAL,KAAK,CAACH,QAAQ,CAACE,KAAK,EAAEU,IAAI,CAACC,GAAG,CAACP,GAAG,EAAEK,WAAW,CAAC,CAAC;EACjD,OAAOR,KAAK;AACd;;AAEA;AACA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}