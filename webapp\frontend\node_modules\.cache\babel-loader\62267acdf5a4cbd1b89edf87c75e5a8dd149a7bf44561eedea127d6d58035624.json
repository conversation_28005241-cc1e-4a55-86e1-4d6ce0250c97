{"ast": null, "code": "import { createIsAfterIgnoreDatePart } from \"./time-utils.js\";\nimport { mergeDateAndTime, getTodayDate } from \"./date-utils.js\";\nexport const SECTION_TYPE_GRANULARITY = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hours: 4,\n  minutes: 5,\n  seconds: 6,\n  milliseconds: 7\n};\nexport const getSectionTypeGranularity = sections => Math.max(...sections.map(section => SECTION_TYPE_GRANULARITY[section.type] ?? 1));\nconst roundDate = (utils, granularity, date) => {\n  if (granularity === SECTION_TYPE_GRANULARITY.year) {\n    return utils.startOfYear(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.month) {\n    return utils.startOfMonth(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.day) {\n    return utils.startOfDay(date);\n  }\n\n  // We don't have startOfHour / startOfMinute / startOfSecond\n  let roundedDate = date;\n  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {\n    roundedDate = utils.setMinutes(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {\n    roundedDate = utils.setSeconds(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {\n    roundedDate = utils.setMilliseconds(roundedDate, 0);\n  }\n  return roundedDate;\n};\nexport const getDefaultReferenceDate = ({\n  props,\n  utils,\n  granularity,\n  timezone,\n  getTodayDate: inGetTodayDate\n}) => {\n  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(utils, granularity, getTodayDate(utils, timezone));\n  if (props.minDate != null && utils.isAfterDay(props.minDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.minDate);\n  }\n  if (props.maxDate != null && utils.isBeforeDay(props.maxDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.maxDate);\n  }\n  const isAfter = createIsAfterIgnoreDatePart(props.disableIgnoringDatePartForTimeValidation ?? false, utils);\n  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : mergeDateAndTime(utils, referenceDate, props.minTime));\n  }\n  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : mergeDateAndTime(utils, referenceDate, props.maxTime));\n  }\n  return referenceDate;\n};", "map": {"version": 3, "names": ["createIsAfterIgnoreDatePart", "mergeDateAndTime", "getTodayDate", "SECTION_TYPE_GRANULARITY", "year", "month", "day", "hours", "minutes", "seconds", "milliseconds", "getSectionTypeGranularity", "sections", "Math", "max", "map", "section", "type", "roundDate", "utils", "granularity", "date", "startOfYear", "startOfMonth", "startOfDay", "roundedDate", "setMinutes", "setSeconds", "setMilliseconds", "getDefaultReferenceDate", "props", "timezone", "inGetTodayDate", "referenceDate", "minDate", "isAfterDay", "maxDate", "isBeforeDay", "isAfter", "disableIgnoringDatePartForTimeValidation", "minTime", "maxTime"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/utils/getDefaultReferenceDate.js"], "sourcesContent": ["import { createIsAfterIgnoreDatePart } from \"./time-utils.js\";\nimport { mergeDateAndTime, getTodayDate } from \"./date-utils.js\";\nexport const SECTION_TYPE_GRANULARITY = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hours: 4,\n  minutes: 5,\n  seconds: 6,\n  milliseconds: 7\n};\nexport const getSectionTypeGranularity = sections => Math.max(...sections.map(section => SECTION_TYPE_GRANULARITY[section.type] ?? 1));\nconst roundDate = (utils, granularity, date) => {\n  if (granularity === SECTION_TYPE_GRANULARITY.year) {\n    return utils.startOfYear(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.month) {\n    return utils.startOfMonth(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.day) {\n    return utils.startOfDay(date);\n  }\n\n  // We don't have startOfHour / startOfMinute / startOfSecond\n  let roundedDate = date;\n  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {\n    roundedDate = utils.setMinutes(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {\n    roundedDate = utils.setSeconds(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {\n    roundedDate = utils.setMilliseconds(roundedDate, 0);\n  }\n  return roundedDate;\n};\nexport const getDefaultReferenceDate = ({\n  props,\n  utils,\n  granularity,\n  timezone,\n  getTodayDate: inGetTodayDate\n}) => {\n  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(utils, granularity, getTodayDate(utils, timezone));\n  if (props.minDate != null && utils.isAfterDay(props.minDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.minDate);\n  }\n  if (props.maxDate != null && utils.isBeforeDay(props.maxDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.maxDate);\n  }\n  const isAfter = createIsAfterIgnoreDatePart(props.disableIgnoringDatePartForTimeValidation ?? false, utils);\n  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : mergeDateAndTime(utils, referenceDate, props.minTime));\n  }\n  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : mergeDateAndTime(utils, referenceDate, props.maxTime));\n  }\n  return referenceDate;\n};"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,iBAAiB;AAC7D,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,iBAAiB;AAChE,OAAO,MAAMC,wBAAwB,GAAG;EACtCC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,yBAAyB,GAAGC,QAAQ,IAAIC,IAAI,CAACC,GAAG,CAAC,GAAGF,QAAQ,CAACG,GAAG,CAACC,OAAO,IAAIb,wBAAwB,CAACa,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtI,MAAMC,SAAS,GAAGA,CAACC,KAAK,EAAEC,WAAW,EAAEC,IAAI,KAAK;EAC9C,IAAID,WAAW,KAAKjB,wBAAwB,CAACC,IAAI,EAAE;IACjD,OAAOe,KAAK,CAACG,WAAW,CAACD,IAAI,CAAC;EAChC;EACA,IAAID,WAAW,KAAKjB,wBAAwB,CAACE,KAAK,EAAE;IAClD,OAAOc,KAAK,CAACI,YAAY,CAACF,IAAI,CAAC;EACjC;EACA,IAAID,WAAW,KAAKjB,wBAAwB,CAACG,GAAG,EAAE;IAChD,OAAOa,KAAK,CAACK,UAAU,CAACH,IAAI,CAAC;EAC/B;;EAEA;EACA,IAAII,WAAW,GAAGJ,IAAI;EACtB,IAAID,WAAW,GAAGjB,wBAAwB,CAACK,OAAO,EAAE;IAClDiB,WAAW,GAAGN,KAAK,CAACO,UAAU,CAACD,WAAW,EAAE,CAAC,CAAC;EAChD;EACA,IAAIL,WAAW,GAAGjB,wBAAwB,CAACM,OAAO,EAAE;IAClDgB,WAAW,GAAGN,KAAK,CAACQ,UAAU,CAACF,WAAW,EAAE,CAAC,CAAC;EAChD;EACA,IAAIL,WAAW,GAAGjB,wBAAwB,CAACO,YAAY,EAAE;IACvDe,WAAW,GAAGN,KAAK,CAACS,eAAe,CAACH,WAAW,EAAE,CAAC,CAAC;EACrD;EACA,OAAOA,WAAW;AACpB,CAAC;AACD,OAAO,MAAMI,uBAAuB,GAAGA,CAAC;EACtCC,KAAK;EACLX,KAAK;EACLC,WAAW;EACXW,QAAQ;EACR7B,YAAY,EAAE8B;AAChB,CAAC,KAAK;EACJ,IAAIC,aAAa,GAAGD,cAAc,GAAGA,cAAc,CAAC,CAAC,GAAGd,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAElB,YAAY,CAACiB,KAAK,EAAEY,QAAQ,CAAC,CAAC;EACpH,IAAID,KAAK,CAACI,OAAO,IAAI,IAAI,IAAIf,KAAK,CAACgB,UAAU,CAACL,KAAK,CAACI,OAAO,EAAED,aAAa,CAAC,EAAE;IAC3EA,aAAa,GAAGf,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEU,KAAK,CAACI,OAAO,CAAC;EAC9D;EACA,IAAIJ,KAAK,CAACM,OAAO,IAAI,IAAI,IAAIjB,KAAK,CAACkB,WAAW,CAACP,KAAK,CAACM,OAAO,EAAEH,aAAa,CAAC,EAAE;IAC5EA,aAAa,GAAGf,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEU,KAAK,CAACM,OAAO,CAAC;EAC9D;EACA,MAAME,OAAO,GAAGtC,2BAA2B,CAAC8B,KAAK,CAACS,wCAAwC,IAAI,KAAK,EAAEpB,KAAK,CAAC;EAC3G,IAAIW,KAAK,CAACU,OAAO,IAAI,IAAI,IAAIF,OAAO,CAACR,KAAK,CAACU,OAAO,EAAEP,aAAa,CAAC,EAAE;IAClEA,aAAa,GAAGf,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEU,KAAK,CAACS,wCAAwC,GAAGT,KAAK,CAACU,OAAO,GAAGvC,gBAAgB,CAACkB,KAAK,EAAEc,aAAa,EAAEH,KAAK,CAACU,OAAO,CAAC,CAAC;EACvK;EACA,IAAIV,KAAK,CAACW,OAAO,IAAI,IAAI,IAAIH,OAAO,CAACL,aAAa,EAAEH,KAAK,CAACW,OAAO,CAAC,EAAE;IAClER,aAAa,GAAGf,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEU,KAAK,CAACS,wCAAwC,GAAGT,KAAK,CAACW,OAAO,GAAGxC,gBAAgB,CAACkB,KAAK,EAAEc,aAAa,EAAEH,KAAK,CAACW,OAAO,CAAC,CAAC;EACvK;EACA,OAAOR,aAAa;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}