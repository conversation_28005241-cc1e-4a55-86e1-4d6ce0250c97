{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    past: \"{{count}} წამზე ნაკლები ხნის წინ\",\n    present: \"{{count}} წამზე ნაკლები\",\n    future: \"{{count}} წამზე ნაკლებში\"\n  },\n  xSeconds: {\n    past: \"{{count}} წამის წინ\",\n    present: \"{{count}} წამი\",\n    future: \"{{count}} წამში\"\n  },\n  halfAMinute: {\n    past: \"ნახევარი წუთის წინ\",\n    present: \"ნახევარი წუთი\",\n    future: \"ნახევარი წუთში\"\n  },\n  lessThanXMinutes: {\n    past: \"{{count}} წუთზე ნაკლები ხნის წინ\",\n    present: \"{{count}} წუთზე ნაკლები\",\n    future: \"{{count}} წუთზე ნაკლებში\"\n  },\n  xMinutes: {\n    past: \"{{count}} წუთის წინ\",\n    present: \"{{count}} წუთი\",\n    future: \"{{count}} წუთში\"\n  },\n  aboutXHours: {\n    past: \"დაახლოებით {{count}} საათის წინ\",\n    present: \"დაახლოებით {{count}} საათი\",\n    future: \"დაახლოებით {{count}} საათში\"\n  },\n  xHours: {\n    past: \"{{count}} საათის წინ\",\n    present: \"{{count}} საათი\",\n    future: \"{{count}} საათში\"\n  },\n  xDays: {\n    past: \"{{count}} დღის წინ\",\n    present: \"{{count}} დღე\",\n    future: \"{{count}} დღეში\"\n  },\n  aboutXWeeks: {\n    past: \"დაახლოებით {{count}} კვირას წინ\",\n    present: \"დაახლოებით {{count}} კვირა\",\n    future: \"დაახლოებით {{count}} კვირაში\"\n  },\n  xWeeks: {\n    past: \"{{count}} კვირას კვირა\",\n    present: \"{{count}} კვირა\",\n    future: \"{{count}} კვირაში\"\n  },\n  aboutXMonths: {\n    past: \"დაახლოებით {{count}} თვის წინ\",\n    present: \"დაახლოებით {{count}} თვე\",\n    future: \"დაახლოებით {{count}} თვეში\"\n  },\n  xMonths: {\n    past: \"{{count}} თვის წინ\",\n    present: \"{{count}} თვე\",\n    future: \"{{count}} თვეში\"\n  },\n  aboutXYears: {\n    past: \"დაახლოებით {{count}} წლის წინ\",\n    present: \"დაახლოებით {{count}} წელი\",\n    future: \"დაახლოებით {{count}} წელში\"\n  },\n  xYears: {\n    past: \"{{count}} წლის წინ\",\n    present: \"{{count}} წელი\",\n    future: \"{{count}} წელში\"\n  },\n  overXYears: {\n    past: \"{{count}} წელზე მეტი ხნის წინ\",\n    present: \"{{count}} წელზე მეტი\",\n    future: \"{{count}} წელზე მეტი ხნის შემდეგ\"\n  },\n  almostXYears: {\n    past: \"თითქმის {{count}} წლის წინ\",\n    present: \"თითქმის {{count}} წელი\",\n    future: \"თითქმის {{count}} წელში\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (options?.addSuffix && options.comparison && options.comparison > 0) {\n    result = tokenValue.future.replace(\"{{count}}\", String(count));\n  } else if (options?.addSuffix) {\n    result = tokenValue.past.replace(\"{{count}}\", String(count));\n  } else {\n    result = tokenValue.present.replace(\"{{count}}\", String(count));\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "past", "present", "future", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "comparison", "replace", "String"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ka/_lib/formatDistance.mjs"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    past: \"{{count}} წამზე ნაკლები ხნის წინ\",\n    present: \"{{count}} წამზე ნაკლები\",\n    future: \"{{count}} წამზე ნაკლებში\",\n  },\n\n  xSeconds: {\n    past: \"{{count}} წამის წინ\",\n    present: \"{{count}} წამი\",\n    future: \"{{count}} წამში\",\n  },\n\n  halfAMinute: {\n    past: \"ნახევარი წუთის წინ\",\n    present: \"ნახევარი წუთი\",\n    future: \"ნახევარი წუთში\",\n  },\n\n  lessThanXMinutes: {\n    past: \"{{count}} წუთზე ნაკლები ხნის წინ\",\n    present: \"{{count}} წუთზე ნაკლები\",\n    future: \"{{count}} წუთზე ნაკლებში\",\n  },\n\n  xMinutes: {\n    past: \"{{count}} წუთის წინ\",\n    present: \"{{count}} წუთი\",\n    future: \"{{count}} წუთში\",\n  },\n\n  aboutXHours: {\n    past: \"დაახლოებით {{count}} საათის წინ\",\n    present: \"დაახლოებით {{count}} საათი\",\n    future: \"დაახლოებით {{count}} საათში\",\n  },\n\n  xHours: {\n    past: \"{{count}} საათის წინ\",\n    present: \"{{count}} საათი\",\n    future: \"{{count}} საათში\",\n  },\n\n  xDays: {\n    past: \"{{count}} დღის წინ\",\n    present: \"{{count}} დღე\",\n    future: \"{{count}} დღეში\",\n  },\n\n  aboutXWeeks: {\n    past: \"დაახლოებით {{count}} კვირას წინ\",\n    present: \"დაახლოებით {{count}} კვირა\",\n    future: \"დაახლოებით {{count}} კვირაში\",\n  },\n\n  xWeeks: {\n    past: \"{{count}} კვირას კვირა\",\n    present: \"{{count}} კვირა\",\n    future: \"{{count}} კვირაში\",\n  },\n\n  aboutXMonths: {\n    past: \"დაახლოებით {{count}} თვის წინ\",\n    present: \"დაახლოებით {{count}} თვე\",\n    future: \"დაახლოებით {{count}} თვეში\",\n  },\n\n  xMonths: {\n    past: \"{{count}} თვის წინ\",\n    present: \"{{count}} თვე\",\n    future: \"{{count}} თვეში\",\n  },\n\n  aboutXYears: {\n    past: \"დაახლოებით {{count}} წლის წინ\",\n    present: \"დაახლოებით {{count}} წელი\",\n    future: \"დაახლოებით {{count}} წელში\",\n  },\n\n  xYears: {\n    past: \"{{count}} წლის წინ\",\n    present: \"{{count}} წელი\",\n    future: \"{{count}} წელში\",\n  },\n\n  overXYears: {\n    past: \"{{count}} წელზე მეტი ხნის წინ\",\n    present: \"{{count}} წელზე მეტი\",\n    future: \"{{count}} წელზე მეტი ხნის შემდეგ\",\n  },\n\n  almostXYears: {\n    past: \"თითქმის {{count}} წლის წინ\",\n    present: \"თითქმის {{count}} წელი\",\n    future: \"თითქმის {{count}} წელში\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (\n    options?.addSuffix &&\n    options.comparison &&\n    options.comparison > 0\n  ) {\n    result = tokenValue.future.replace(\"{{count}}\", String(count));\n  } else if (options?.addSuffix) {\n    result = tokenValue.past.replace(\"{{count}}\", String(count));\n  } else {\n    result = tokenValue.present.replace(\"{{count}}\", String(count));\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,IAAI,EAAE,kCAAkC;IACxCC,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE;EACV,CAAC;EAEDC,QAAQ,EAAE;IACRH,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE;EACV,CAAC;EAEDE,WAAW,EAAE;IACXJ,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE;EACV,CAAC;EAEDG,gBAAgB,EAAE;IAChBL,IAAI,EAAE,kCAAkC;IACxCC,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE;EACV,CAAC;EAEDI,QAAQ,EAAE;IACRN,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE;EACV,CAAC;EAEDK,WAAW,EAAE;IACXP,IAAI,EAAE,iCAAiC;IACvCC,OAAO,EAAE,4BAA4B;IACrCC,MAAM,EAAE;EACV,CAAC;EAEDM,MAAM,EAAE;IACNR,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,EAAE,iBAAiB;IAC1BC,MAAM,EAAE;EACV,CAAC;EAEDO,KAAK,EAAE;IACLT,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE;EACV,CAAC;EAEDQ,WAAW,EAAE;IACXV,IAAI,EAAE,iCAAiC;IACvCC,OAAO,EAAE,4BAA4B;IACrCC,MAAM,EAAE;EACV,CAAC;EAEDS,MAAM,EAAE;IACNX,IAAI,EAAE,wBAAwB;IAC9BC,OAAO,EAAE,iBAAiB;IAC1BC,MAAM,EAAE;EACV,CAAC;EAEDU,YAAY,EAAE;IACZZ,IAAI,EAAE,+BAA+B;IACrCC,OAAO,EAAE,0BAA0B;IACnCC,MAAM,EAAE;EACV,CAAC;EAEDW,OAAO,EAAE;IACPb,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE;EACV,CAAC;EAEDY,WAAW,EAAE;IACXd,IAAI,EAAE,+BAA+B;IACrCC,OAAO,EAAE,2BAA2B;IACpCC,MAAM,EAAE;EACV,CAAC;EAEDa,MAAM,EAAE;IACNf,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE;EACV,CAAC;EAEDc,UAAU,EAAE;IACVhB,IAAI,EAAE,+BAA+B;IACrCC,OAAO,EAAE,sBAAsB;IAC/BC,MAAM,EAAE;EACV,CAAC;EAEDe,YAAY,EAAE;IACZjB,IAAI,EAAE,4BAA4B;IAClCC,OAAO,EAAE,wBAAwB;IACjCC,MAAM,EAAE;EACV;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IACLF,OAAO,EAAEG,SAAS,IAClBH,OAAO,CAACI,UAAU,IAClBJ,OAAO,CAACI,UAAU,GAAG,CAAC,EACtB;IACAH,MAAM,GAAGC,UAAU,CAACrB,MAAM,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAChE,CAAC,MAAM,IAAIC,OAAO,EAAEG,SAAS,EAAE;IAC7BF,MAAM,GAAGC,UAAU,CAACvB,IAAI,CAAC0B,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAC9D,CAAC,MAAM;IACLE,MAAM,GAAGC,UAAU,CAACtB,OAAO,CAACyB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EACjE;EAEA,OAAOE,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}