{"ast": null, "code": "import { formatDistance } from \"./af/_lib/formatDistance.js\";\nimport { formatLong } from \"./af/_lib/formatLong.js\";\nimport { formatRelative } from \"./af/_lib/formatRelative.js\";\nimport { localize } from \"./af/_lib/localize.js\";\nimport { match } from \"./af/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Afrikaans locale.\n * @language Afrikaans\n * @iso-639-2 afr\n * <AUTHOR> Weststrate [@marnusw](https://github.com/marnusw)\n */\nexport const af = {\n  code: \"af\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default af;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "af", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/af.js"], "sourcesContent": ["import { formatDistance } from \"./af/_lib/formatDistance.js\";\nimport { formatLong } from \"./af/_lib/formatLong.js\";\nimport { formatRelative } from \"./af/_lib/formatRelative.js\";\nimport { localize } from \"./af/_lib/localize.js\";\nimport { match } from \"./af/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Afrikaans locale.\n * @language Afrikaans\n * @iso-639-2 afr\n * <AUTHOR> Weststrate [@marnusw](https://github.com/marnusw)\n */\nexport const af = {\n  code: \"af\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default af;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}